const axios = require('axios');

async function testPagination() {
    console.log('🔍 اختبار Pagination للمنتجات\n');
    
    const baseUrl = 'https://dalilakauto.com/api/v1/ecommerce/products';
    
    try {
        // اختبار الصفحة الأولى بدون معاملات
        console.log('📋 اختبار الصفحة الأولى (افتراضي):');
        const response1 = await axios.get(baseUrl);
        console.log(`عدد المنتجات: ${response1.data.data?.length || 0}`);
        
        if (response1.data.meta) {
            console.log(`إجمالي المنتجات: ${response1.data.meta.total}`);
            console.log(`الصفحة الحالية: ${response1.data.meta.current_page}`);
            console.log(`آخر صفحة: ${response1.data.meta.last_page}`);
            console.log(`منتجات في الصفحة: ${response1.data.meta.per_page}`);
        }
        
        console.log('\n📋 اختبار مع per_page=100:');
        const response2 = await axios.get(`${baseUrl}?per_page=100`);
        console.log(`عدد المنتجات: ${response2.data.data?.length || 0}`);
        
        if (response2.data.meta) {
            console.log(`إجمالي المنتجات: ${response2.data.meta.total}`);
            console.log(`الصفحة الحالية: ${response2.data.meta.current_page}`);
            console.log(`آخر صفحة: ${response2.data.meta.last_page}`);
            console.log(`منتجات في الصفحة: ${response2.data.meta.per_page}`);
        }
        
        console.log('\n📋 اختبار الصفحة الثانية:');
        const response3 = await axios.get(`${baseUrl}?page=2`);
        console.log(`عدد المنتجات: ${response3.data.data?.length || 0}`);
        
        console.log('\n📋 اختبار مع limit كبير:');
        const response4 = await axios.get(`${baseUrl}?limit=1000`);
        console.log(`عدد المنتجات: ${response4.data.data?.length || 0}`);
        
    } catch (error) {
        console.error('❌ خطأ:', error.message);
        if (error.response?.data) {
            console.log('تفاصيل الخطأ:', JSON.stringify(error.response.data, null, 2));
        }
    }
}

testPagination();
