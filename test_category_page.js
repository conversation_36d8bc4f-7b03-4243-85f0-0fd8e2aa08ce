/**
 * اختبار صفحة جميع المنتجات
 */

// محاكاة الضغط على زر "مشاهدة المزيد"
function testShowAllProducts() {
    console.log('🧪 اختبار صفحة جميع المنتجات...');
    
    // فتح console في المتصفح وتشغيل هذا الكود:
    console.log(`
    // اختبار 1: التحقق من وجود العناصر
    console.log('Dashboard:', document.getElementById('dashboard'));
    console.log('Category Page:', document.getElementById('categoryPage'));
    console.log('Main Content:', document.getElementById('mainContent'));
    
    // اختبار 2: محاكاة الضغط على زر "مشاهدة المزيد"
    showAllProducts();
    
    // اختبار 3: التحقق من حالة الصفحات بعد 2 ثانية
    setTimeout(() => {
        console.log('=== حالة الصفحات بعد التحميل ===');
        console.log('Dashboard display:', document.getElementById('dashboard')?.style.display);
        console.log('Category Page display:', document.getElementById('categoryPage')?.style.display);
        console.log('Main Content display:', document.getElementById('mainContent')?.style.display);
        
        const categoryGrid = document.getElementById('categoryProductsGrid');
        console.log('Products Grid:', categoryGrid);
        console.log('Products Grid Content:', categoryGrid?.innerHTML.substring(0, 200) + '...');
    }, 2000);
    `);
}

// تشغيل الاختبار
testShowAllProducts();
