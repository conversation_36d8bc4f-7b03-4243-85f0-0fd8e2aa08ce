{"name": "dalila-car-pwa", "version": "1.0.0", "description": "دليل كار - ت<PERSON><PERSON><PERSON>ق قطع غيار السيارات PWA", "main": "web-preview.html", "scripts": {"start": "npx http-server . -p 8000 -c-1", "build": "echo 'PWA Build completed'", "build:apk": "node build-apk.js", "android": "npx cap run android", "android:build": "npx cap build android", "ios": "npx cap run ios", "sync": "npx cap sync", "serve": "npx http-server . -p 8000 -c-1 --cors", "dev": "npx http-server . -p 3000 -c-1 --cors", "install:capacitor": "npm install @capacitor/core @capacitor/cli @capacitor/android @capacitor/ios", "init:capacitor": "npx cap init", "add:android": "npx cap add android", "add:ios": "npx cap add ios", "open:android": "npx cap open android", "open:ios": "npx cap open ios"}, "keywords": ["auto-parts", "car-parts", "automotive", "ecommerce", "mobile-app", "pwa", "arabic", "dalila", "قطع-غيار", "سيارات"], "author": {"name": "Dalila Car Auto Parts", "email": "<EMAIL>", "url": "https://dalilakauto.com"}, "license": "MIT", "homepage": "https://dalilakauto.com", "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "dependencies": {"@capacitor/android": "^5.7.8", "@capacitor/app": "^5.0.6", "@capacitor/browser": "^5.1.0", "@capacitor/camera": "^5.0.6", "@capacitor/core": "^5.7.8", "@capacitor/device": "^5.0.6", "@capacitor/filesystem": "^5.1.4", "@capacitor/geolocation": "^5.0.6", "@capacitor/haptics": "^5.0.6", "@capacitor/ios": "^5.7.0", "@capacitor/keyboard": "^5.0.6", "@capacitor/network": "^5.0.6", "@capacitor/share": "^5.0.6", "@capacitor/splash-screen": "^5.0.6", "@capacitor/status-bar": "^5.0.6", "@capacitor/toast": "^5.0.6", "axios": "^1.6.0"}, "devDependencies": {"@capacitor/cli": "^5.7.8", "http-server": "^14.1.1", "typescript": "^5.8.3"}, "capacitor": {"appId": "com.dalilakauto.app", "appName": "دليل كار", "webDir": ".", "bundledWebRuntime": false}}