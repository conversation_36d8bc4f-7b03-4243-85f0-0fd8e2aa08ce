/**
 * تحميل جميع المنتجات من APIs السيرفر فقط
 * Load All Products from Server APIs Only
 *
 * ⚠️ هذا الملف يحمل المنتجات من APIs السيرفر مباشرة
 * ❌ لا يستخدم قاعدة البيانات المحلية نهائياً
 * 🌐 جميع البيانات من: https://dalilakauto.com/api/v1
 */
(function() {
    'use strict';
    
    console.log('🔧 تحميل حل مشكلة المنتجات...');
    
    // إعدادات التحميل
    const LOAD_CONFIG = {
        API_BASE: 'https://dalilakauto.com',
        MAX_PAGES: 100,
        PER_PAGE: 100,
        DELAY: 200
    };
    
    let allProductsData = [];
    let isLoadingAll = false;
    
    /**
     * تحميل جميع المنتجات من جميع الصفحات
     */
    async function loadAllProductsFromAllPages() {
        if (isLoadingAll) {
            console.log('⏳ التحميل قيد التقدم...');
            return allProductsData;
        }
        
        isLoadingAll = true;
        console.log('🚀 بدء تحميل جميع المنتجات من جميع الصفحات...');
        
        // إظهار مؤشر التحميل
        showGlobalLoadingIndicator('جاري تحميل جميع المنتجات...');
        
        try {
            allProductsData = [];
            let page = 1;
            let hasMore = true;
            let totalLoaded = 0;
            
            while (hasMore && page <= LOAD_CONFIG.MAX_PAGES) {
                console.log(`📄 تحميل الصفحة ${page}...`);
                
                // تحديث مؤشر التحميل
                updateGlobalLoadingIndicator(`تحميل الصفحة ${page}... (${totalLoaded} منتج)`);
                
                try {
                    // استخدام APIs فقط (ليس قاعدة البيانات المحلية)
                    const endpoints = [
                        `/api/v1/ecommerce/products?page=${page}&per_page=${LOAD_CONFIG.PER_PAGE}`,
                        `/ecommerce/products?page=${page}&per_page=${LOAD_CONFIG.PER_PAGE}`,
                        `/api/v1/products?page=${page}&per_page=${LOAD_CONFIG.PER_PAGE}`
                    ];
                    
                    let data = null;
                    let products = [];
                    
                    for (const endpoint of endpoints) {
                        try {
                            const url = LOAD_CONFIG.API_BASE + endpoint;
                            console.log(`🔗 محاولة: ${url}`);
                            
                            const response = await fetch(url);
                            if (response.ok) {
                                data = await response.json();
                                
                                if (data.error === false && data.data) {
                                    products = data.data.data || data.data;
                                    if (Array.isArray(products) && products.length > 0) {
                                        console.log(`✅ نجح endpoint: ${endpoint} - ${products.length} منتج`);
                                        break;
                                    }
                                }
                            }
                        } catch (endpointError) {
                            console.log(`❌ فشل endpoint: ${endpoint}`, endpointError.message);
                        }
                    }
                    
                    if (products.length > 0) {
                        allProductsData.push(...products);
                        totalLoaded = allProductsData.length;
                        
                        console.log(`✅ صفحة ${page}: ${products.length} منتج (إجمالي: ${totalLoaded})`);
                        
                        // فحص إذا كان هناك صفحات أخرى
                        if (data && data.data && data.data.current_page && data.data.last_page) {
                            hasMore = data.data.current_page < data.data.last_page;
                            console.log(`📊 الصفحة ${data.data.current_page} من ${data.data.last_page}`);
                        } else if (products.length < LOAD_CONFIG.PER_PAGE) {
                            hasMore = false;
                            console.log('📊 آخر صفحة (منتجات أقل من المطلوب)');
                        }
                        
                        page++;
                        
                        // تأخير بين الطلبات
                        if (hasMore) {
                            await new Promise(resolve => setTimeout(resolve, LOAD_CONFIG.DELAY));
                        }
                    } else {
                        console.log(`❌ لا توجد منتجات في الصفحة ${page}`);
                        hasMore = false;
                    }
                    
                } catch (pageError) {
                    console.error(`❌ خطأ في الصفحة ${page}:`, pageError);
                    page++;
                    if (page > page + 3) hasMore = false; // توقف بعد 3 صفحات فارغة
                }
            }
            
            console.log(`🎉 انتهى التحميل! إجمالي المنتجات: ${allProductsData.length}`);
            
            // تحديث الواجهة
            updateProductCountInUI(allProductsData.length);
            
            // إخفاء مؤشر التحميل
            hideGlobalLoadingIndicator();
            
            // حفظ في التخزين المحلي
            try {
                localStorage.setItem('dalilk_all_products', JSON.stringify({
                    products: allProductsData,
                    timestamp: Date.now(),
                    count: allProductsData.length
                }));
                console.log('💾 تم حفظ المنتجات في التخزين المحلي');
            } catch (storageError) {
                console.warn('⚠️ فشل في حفظ المنتجات في التخزين المحلي:', storageError);
            }
            
            return allProductsData;
            
        } catch (error) {
            console.error('❌ فشل في تحميل المنتجات:', error);
            hideGlobalLoadingIndicator();
            showErrorNotification('فشل في تحميل المنتجات. يرجى إعادة المحاولة.');
            return [];
        } finally {
            isLoadingAll = false;
        }
    }
    
    /**
     * إظهار مؤشر التحميل العام
     */
    function showGlobalLoadingIndicator(message) {
        let indicator = document.getElementById('globalLoadingIndicator');
        if (!indicator) {
            indicator = document.createElement('div');
            indicator.id = 'globalLoadingIndicator';
            indicator.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.8);
                color: white;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                z-index: 99999;
                font-family: Arial, sans-serif;
                direction: rtl;
            `;
            document.body.appendChild(indicator);
        }
        
        indicator.innerHTML = `
            <div style="text-align: center;">
                <div style="width: 60px; height: 60px; border: 6px solid #f3f3f3; border-top: 6px solid #667eea; border-radius: 50%; animation: spin 1s linear infinite; margin: 0 auto 20px;"></div>
                <div style="font-size: 18px; margin-bottom: 10px;">${message}</div>
                <div style="font-size: 14px; opacity: 0.8;">يرجى الانتظار...</div>
            </div>
        `;
        indicator.style.display = 'flex';
        
        // إضافة CSS للدوران إذا لم يكن موجوداً
        if (!document.getElementById('spinAnimation')) {
            const style = document.createElement('style');
            style.id = 'spinAnimation';
            style.textContent = '@keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }';
            document.head.appendChild(style);
        }
    }
    
    /**
     * تحديث مؤشر التحميل العام
     */
    function updateGlobalLoadingIndicator(message) {
        const indicator = document.getElementById('globalLoadingIndicator');
        if (indicator) {
            const messageDiv = indicator.querySelector('div:nth-child(2)');
            if (messageDiv) {
                messageDiv.textContent = message;
            }
        }
    }
    
    /**
     * إخفاء مؤشر التحميل العام
     */
    function hideGlobalLoadingIndicator() {
        const indicator = document.getElementById('globalLoadingIndicator');
        if (indicator) {
            indicator.style.display = 'none';
        }
    }
    
    /**
     * عرض إشعار خطأ
     */
    function showErrorNotification(message) {
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #ff4757;
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            z-index: 100000;
            font-family: Arial, sans-serif;
            direction: rtl;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        `;
        notification.textContent = message;
        document.body.appendChild(notification);
        
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 5000);
    }
    
    /**
     * تحديث عدد المنتجات في الواجهة
     */
    function updateProductCountInUI(count) {
        // تحديث عنصر إجمالي المنتجات
        const selectors = [
            '#totalProducts',
            '.total-products',
            '.stat-number',
            '[data-products-count]'
        ];
        
        selectors.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            elements.forEach(el => {
                if (el.id === 'totalProducts' || el.classList.contains('total-products')) {
                    el.textContent = count.toLocaleString();
                }
            });
        });
        
        // تحديث العنوان
        document.title = `دليل - ${count.toLocaleString()} منتج`;
        
        console.log(`📊 تم تحديث عدد المنتجات في الواجهة: ${count}`);
    }
    
    /**
     * تحميل المنتجات من التخزين المحلي
     */
    function loadProductsFromCache() {
        try {
            const cached = localStorage.getItem('dalilk_all_products');
            if (cached) {
                const data = JSON.parse(cached);
                const age = Date.now() - data.timestamp;
                const maxAge = 10 * 60 * 1000; // 10 دقائق
                
                if (age < maxAge && data.products && data.products.length > 0) {
                    console.log(`📦 تم تحميل ${data.products.length} منتج من التخزين المحلي`);
                    allProductsData = data.products;
                    updateProductCountInUI(data.products.length);
                    return data.products;
                }
            }
        } catch (error) {
            console.warn('⚠️ فشل في تحميل المنتجات من التخزين المحلي:', error);
        }
        return null;
    }
    
    /**
     * تهيئة النظام
     */
    function initialize() {
        console.log('🔧 تهيئة نظام تحميل جميع المنتجات...');
        
        // تحميل من APIs مباشرة (تعطيل التخزين المحلي)
        console.log('🔄 تحميل المنتجات من APIs السيرفر مباشرة...');
        console.log('🚫 تم تعطيل قاعدة البيانات المحلية نهائياً');

        // مسح التخزين المحلي للمنتجات لضمان التحميل الجديد
        localStorage.removeItem('dalilk_all_products');
        localStorage.removeItem('products_cache');
        localStorage.removeItem('categories_cache');

        // تحميل جميع المنتجات فوراً من السيرفر
        setTimeout(loadAllProductsFromAllPages, 1000);
        
        // إضافة زر لإعادة التحميل
        addReloadButton();
    }
    
    /**
     * إضافة زر لإعادة تحميل جميع المنتجات
     */
    function addReloadButton() {
        const button = document.createElement('button');
        button.textContent = `تحميل جميع المنتجات (${allProductsData.length})`;
        button.style.cssText = `
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 25px;
            font-size: 14px;
            cursor: pointer;
            z-index: 1000;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            font-family: Arial, sans-serif;
            direction: rtl;
        `;
        
        button.onclick = async () => {
            button.disabled = true;
            button.textContent = 'جاري التحميل...';
            
            await loadAllProductsFromAllPages();
            
            button.disabled = false;
            button.textContent = `تحميل جميع المنتجات (${allProductsData.length})`;
        };
        
        document.body.appendChild(button);
    }
    
    // تصدير الوظائف للاستخدام الخارجي
    window.DalikProductLoader = {
        loadAll: loadAllProductsFromAllPages,
        getAll: () => allProductsData,
        getCount: () => allProductsData.length,
        clearCache: () => {
            localStorage.removeItem('dalilk_all_products');
            allProductsData = [];
            console.log('🗑️ تم مسح التخزين المؤقت');
        }
    };
    
    // بدء التهيئة
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initialize);
    } else {
        initialize();
    }
    
    console.log('✅ تم تحميل حل مشكلة المنتجات');
    
})();
