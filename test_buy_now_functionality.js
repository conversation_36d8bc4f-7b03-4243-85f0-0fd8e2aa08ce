/**
 * اختبار وظيفة "اشتري الآن"
 */

console.log('🛒 اختبار وظيفة "اشتري الآن"...');

// محاكاة تسجيل دخول المستخدم للاختبار
function simulateUserLogin() {
    window.user = {
        id: 1,
        name: 'مستخدم تجريبي',
        email: '<EMAIL>'
    };
    console.log('✅ تم محاكاة تسجيل دخول المستخدم');
}

// اختبار وظيفة buyNow الرئيسية
function testBuyNowFunction() {
    console.log('\n🧪 اختبار وظيفة buyNow...');
    
    // التحقق من وجود الوظيفة
    if (typeof buyNow !== 'function') {
        console.log('❌ وظيفة buyNow غير موجودة');
        return;
    }
    
    console.log('✅ وظيفة buyNow موجودة');
    
    // محاكاة منتج تجريبي
    const testProduct = {
        id: 999,
        name: 'منتج تجريبي للاختبار',
        price: 25000,
        image_url: 'https://via.placeholder.com/150',
        is_out_of_stock: false
    };
    
    // إضافة المنتج لمصادر البيانات
    if (!window.allProductsData) window.allProductsData = [];
    window.allProductsData.push(testProduct);
    
    // محاكاة تسجيل الدخول
    simulateUserLogin();
    
    // اختبار الوظيفة
    try {
        console.log('محاولة تشغيل buyNow(999)...');
        buyNow(999);
        console.log('✅ تم تشغيل buyNow بنجاح');
    } catch (error) {
        console.error('❌ خطأ في buyNow:', error);
    }
}

// اختبار وظيفة buyNowFromCard
function testBuyNowFromCardFunction() {
    console.log('\n🧪 اختبار وظيفة buyNowFromCard...');
    
    // التحقق من وجود الوظيفة
    if (typeof buyNowFromCard !== 'function') {
        console.log('❌ وظيفة buyNowFromCard غير موجودة');
        return;
    }
    
    console.log('✅ وظيفة buyNowFromCard موجودة');
    
    // محاكاة تسجيل الدخول
    simulateUserLogin();
    
    // اختبار الوظيفة
    try {
        console.log('محاولة تشغيل buyNowFromCard(999)...');
        buyNowFromCard(999);
        console.log('✅ تم تشغيل buyNowFromCard بنجاح');
    } catch (error) {
        console.error('❌ خطأ في buyNowFromCard:', error);
    }
}

// اختبار وظائف السلة المطلوبة
function testCartFunctions() {
    console.log('\n🛒 اختبار وظائف السلة...');
    
    const requiredFunctions = [
        'addToCart',
        'updateCartBadge',
        'proceedToCheckout',
        'getCartTotal'
    ];
    
    requiredFunctions.forEach(funcName => {
        if (typeof window[funcName] === 'function') {
            console.log(`✅ ${funcName} موجودة`);
        } else {
            console.log(`❌ ${funcName} غير موجودة`);
        }
    });
    
    // فحص متغير السلة
    if (Array.isArray(window.cart)) {
        console.log(`✅ متغير cart موجود (${window.cart.length} عنصر)`);
    } else {
        console.log('❌ متغير cart غير موجود أو غير صحيح');
    }
}

// اختبار أزرار "اشتري الآن" في الصفحة
function testBuyNowButtons() {
    console.log('\n🔘 فحص أزرار "اشتري الآن" في الصفحة...');
    
    const buyNowButtons = document.querySelectorAll('[onclick*="buyNow"]');
    console.log(`عدد أزرار "اشتري الآن": ${buyNowButtons.length}`);
    
    buyNowButtons.forEach((button, index) => {
        console.log(`الزر ${index + 1}:`, {
            text: button.textContent.trim(),
            onclick: button.getAttribute('onclick'),
            visible: button.offsetParent !== null
        });
    });
}

// اختبار محاكاة كاملة
function simulateCompleteBuyNowFlow() {
    console.log('\n🎯 محاكاة تدفق "اشتري الآن" كامل...');
    
    // تنظيف السلة
    window.cart = [];
    
    // محاكاة تسجيل الدخول
    simulateUserLogin();
    
    // محاكاة منتج
    const testProduct = {
        id: 888,
        name: 'منتج اختبار التدفق الكامل',
        price: 15000,
        image_url: 'https://via.placeholder.com/150',
        is_out_of_stock: false
    };
    
    if (!window.allProductsData) window.allProductsData = [];
    window.allProductsData.push(testProduct);
    
    console.log('1. السلة قبل الشراء:', window.cart.length, 'عنصر');
    
    // تشغيل buyNow
    try {
        buyNow(888);
        
        setTimeout(() => {
            console.log('2. السلة بعد الشراء:', window.cart.length, 'عنصر');
            
            if (window.cart.length > 0) {
                console.log('✅ تم إضافة المنتج للسلة بنجاح');
                console.log('المنتج المضاف:', window.cart[window.cart.length - 1]);
            } else {
                console.log('❌ لم يتم إضافة المنتج للسلة');
            }
        }, 1000);
        
    } catch (error) {
        console.error('❌ خطأ في التدفق الكامل:', error);
    }
}

// تشغيل جميع الاختبارات
function runAllBuyNowTests() {
    console.log('🚀 بدء جميع اختبارات "اشتري الآن"...');
    
    testBuyNowFunction();
    testBuyNowFromCardFunction();
    testCartFunctions();
    testBuyNowButtons();
    simulateCompleteBuyNowFlow();
    
    console.log('\n✅ انتهت جميع الاختبارات');
}

// تشغيل الاختبارات
runAllBuyNowTests();

console.log('\n📋 الاختبارات المتاحة:');
console.log('- testBuyNowFunction() - اختبار وظيفة buyNow');
console.log('- testBuyNowFromCardFunction() - اختبار وظيفة buyNowFromCard');
console.log('- testCartFunctions() - اختبار وظائف السلة');
console.log('- testBuyNowButtons() - فحص أزرار "اشتري الآن"');
console.log('- simulateCompleteBuyNowFlow() - محاكاة تدفق كامل');
console.log('- runAllBuyNowTests() - تشغيل جميع الاختبارات');
