/**
 * تشخيص مشاكل الصفحات
 */

console.log('🔧 تشخيص مشاكل الصفحات...');

// وظيفة تشخيص شاملة
function debugPages() {
    console.log('\n=== تشخيص الصفحات ===');
    
    // فحص العناصر الأساسية
    console.log('📋 فحص العناصر الأساسية:');
    console.log('- Main Content:', document.getElementById('mainContent') ? '✅' : '❌');
    console.log('- Phone Container:', document.querySelector('.phone-container') ? '✅' : '❌');
    
    // فحص الصفحات الموجودة
    console.log('\n📄 الصفحات الموجودة:');
    const categoryPage = document.getElementById('categoryPage');
    const specificCategoryPage = document.getElementById('specificCategoryPage');
    
    console.log('- Category Page (All Products):', categoryPage ? '✅' : '❌');
    if (categoryPage) {
        console.log('  Display:', categoryPage.style.display);
        console.log('  Content:', categoryPage.innerHTML.length > 100 ? '✅ Has Content' : '❌ Empty');
    }
    
    console.log('- Specific Category Page:', specificCategoryPage ? '✅' : '❌');
    if (specificCategoryPage) {
        console.log('  Display:', specificCategoryPage.style.display);
        console.log('  Content:', specificCategoryPage.innerHTML.length > 100 ? '✅ Has Content' : '❌ Empty');
    }
    
    // فحص الوظائف
    console.log('\n🔧 فحص الوظائف:');
    console.log('- showAllProducts:', typeof showAllProducts === 'function' ? '✅' : '❌');
    console.log('- showCategoryProducts:', typeof showCategoryProducts === 'function' ? '✅' : '❌');
    console.log('- createAllProductsPage:', typeof createAllProductsPage === 'function' ? '✅' : '❌');
    console.log('- createCategoryPage:', typeof createCategoryPage === 'function' ? '✅' : '❌');
}

// وظيفة اختبار صفحة جميع المنتجات
function testAllProductsPage() {
    console.log('\n🧪 اختبار صفحة جميع المنتجات...');
    
    try {
        showAllProducts();
        
        setTimeout(() => {
            const categoryPage = document.getElementById('categoryPage');
            console.log('Category Page after showAllProducts:', categoryPage ? '✅' : '❌');
            if (categoryPage) {
                console.log('Display:', categoryPage.style.display);
                console.log('Grid Element:', document.getElementById('categoryProductsGrid') ? '✅' : '❌');
            }
        }, 1000);
        
    } catch (error) {
        console.error('❌ خطأ في اختبار صفحة جميع المنتجات:', error);
    }
}

// وظيفة اختبار صفحة أدوات الصدر
function testChestToolsPage() {
    console.log('\n🧪 اختبار صفحة أدوات الصدر...');
    
    try {
        showCategoryProducts('chest-tools');
        
        setTimeout(() => {
            const specificCategoryPage = document.getElementById('specificCategoryPage');
            console.log('Specific Category Page after showCategoryProducts:', specificCategoryPage ? '✅' : '❌');
            if (specificCategoryPage) {
                console.log('Display:', specificCategoryPage.style.display);
            }
        }, 1000);
        
    } catch (error) {
        console.error('❌ خطأ في اختبار صفحة أدوات الصدر:', error);
    }
}

// تشغيل التشخيص
debugPages();

console.log('\n📋 الاختبارات المتاحة:');
console.log('- debugPages() - تشخيص شامل');
console.log('- testAllProductsPage() - اختبار صفحة جميع المنتجات');
console.log('- testChestToolsPage() - اختبار صفحة أدوات الصدر');
