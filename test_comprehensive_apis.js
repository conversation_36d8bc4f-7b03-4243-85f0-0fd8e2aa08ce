// 🧪 اختبار شامل للـ APIs - مشروع دليل
const https = require('https');

const BASE_URL = 'https://dalilakauto.com/api/v1';

// دالة مساعدة لعمل HTTP requests
function makeRequest(url, options = {}) {
    return new Promise((resolve, reject) => {
        const req = https.request(url, {
            method: options.method || 'GET',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'User-Agent': 'Mozilla/5.0 (compatible; DalilaApp/1.0)',
                ...options.headers
            },
            timeout: 15000
        }, (res) => {
            let data = '';
            
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                try {
                    const parsedData = JSON.parse(data);
                    resolve({
                        status: res.statusCode,
                        data: parsedData,
                        headers: res.headers
                    });
                } catch (error) {
                    resolve({
                        status: res.statusCode,
                        data: data,
                        headers: res.headers
                    });
                }
            });
        });
        
        req.on('error', (error) => {
            reject(error);
        });
        
        req.on('timeout', () => {
            req.destroy();
            reject(new Error('Request timeout'));
        });
        
        if (options.body) {
            req.write(JSON.stringify(options.body));
        }
        
        req.end();
    });
}

// اختبار الاتصال الأساسي
async function testConnection() {
    console.log('🔗 اختبار الاتصال بالخادم...');
    console.log(`📡 Base URL: ${BASE_URL}`);
    console.log('='.repeat(50));

    try {
        // اختبار صفحة الصحة
        const response = await makeRequest('https://dalilakauto.com');
        console.log('✅ الخادم يعمل بنجاح - Status:', response.status);
        return true;
    } catch (error) {
        console.log('❌ فشل في الاتصال بالخادم:', error.message);
        return false;
    }
}

// اختبار APIs المختلفة
async function testAPIs() {
    const tests = [
        {
            name: 'Products API',
            endpoint: '/ecommerce/products',
            method: 'GET',
            params: '?page=1&per_page=5'
        },
        {
            name: 'Categories API',
            endpoint: '/ecommerce/product-categories',
            method: 'GET',
        },
        {
            name: 'Brands API',
            endpoint: '/ecommerce/brands',
            method: 'GET',
        },
        {
            name: 'Vehicle Parts - Root Categories',
            endpoint: '/vehicle-parts/root-categories',
            method: 'GET',
        },
        {
            name: 'Vehicle Parts - Makes',
            endpoint: '/vehicle-parts/makes',
            method: 'GET',
        },
        {
            name: 'Countries API',
            endpoint: '/location/countries',
            method: 'GET',
        },
        {
            name: 'Blog Posts API',
            endpoint: '/posts',
            method: 'GET',
            params: '?page=1&per_page=5'
        },
        {
            name: 'Simple Sliders API',
            endpoint: '/simple-sliders',
            method: 'GET',
        },
        {
            name: 'Flash Sales API',
            endpoint: '/ecommerce/flash-sales',
            method: 'GET',
        }
    ];

    console.log('\n🧪 اختبار APIs المختلفة:');
    console.log('='.repeat(50));

    const results = [];

    for (const test of tests) {
        try {
            console.log(`\n📋 اختبار: ${test.name}`);
            const fullEndpoint = test.endpoint + (test.params || '');
            console.log(`🔗 ${test.method} ${fullEndpoint}`);

            const response = await makeRequest(`${BASE_URL}${fullEndpoint}`, {
                method: test.method
            });

            if (response.status >= 200 && response.status < 300) {
                console.log(`✅ نجح - Status: ${response.status}`);

                let dataInfo = '';
                if (response.data) {
                    if (response.data.data && Array.isArray(response.data.data)) {
                        dataInfo = `${response.data.data.length} عنصر`;
                        if (response.data.data.length > 0) {
                            const sampleKeys = Object.keys(response.data.data[0]).slice(0, 3);
                            console.log(`📝 مثال على البيانات:`, sampleKeys.join(', '));
                        }
                    } else if (Array.isArray(response.data)) {
                        dataInfo = `${response.data.length} عنصر`;
                    } else {
                        dataInfo = `نوع: ${typeof response.data}`;
                    }
                    console.log(`📊 البيانات: ${dataInfo}`);
                }

                results.push({
                    name: test.name,
                    status: 'success',
                    code: response.status,
                    dataInfo
                });
            } else {
                console.log(`⚠️ استجابة غير متوقعة - Status: ${response.status}`);
                if (response.data) {
                    console.log(`📝 رسالة:`, JSON.stringify(response.data).substring(0, 100));
                }
                
                results.push({
                    name: test.name,
                    status: 'warning',
                    code: response.status,
                    message: 'استجابة غير متوقعة'
                });
            }
        } catch (error) {
            console.log(`❌ فشل - Network Error: ${error.message}`);
            results.push({
                name: test.name,
                status: 'error',
                message: error.message
            });
        }
        
        // انتظار قصير بين الطلبات
        await new Promise(resolve => setTimeout(resolve, 500));
    }

    return results;
}

// تشغيل جميع الاختبارات
async function runAllTests() {
    console.log('🚀 بدء اختبار APIs - مشروع دليل');
    console.log('⏰ ' + new Date().toLocaleString('ar-EG'));
    console.log('='.repeat(60));

    // اختبار الاتصال الأساسي
    const isConnected = await testConnection();

    if (!isConnected) {
        console.log('\n❌ فشل في الاتصال بالخادم. تأكد من الاتصال بالإنترنت والوصول إلى https://dalilakauto.com');
        return;
    }

    // اختبار APIs
    const results = await testAPIs();

    // تلخيص النتائج
    console.log('\n' + '='.repeat(60));
    console.log('📊 ملخص نتائج الاختبار:');
    console.log('='.repeat(60));

    const successful = results.filter(r => r.status === 'success').length;
    const warnings = results.filter(r => r.status === 'warning').length;
    const errors = results.filter(r => r.status === 'error').length;

    console.log(`✅ نجح: ${successful}`);
    console.log(`⚠️ تحذيرات: ${warnings}`);
    console.log(`❌ أخطاء: ${errors}`);

    if (errors > 0) {
        console.log('\n❌ APIs التي فشلت:');
        results.filter(r => r.status === 'error').forEach(r => {
            console.log(`  - ${r.name}: ${r.message}`);
        });
    }

    if (warnings > 0) {
        console.log('\n⚠️ APIs مع تحذيرات:');
        results.filter(r => r.status === 'warning').forEach(r => {
            console.log(`  - ${r.name}: Status ${r.code}`);
        });
    }

    console.log('\n✅ انتهى اختبار APIs');
    console.log('📝 ملاحظة: بعض APIs قد تتطلب مصادقة أو إعداد إضافي');
}

// تشغيل الاختبارات
runAllTests().catch(console.error);
