/* 🎨 دليل كار - الأنماط الأساسية المحسنة */

/* ===== إعادة تعيين الأساسيات ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    /* منع الزوم والتكبير */
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-touch-callout: none;
    -webkit-tap-highlight-color: transparent;
}

body {
    font-family: 'Arial', sans-serif;
    background: #f0f2f5;
    color: #333;
    direction: rtl;
    min-height: 100vh;
}

.phone-container {
    width: 100%;
    max-width: 100%;
    margin: 0;
    background: white;
    overflow: hidden;
    height: 100vh;
    display: flex;
    flex-direction: column;
}

/* ===== Responsive Design للشاشات المختلفة ===== */
@media (min-width: 768px) {
    .phone-container {
        max-width: 768px;
        margin: 0 auto;
        box-shadow: 0 0 20px rgba(0,0,0,0.1);
        border-radius: 12px;
    }
}

@media (min-width: 1024px) {
    .phone-container {
        max-width: 1024px;
        border-radius: 0;
        box-shadow: none;
    }
}

/* تأكد من أن الصفحات داخل الحاوي يمكن اسكرولها */
.phone-container .page {
    flex: 1;
    overflow-y: auto;
    height: 100vh;
    -webkit-overflow-scrolling: touch;
}

/* ===== Responsive Typography ===== */
@media (min-width: 768px) {
    h1 { font-size: 2.5rem; }
    h2 { font-size: 2rem; }
    h3 { font-size: 1.5rem; }
    .btn { padding: 12px 24px; font-size: 16px; }
    .product-card { margin: 10px; }
}

@media (min-width: 1024px) {
    h1 { font-size: 3rem; }
    h2 { font-size: 2.25rem; }
    .container { max-width: 1200px; margin: 0 auto; }
    .products-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 20px;
    }
}

.app-content {
    flex: 1;
    overflow-y: auto;
    position: relative;
    padding-bottom: 80px;
}

.screen {
    display: none;
    height: 100%;
    overflow-y: auto;
}

.screen.active {
    display: block;
}

.dashboard-header {
    display: none; /* إخفاء الـ header القديم */
    background: white;
    color: #333;
    padding: 20px;
    text-align: center;
    border-bottom: 1px solid #eee;
}

/* ===== Professional Navigation - مثل أمازون ===== */
.bottom-navigation {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: white;
    border-top: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-around;
    padding: 8px 0;
    z-index: 1000;
    box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
}

.nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 8px 12px;
    text-decoration: none;
    color: #666;
    transition: color 0.3s ease;
    min-width: 60px;
    cursor: pointer;
}

.nav-item.active {
    color: #667eea;
}

.nav-item i {
    font-size: 20px;
    margin-bottom: 4px;
}

.nav-item span {
    font-size: 12px;
    font-weight: 500;
}

/* إخفاء Navigation في الشاشات الكبيرة */
@media (min-width: 1024px) {
    .bottom-navigation {
        display: none;
    }
}

/* إخفاء Navigation في صفحات معينة */
.hide-navigation .bottom-navigation {
    display: none !important;
}

/* تعديل padding للمحتوى لتجنب تداخل Navigation */
@media (max-width: 1023px) {
    .app-content {
        padding-bottom: 80px;
    }

    /* إزالة padding عندما تكون Navigation مخفية */
    .hide-navigation .app-content {
        padding-bottom: 0;
    }
}

/* ===== تحسينات الأداء ===== */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(102, 126, 234, 0.3);
    border-radius: 50%;
    border-top-color: #667eea;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* ===== نظام الألوان الموحد ===== */
:root {
    /* الألوان الأساسية */
    --primary-color: #667eea;
    --primary-dark: #5a6fd8;
    --primary-light: #f0f4ff;
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

    /* الألوان الثانوية */
    --secondary-color: #764ba2;
    --secondary-light: #f8f9ff;

    /* ألوان الحالة */
    --success-color: #27ae60;
    --success-light: #f8fff8;
    --warning-color: #f39c12;
    --warning-light: #fff8e1;
    --error-color: #e74c3c;
    --error-light: #ffebee;
    --info-color: #3498db;
    --info-light: #e3f2fd;

    /* ألوان النص */
    --text-color: #333;
    --text-light: #666;
    --text-muted: #999;
    --text-inverse: #ffffff;

    /* ألوان الخلفية */
    --background-color: #f0f2f5;
    --surface-color: #ffffff;
    --surface-secondary: #f8f9fa;
    --surface-tertiary: #e9ecef;

    /* ألوان الحدود */
    --border-color: #e0e0e0;
    --border-light: #f0f0f0;
    --border-dark: #dee2e6;

    /* ألوان خاصة */
    --star-color: #ffc107;
    --heart-color: #ff6b6b;
    --discount-color: #ff4757;
    --free-shipping-color: #4CAF50;

    /* الظلال */
    --shadow-sm: 0 2px 8px rgba(0,0,0,0.1);
    --shadow-md: 0 4px 16px rgba(0,0,0,0.15);
    --shadow-lg: 0 8px 25px rgba(0,0,0,0.15);

    /* التدرجات */
    --gradient-primary: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    --gradient-success: linear-gradient(135deg, var(--success-color) 0%, #229954 100%);
    --gradient-error: linear-gradient(135deg, var(--error-color) 0%, #c0392b 100%);
    --gradient-warning: linear-gradient(135deg, var(--warning-color) 0%, #e67e22 100%);
}

/* ===== تحسينات الأزرار ===== */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    min-height: 44px;
}

.btn-primary {
    background: var(--gradient-primary);
    color: var(--text-inverse);
    border: none;
}

.btn-primary:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-secondary {
    background: var(--surface-secondary);
    color: var(--text-color);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover {
    background: var(--surface-tertiary);
    border-color: var(--primary-color);
}

.btn-success {
    background: var(--gradient-success);
    color: var(--text-inverse);
    border: none;
}

.btn-success:hover {
    background: var(--success-color);
    transform: translateY(-1px);
}

.btn-error {
    background: var(--gradient-error);
    color: var(--text-inverse);
    border: none;
}

.btn-error:hover {
    background: var(--error-color);
    transform: translateY(-1px);
}

.btn-outline {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-outline:hover {
    background: var(--primary-color);
    color: var(--text-inverse);
}

/* ===== تحسينات الكروت ===== */
.card {
    background: var(--surface-color);
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    overflow: hidden;
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0,0,0,0.15);
}

.product-card {
    cursor: pointer;
}

.product-card img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.product-card .card-body {
    padding: 16px;
}

.product-card .product-name {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 8px;
    color: var(--text-color);
}

.product-card .product-price {
    font-size: 18px;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 12px;
}

/* ===== تحسينات الشبكة ===== */
.grid {
    display: grid;
    gap: 16px;
}

.grid-cols-1 { grid-template-columns: 1fr; }
.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.grid-cols-4 { grid-template-columns: repeat(4, 1fr); }

@media (max-width: 640px) {
    .grid-cols-2 { grid-template-columns: 1fr; }
    .grid-cols-3 { grid-template-columns: repeat(2, 1fr); }
    .grid-cols-4 { grid-template-columns: repeat(2, 1fr); }
}

/* ===== تحسينات النصوص ===== */
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-left { text-align: left; }

.text-sm { font-size: 14px; }
.text-lg { font-size: 18px; }
.text-xl { font-size: 20px; }

.font-bold { font-weight: 700; }
.font-semibold { font-weight: 600; }

/* ===== تحسينات المسافات ===== */
.p-4 { padding: 16px; }
.p-6 { padding: 24px; }
.m-4 { margin: 16px; }
.mb-4 { margin-bottom: 16px; }
.mt-4 { margin-top: 16px; }

/* ===== تحسينات الحاويات ===== */
.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 16px;
}

.flex {
    display: flex;
}

.flex-col {
    flex-direction: column;
}

.items-center {
    align-items: center;
}

.justify-center {
    justify-content: center;
}

.justify-between {
    justify-content: space-between;
}

/* ===== نظام الإشعارات المحسن ===== */
.toast {
    position: fixed;
    top: 20px;
    right: 20px;
    background: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 0;
    box-shadow: var(--shadow-lg);
    z-index: 1000;
    max-width: 400px;
    min-width: 300px;
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.3s var(--animation-easing);
    overflow: hidden;
}

.toast.show {
    opacity: 1;
    transform: translateX(0);
}

.toast-content {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px 20px;
}

.toast-icon {
    font-size: 20px;
    flex-shrink: 0;
}

.toast-message {
    flex: 1;
    font-size: 14px;
    font-weight: 500;
    line-height: 1.4;
    color: var(--text-color);
}

.toast-close {
    position: absolute;
    top: 8px;
    left: 8px;
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    color: var(--text-muted);
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.toast-close:hover {
    background: rgba(0, 0, 0, 0.1);
    color: var(--text-color);
}

/* أنواع الإشعارات */
.toast-success {
    border-left: 4px solid var(--success-color);
    background: var(--success-light);
}

.toast-success .toast-icon {
    color: var(--success-color);
}

.toast-error {
    border-left: 4px solid var(--error-color);
    background: var(--error-light);
}

.toast-error .toast-icon {
    color: var(--error-color);
}

.toast-warning {
    border-left: 4px solid var(--warning-color);
    background: var(--warning-light);
}

.toast-warning .toast-icon {
    color: var(--warning-color);
}

.toast-info {
    border-left: 4px solid var(--info-color);
    background: var(--info-light);
}

.toast-info .toast-icon {
    color: var(--info-color);
}

/* تأثيرات الرسوم المتحركة */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutRight {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 640px) {
    .toast {
        right: 10px;
        left: 10px;
        max-width: none;
        min-width: auto;
    }

    .toast-content {
        padding: 14px 16px;
    }

    .toast-message {
        font-size: 13px;
    }
}

/* ===== تحسينات الوصولية ===== */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* ===== تحسينات التركيز ===== */
.focus-visible:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* ===== تحسينات الأداء ===== */
/* تحسين الرسوم المتحركة */
* {
    will-change: auto;
}

.card:hover,
.btn:hover,
.nav-item:hover {
    will-change: transform;
}

.loading-spinner {
    will-change: transform;
}

/* تحسين التمرير */
.app-content,
.screen {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
}

/* تحسين الخطوط */
body {
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* تحسين الصور */
img {
    max-width: 100%;
    height: auto;
    loading: lazy;
}

/* تقليل الحركة للمستخدمين الذين يفضلون ذلك */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

/* تحسين الذاكرة */
.hidden {
    display: none !important;
}

.invisible {
    visibility: hidden !important;
}

/* ===== تحسينات الطباعة ===== */
@media print {
    .bottom-navigation,
    .nav-item,
    .toast {
        display: none !important;
    }

    .phone-container {
        box-shadow: none;
        border-radius: 0;
        max-width: 100%;
    }

    .app-content {
        padding-bottom: 0;
    }

    .card {
        break-inside: avoid;
    }
}
