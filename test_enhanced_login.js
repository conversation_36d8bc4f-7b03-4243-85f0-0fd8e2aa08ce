/**
 * اختبار تحسينات تسجيل الدخول المطابقة للباك اند
 */

console.log('🔐 اختبار تحسينات تسجيل الدخول...');

// فحص عناصر تسجيل الدخول المحدثة
function checkLoginElements() {
    console.log('\n📋 فحص عناصر تسجيل الدخول...');
    
    const elements = {
        loginEmail: document.getElementById('loginEmail'),
        loginPassword: document.getElementById('loginPassword'),
        rememberMe: document.getElementById('rememberMe'),
        regularLogin: document.getElementById('regularLogin'),
        wholesaleLogin: document.getElementById('wholesaleLogin'),
        loginBtn: document.getElementById('loginBtn')
    };
    
    Object.entries(elements).forEach(([name, element]) => {
        if (element) {
            console.log(`✅ ${name} موجود`);
            if (element.type === 'radio') {
                console.log(`   - محدد: ${element.checked}`);
            }
            if (element.placeholder) {
                console.log(`   - Placeholder: ${element.placeholder}`);
            }
        } else {
            console.log(`❌ ${name} غير موجود`);
        }
    });
    
    return elements;
}

// اختبار تبديل نوع تسجيل الدخول
function testLoginTypeToggle() {
    console.log('\n🔄 اختبار تبديل نوع تسجيل الدخول...');
    
    const regularLogin = document.getElementById('regularLogin');
    const wholesaleLogin = document.getElementById('wholesaleLogin');
    
    if (!regularLogin || !wholesaleLogin) {
        console.log('❌ عناصر نوع تسجيل الدخول غير موجودة');
        return;
    }
    
    // اختبار التبديل إلى تاجر جملة
    console.log('1️⃣ التبديل إلى تاجر جملة...');
    wholesaleLogin.checked = true;
    regularLogin.checked = false;
    console.log(`   - تاجر جملة محدد: ${wholesaleLogin.checked ? '✅' : '❌'}`);
    
    // اختبار التبديل إلى عادي
    console.log('2️⃣ التبديل إلى عادي...');
    regularLogin.checked = true;
    wholesaleLogin.checked = false;
    console.log(`   - عادي محدد: ${regularLogin.checked ? '✅' : '❌'}`);
}

// اختبار التحقق من صحة البيانات
function testLoginValidation() {
    console.log('\n✅ اختبار التحقق من صحة بيانات تسجيل الدخول...');
    
    // اختبار بريد إلكتروني صحيح
    console.log('1️⃣ اختبار بريد إلكتروني صحيح...');
    const emailValid = validateLoginForm('<EMAIL>', 'password123');
    console.log(`   - صحة البيانات: ${emailValid ? '✅' : '❌'}`);
    
    // اختبار رقم هاتف صحيح
    console.log('2️⃣ اختبار رقم هاتف صحيح...');
    const phoneValid = validateLoginForm('07701234567', 'password123');
    console.log(`   - صحة البيانات: ${phoneValid ? '✅' : '❌'}`);
    
    // اختبار بيانات غير صحيحة
    console.log('3️⃣ اختبار بيانات غير صحيحة...');
    const invalidValid = validateLoginForm('invalid-input', 'pass');
    console.log(`   - صحة البيانات: ${invalidValid ? '❌ خطأ' : '✅ رفض صحيح'}`);
    
    // اختبار حقول فارغة
    console.log('4️⃣ اختبار حقول فارغة...');
    const emptyValid = validateLoginForm('', '');
    console.log(`   - صحة البيانات: ${emptyValid ? '❌ خطأ' : '✅ رفض صحيح'}`);
}

// محاكاة تسجيل دخول عادي
function simulateRegularLogin() {
    console.log('\n👤 محاكاة تسجيل دخول عادي...');
    
    // ملء النموذج
    document.getElementById('loginEmail').value = '<EMAIL>';
    document.getElementById('loginPassword').value = 'password123';
    document.getElementById('rememberMe').checked = true;
    
    // اختيار تسجيل دخول عادي
    document.getElementById('regularLogin').checked = true;
    document.getElementById('wholesaleLogin').checked = false;
    
    console.log('✅ تم ملء بيانات تسجيل دخول عادي');
    console.log('📋 البيانات:');
    console.log('- البريد/الهاتف: <EMAIL>');
    console.log('- كلمة المرور: password123');
    console.log('- تذكرني: نعم');
    console.log('- نوع التسجيل: عادي');
}

// محاكاة تسجيل دخول تاجر جملة
function simulateWholesaleLogin() {
    console.log('\n🏪 محاكاة تسجيل دخول تاجر جملة...');
    
    // ملء النموذج
    document.getElementById('loginEmail').value = '07709876543';
    document.getElementById('loginPassword').value = 'wholesale123';
    document.getElementById('rememberMe').checked = false;
    
    // اختيار تسجيل دخول تاجر جملة
    document.getElementById('wholesaleLogin').checked = true;
    document.getElementById('regularLogin').checked = false;
    
    console.log('✅ تم ملء بيانات تسجيل دخول تاجر جملة');
    console.log('📋 البيانات:');
    console.log('- البريد/الهاتف: 07709876543');
    console.log('- كلمة المرور: wholesale123');
    console.log('- تذكرني: لا');
    console.log('- نوع التسجيل: تاجر جملة');
}

// اختبار تسجيل دخول بالبريد الإلكتروني
function simulateEmailLogin() {
    console.log('\n📧 محاكاة تسجيل دخول بالبريد الإلكتروني...');
    
    document.getElementById('loginEmail').value = '<EMAIL>';
    document.getElementById('loginPassword').value = 'mypassword';
    
    console.log('✅ تم ملء بيانات تسجيل دخول بالبريد');
    console.log('📋 البيانات:');
    console.log('- نوع الإدخال: بريد إلكتروني');
    console.log('- البريد: <EMAIL>');
}

// اختبار تسجيل دخول برقم الهاتف
function simulatePhoneLogin() {
    console.log('\n📱 محاكاة تسجيل دخول برقم الهاتف...');
    
    document.getElementById('loginEmail').value = '07701234567';
    document.getElementById('loginPassword').value = 'mypassword';
    
    console.log('✅ تم ملء بيانات تسجيل دخول بالهاتف');
    console.log('📋 البيانات:');
    console.log('- نوع الإدخال: رقم هاتف');
    console.log('- الهاتف: 07701234567');
}

// اختبار وظائف التطبيق
function testApplicationFunctions() {
    console.log('\n🔧 اختبار وظائف التطبيق...');
    
    const functions = [
        'login',
        'validateLoginForm',
        'showLogin',
        'showSignup',
        'showForgotPassword',
        'togglePassword'
    ];
    
    functions.forEach(funcName => {
        if (typeof window[funcName] === 'function') {
            console.log(`✅ ${funcName} موجودة`);
        } else {
            console.log(`❌ ${funcName} غير موجودة`);
        }
    });
}

// اختبار مقارنة مع الباك اند
function compareWithBackend() {
    console.log('\n🔄 مقارنة مع الباك اند...');
    
    console.log('📊 المميزات المطابقة للباك اند:');
    console.log('✅ تسجيل الدخول بالبريد الإلكتروني');
    console.log('✅ تسجيل الدخول برقم الهاتف');
    console.log('✅ خيار "تذكرني"');
    console.log('✅ نسيان كلمة المرور');
    console.log('✅ التبديل بين تسجيل الدخول والتسجيل');
    
    console.log('\n🆕 المميزات الإضافية في التطبيق المحمول:');
    console.log('✅ نوع تسجيل الدخول (عادي/تاجر جملة)');
    console.log('✅ تسجيل الدخول برقم الهاتف مع OTP');
    console.log('✅ تصميم متجاوب وجميل');
    console.log('✅ رسائل ترحيب مخصصة');
    console.log('✅ تحقق متقدم من صحة البيانات');
}

// اختبار شامل
function runAllLoginTests() {
    console.log('🚀 بدء جميع اختبارات تسجيل الدخول...');
    
    // فحص العناصر والوظائف
    checkLoginElements();
    testApplicationFunctions();
    
    // اختبار التفاعل
    testLoginTypeToggle();
    testLoginValidation();
    
    // محاكاة تسجيل الدخول
    setTimeout(() => {
        simulateRegularLogin();
        
        setTimeout(() => {
            simulateWholesaleLogin();
            
            setTimeout(() => {
                simulateEmailLogin();
                
                setTimeout(() => {
                    simulatePhoneLogin();
                    
                    setTimeout(() => {
                        compareWithBackend();
                    }, 1000);
                }, 1000);
            }, 1000);
        }, 2000);
    }, 1000);
    
    console.log('\n✅ انتهت جميع الاختبارات');
}

// تشغيل الاختبارات
runAllLoginTests();

console.log('\n📋 الاختبارات المتاحة:');
console.log('- checkLoginElements() - فحص العناصر');
console.log('- testLoginTypeToggle() - اختبار تبديل النوع');
console.log('- testLoginValidation() - اختبار التحقق');
console.log('- simulateRegularLogin() - محاكاة تسجيل دخول عادي');
console.log('- simulateWholesaleLogin() - محاكاة تسجيل دخول تاجر جملة');
console.log('- simulateEmailLogin() - محاكاة تسجيل دخول بالبريد');
console.log('- simulatePhoneLogin() - محاكاة تسجيل دخول بالهاتف');
console.log('- compareWithBackend() - مقارنة مع الباك اند');
console.log('- runAllLoginTests() - تشغيل جميع الاختبارات');

console.log('\n🎯 المتوقع بعد التحديث:');
console.log('✅ تسجيل الدخول بالبريد الإلكتروني أو رقم الهاتف');
console.log('✅ خيار نوع تسجيل الدخول (عادي/تاجر جملة)');
console.log('✅ رسائل ترحيب مخصصة حسب نوع المستخدم');
console.log('✅ تحقق متقدم من صحة البيانات');
console.log('✅ تصميم متطابق مع باقي التطبيق');
console.log('✅ مطابقة كاملة مع وظائف الباك اند');
