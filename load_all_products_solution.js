/**
 * حل تحميل جميع المنتجات - Load All Products Solution
 * يحمل جميع المنتجات من جميع الصفحات تلقائياً
 */

const axios = require('axios');

class ProductLoader {
    constructor() {
        this.baseUrl = 'https://dalilakauto.com/api/v1/ecommerce/products';
        this.allProducts = [];
        this.isLoading = false;
        this.totalProducts = 0;
        this.totalPages = 0;
    }

    /**
     * تحميل جميع المنتجات من جميع الصفحات
     */
    async loadAllProducts() {
        if (this.isLoading) {
            console.log('⏳ التحميل قيد التقدم...');
            return this.allProducts;
        }

        this.isLoading = true;
        this.allProducts = [];
        
        console.log('🚀 بدء تحميل جميع المنتجات...');

        try {
            // تحميل الصفحة الأولى للحصول على معلومات Pagination
            const firstPage = await this.loadPage(1);
            
            if (!firstPage.success) {
                throw new Error('فشل في تحميل الصفحة الأولى');
            }

            this.totalProducts = firstPage.meta.total;
            this.totalPages = firstPage.meta.last_page;
            
            console.log(`📊 إجمالي المنتجات: ${this.totalProducts}`);
            console.log(`📄 إجمالي الصفحات: ${this.totalPages}`);

            // إضافة منتجات الصفحة الأولى
            this.allProducts.push(...firstPage.products);

            // تحميل باقي الصفحات
            const promises = [];
            for (let page = 2; page <= this.totalPages; page++) {
                promises.push(this.loadPage(page));
                
                // تحميل 5 صفحات في كل مرة لتجنب إرهاق الخادم
                if (promises.length === 5 || page === this.totalPages) {
                    const results = await Promise.all(promises);
                    
                    for (const result of results) {
                        if (result.success) {
                            this.allProducts.push(...result.products);
                            console.log(`✅ تم تحميل الصفحة ${result.page}: ${result.products.length} منتج`);
                        } else {
                            console.log(`❌ فشل تحميل الصفحة ${result.page}`);
                        }
                    }
                    
                    console.log(`📈 إجمالي المنتجات المحملة: ${this.allProducts.length}/${this.totalProducts}`);
                    
                    // تأخير قصير بين المجموعات
                    if (page < this.totalPages) {
                        await new Promise(resolve => setTimeout(resolve, 500));
                    }
                    
                    promises.length = 0; // مسح المصفوفة
                }
            }

            console.log(`🎉 تم تحميل جميع المنتجات بنجاح!`);
            console.log(`📊 العدد النهائي: ${this.allProducts.length} منتج`);

            return this.allProducts;

        } catch (error) {
            console.error('❌ خطأ في تحميل المنتجات:', error.message);
            throw error;
        } finally {
            this.isLoading = false;
        }
    }

    /**
     * تحميل صفحة واحدة
     */
    async loadPage(page) {
        try {
            const response = await axios.get(`${this.baseUrl}?page=${page}&per_page=30`);
            
            if (response.data && response.data.data) {
                return {
                    success: true,
                    page: page,
                    products: response.data.data,
                    meta: response.data.meta
                };
            } else {
                return {
                    success: false,
                    page: page,
                    error: 'استجابة غير صحيحة'
                };
            }
        } catch (error) {
            return {
                success: false,
                page: page,
                error: error.message
            };
        }
    }

    /**
     * البحث في جميع المنتجات
     */
    searchProducts(searchTerm) {
        if (!searchTerm) return this.allProducts;
        
        const term = searchTerm.toLowerCase();
        return this.allProducts.filter(product => 
            product.name.toLowerCase().includes(term) ||
            product.description?.toLowerCase().includes(term) ||
            product.sku?.toLowerCase().includes(term)
        );
    }

    /**
     * فلترة المنتجات حسب الفئة
     */
    filterByCategory(categoryId) {
        if (!categoryId) return this.allProducts;
        
        return this.allProducts.filter(product => 
            product.categories?.some(cat => cat.id === categoryId)
        );
    }

    /**
     * الحصول على إحصائيات
     */
    getStats() {
        return {
            totalProducts: this.allProducts.length,
            totalPages: this.totalPages,
            isLoading: this.isLoading,
            categories: [...new Set(this.allProducts.flatMap(p => p.categories?.map(c => c.name) || []))],
            brands: [...new Set(this.allProducts.map(p => p.brand?.name).filter(Boolean))]
        };
    }
}

// تصدير الكلاس
module.exports = ProductLoader;

// اختبار إذا تم تشغيل الملف مباشرة
if (require.main === module) {
    async function test() {
        const loader = new ProductLoader();
        
        try {
            const products = await loader.loadAllProducts();
            console.log('\n📊 إحصائيات نهائية:');
            console.log(loader.getStats());
            
            // اختبار البحث
            const searchResults = loader.searchProducts('شمعة');
            console.log(`\n🔍 نتائج البحث عن "شمعة": ${searchResults.length} منتج`);
            
        } catch (error) {
            console.error('❌ فشل الاختبار:', error.message);
        }
    }
    
    test();
}
