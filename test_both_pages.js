/**
 * اختبار كلا صفحتي المنتجات
 */

console.log('🧪 اختبار صفحات المنتجات...');

// اختبار 1: صفحة "جميع المنتجات"
console.log('\n=== اختبار صفحة "جميع المنتجات" ===');
console.log('للاختبار: اضغط على "مشاهدة المزيد" في قسم "جميع المنتجات"');
console.log('المتوقع: صفحة منفصلة مع قائمة تنقل SVG وتحميل 30 منتج');

// اختبار 2: صفحة "أدوات الصدر"
console.log('\n=== اختبار صفحة "أدوات الصدر" ===');
console.log('للاختبار: اضغط على "مشاهدة المزيد" في قسم "أدوات الصدر"');
console.log('المتوقع: صفحة منفصلة مع قائمة تنقل SVG ومنتجات مفلترة');

// وظائف مساعدة للاختبار
function testAllProductsPage() {
    console.log('🔍 اختبار صفحة جميع المنتجات...');
    showAllProducts();
    
    setTimeout(() => {
        const categoryPage = document.getElementById('categoryPage');
        const specificCategoryPage = document.getElementById('specificCategoryPage');
        
        console.log('Category Page (All Products):', categoryPage?.style.display);
        console.log('Specific Category Page:', specificCategoryPage?.style.display);
        
        const grid = document.getElementById('categoryProductsGrid');
        console.log('Products Grid:', grid?.innerHTML.substring(0, 100) + '...');
    }, 2000);
}

function testChestToolsPage() {
    console.log('🔧 اختبار صفحة أدوات الصدر...');
    showCategoryProducts('chest-tools');
    
    setTimeout(() => {
        const categoryPage = document.getElementById('categoryPage');
        const specificCategoryPage = document.getElementById('specificCategoryPage');
        
        console.log('Category Page (All Products):', categoryPage?.style.display);
        console.log('Specific Category Page (Chest Tools):', specificCategoryPage?.style.display);
    }, 2000);
}

// تشغيل الاختبارات
console.log('\n📋 الاختبارات المتاحة:');
console.log('- testAllProductsPage() - اختبار صفحة جميع المنتجات');
console.log('- testChestToolsPage() - اختبار صفحة أدوات الصدر');
console.log('\nاستخدم هذه الوظائف في console المتصفح');
