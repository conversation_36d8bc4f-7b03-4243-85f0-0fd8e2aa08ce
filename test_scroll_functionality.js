/**
 * اختبار وظيفة الاسكرول في الصفحات
 */

console.log('📜 اختبار وظيفة الاسكرول...');

// فحص إعدادات الاسكرول للصفحات
function checkScrollSettings() {
    console.log('\n📋 فحص إعدادات الاسكرول...');
    
    const pages = ['searchPage', 'profilePage', 'homePage', 'cartPage', 'wishlistPage'];
    
    pages.forEach(pageId => {
        const page = document.getElementById(pageId);
        if (page) {
            const styles = window.getComputedStyle(page);
            console.log(`\n📄 ${pageId}:`);
            console.log(`   - Height: ${styles.height}`);
            console.log(`   - Overflow-Y: ${styles.overflowY}`);
            console.log(`   - Webkit Overflow Scrolling: ${styles.webkitOverflowScrolling}`);
            console.log(`   - Scroll Height: ${page.scrollHeight}px`);
            console.log(`   - Client Height: ${page.clientHeight}px`);
            console.log(`   - Can Scroll: ${page.scrollHeight > page.clientHeight ? '✅' : '❌'}`);
        } else {
            console.log(`❌ ${pageId} غير موجود`);
        }
    });
}

// اختبار الاسكرول في صفحة البحث
function testSearchPageScroll() {
    console.log('\n🔍 اختبار الاسكرول في صفحة البحث...');
    
    // الانتقال لصفحة البحث
    showPage('search');
    
    setTimeout(() => {
        const searchPage = document.getElementById('searchPage');
        if (searchPage) {
            console.log('✅ صفحة البحث مفتوحة');
            
            // فحص إعدادات الاسكرول
            const styles = window.getComputedStyle(searchPage);
            console.log(`   - Height: ${styles.height}`);
            console.log(`   - Overflow-Y: ${styles.overflowY}`);
            console.log(`   - Scroll Height: ${searchPage.scrollHeight}px`);
            console.log(`   - Client Height: ${searchPage.clientHeight}px`);
            
            // محاولة الاسكرول
            if (searchPage.scrollHeight > searchPage.clientHeight) {
                console.log('📜 محاولة الاسكرول...');
                
                // الاسكرول لأسفل
                searchPage.scrollTop = 100;
                setTimeout(() => {
                    console.log(`   - Scroll Top after scroll: ${searchPage.scrollTop}px`);
                    if (searchPage.scrollTop > 0) {
                        console.log('✅ الاسكرول يعمل في صفحة البحث');
                    } else {
                        console.log('❌ الاسكرول لا يعمل في صفحة البحث');
                    }
                    
                    // العودة لأعلى
                    searchPage.scrollTop = 0;
                }, 500);
            } else {
                console.log('ℹ️ المحتوى لا يحتاج اسكرول');
            }
        } else {
            console.log('❌ صفحة البحث غير موجودة');
        }
    }, 500);
}

// اختبار الاسكرول في صفحة الحساب
function testProfilePageScroll() {
    console.log('\n👤 اختبار الاسكرول في صفحة الحساب...');
    
    // الانتقال لصفحة الحساب
    showPage('profile');
    
    setTimeout(() => {
        const profilePage = document.getElementById('profilePage');
        if (profilePage) {
            console.log('✅ صفحة الحساب مفتوحة');
            
            // فحص إعدادات الاسكرول
            const styles = window.getComputedStyle(profilePage);
            console.log(`   - Height: ${styles.height}`);
            console.log(`   - Overflow-Y: ${styles.overflowY}`);
            console.log(`   - Scroll Height: ${profilePage.scrollHeight}px`);
            console.log(`   - Client Height: ${profilePage.clientHeight}px`);
            
            // محاولة الاسكرول
            if (profilePage.scrollHeight > profilePage.clientHeight) {
                console.log('📜 محاولة الاسكرول...');
                
                // الاسكرول لأسفل
                profilePage.scrollTop = 100;
                setTimeout(() => {
                    console.log(`   - Scroll Top after scroll: ${profilePage.scrollTop}px`);
                    if (profilePage.scrollTop > 0) {
                        console.log('✅ الاسكرول يعمل في صفحة الحساب');
                    } else {
                        console.log('❌ الاسكرول لا يعمل في صفحة الحساب');
                    }
                    
                    // العودة لأعلى
                    profilePage.scrollTop = 0;
                }, 500);
            } else {
                console.log('ℹ️ المحتوى لا يحتاج اسكرول');
            }
        } else {
            console.log('❌ صفحة الحساب غير موجودة');
        }
    }, 500);
}

// إضافة محتوى إضافي لاختبار الاسكرول
function addTestContent() {
    console.log('\n📝 إضافة محتوى إضافي لاختبار الاسكرول...');
    
    // إضافة محتوى لصفحة البحث
    const searchPage = document.getElementById('searchPage');
    if (searchPage) {
        const testContent = document.createElement('div');
        testContent.id = 'testScrollContent';
        testContent.innerHTML = `
            <div style="padding: 20px; margin: 20px 0;">
                <h3>محتوى اختبار الاسكرول</h3>
                ${Array.from({length: 20}, (_, i) => `
                    <div style="padding: 15px; margin: 10px 0; background: #f8f9fa; border-radius: 8px;">
                        <h4>عنصر اختبار ${i + 1}</h4>
                        <p>هذا محتوى اختبار للتأكد من عمل الاسكرول بشكل صحيح. يجب أن تكون قادراً على رؤية هذا المحتوى والاسكرول خلاله بسهولة.</p>
                    </div>
                `).join('')}
            </div>
        `;
        searchPage.appendChild(testContent);
        console.log('✅ تم إضافة محتوى اختبار لصفحة البحث');
    }
    
    // إضافة محتوى لصفحة الحساب
    const profilePage = document.getElementById('profilePage');
    if (profilePage) {
        const testContent = document.createElement('div');
        testContent.id = 'testScrollContentProfile';
        testContent.innerHTML = `
            <div style="padding: 20px; margin: 20px 0;">
                <h3>محتوى اختبار الاسكرول - صفحة الحساب</h3>
                ${Array.from({length: 15}, (_, i) => `
                    <div style="padding: 15px; margin: 10px 0; background: #e3f2fd; border-radius: 8px;">
                        <h4>قسم ${i + 1}</h4>
                        <p>هذا محتوى اختبار لصفحة الحساب. يجب أن يكون الاسكرول سلساً وسهل الاستخدام على جميع الأجهزة.</p>
                    </div>
                `).join('')}
            </div>
        `;
        profilePage.appendChild(testContent);
        console.log('✅ تم إضافة محتوى اختبار لصفحة الحساب');
    }
}

// إزالة محتوى الاختبار
function removeTestContent() {
    console.log('\n🗑️ إزالة محتوى الاختبار...');
    
    const testContents = ['testScrollContent', 'testScrollContentProfile'];
    testContents.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.remove();
            console.log(`✅ تم إزالة ${id}`);
        }
    });
}

// اختبار الاسكرول على الأجهزة المختلفة
function testScrollOnDifferentDevices() {
    console.log('\n📱 اختبار الاسكرول على الأجهزة المختلفة...');
    
    const phoneContainer = document.querySelector('.phone-container');
    if (phoneContainer) {
        const styles = window.getComputedStyle(phoneContainer);
        console.log('📱 Phone Container:');
        console.log(`   - Width: ${styles.width}`);
        console.log(`   - Height: ${styles.height}`);
        console.log(`   - Overflow: ${styles.overflow}`);
        console.log(`   - Display: ${styles.display}`);
    }
    
    // فحص دعم touch scrolling
    const supportsTouch = 'ontouchstart' in window;
    console.log(`📱 Touch Support: ${supportsTouch ? '✅' : '❌'}`);
    
    // فحص webkit overflow scrolling
    const testDiv = document.createElement('div');
    testDiv.style.webkitOverflowScrolling = 'touch';
    const supportsWebkitScrolling = testDiv.style.webkitOverflowScrolling === 'touch';
    console.log(`🍎 Webkit Overflow Scrolling: ${supportsWebkitScrolling ? '✅' : '❌'}`);
}

// اختبار شامل للاسكرول
function runAllScrollTests() {
    console.log('🚀 بدء جميع اختبارات الاسكرول...');
    
    // فحص الإعدادات الأساسية
    checkScrollSettings();
    testScrollOnDifferentDevices();
    
    // إضافة محتوى للاختبار
    addTestContent();
    
    // اختبار الصفحات
    setTimeout(() => {
        testSearchPageScroll();
        
        setTimeout(() => {
            testProfilePageScroll();
            
            // العودة للصفحة الرئيسية
            setTimeout(() => {
                showPage('home');
                console.log('\n✅ انتهت جميع اختبارات الاسكرول');
                console.log('\n📊 ملخص النتائج:');
                console.log('- تم فحص إعدادات الاسكرول لجميع الصفحات');
                console.log('- تم اختبار الاسكرول في صفحة البحث');
                console.log('- تم اختبار الاسكرول في صفحة الحساب');
                console.log('- تم فحص دعم الأجهزة المختلفة');
                
                // إزالة محتوى الاختبار بعد 5 ثوان
                setTimeout(removeTestContent, 5000);
            }, 3000);
        }, 3000);
    }, 1000);
}

// تشغيل الاختبارات
runAllScrollTests();

console.log('\n📋 الاختبارات المتاحة:');
console.log('- checkScrollSettings() - فحص إعدادات الاسكرول');
console.log('- testSearchPageScroll() - اختبار اسكرول صفحة البحث');
console.log('- testProfilePageScroll() - اختبار اسكرول صفحة الحساب');
console.log('- addTestContent() - إضافة محتوى للاختبار');
console.log('- removeTestContent() - إزالة محتوى الاختبار');
console.log('- testScrollOnDifferentDevices() - اختبار الأجهزة المختلفة');
console.log('- runAllScrollTests() - تشغيل جميع الاختبارات');

console.log('\n🎯 المتوقع بعد الإصلاح:');
console.log('✅ الاسكرول يعمل بسلاسة في صفحة البحث');
console.log('✅ الاسكرول يعمل بسلاسة في صفحة الحساب');
console.log('✅ دعم touch scrolling للأجهزة المحمولة');
console.log('✅ شريط اسكرول مخصص وجميل');
console.log('✅ العودة لأعلى الصفحة عند التنقل');
