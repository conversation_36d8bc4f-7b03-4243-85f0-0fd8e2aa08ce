# 📱 دليل إنشاء APK لتطبيق دليلك أوتو

## ✅ تم تحديث أيقونة التطبيق
تم تحديث جميع الملفات لاستخدام شعار "دليلك" الاحترافي:
- ✅ manifest.json محدث
- ✅ HTML meta tags محدثة  
- ✅ capacitor.config.ts جاهز
- ✅ package.json محدث

## 🚀 خطوات بناء APK

### الخطوة 1: تثبيت المتطلبات
```bash
# تأكد من تثبيت Node.js (16+)
node --version

# تأكد من تثبيت Android Studio
# تحميل من: https://developer.android.com/studio

# تثبيت Java Development Kit (JDK 11+)
java --version
```

### الخطوة 2: تثبيت التبعيات
```bash
# انتقل لمجلد التطبيق
cd /Applications/XAMPP/xamppfiles/htdocs/dalila/dalilkMobileApp

# تثبيت التبعيات
npm install

# تثبيت Capacitor CLI عالمياً (إذا لم يكن مثبت)
npm install -g @capacitor/cli
```

### الخطوة 3: إعداد المشروع
```bash
# تهيئة Capacitor (إذا لم يكن مهيأ)
npx cap init "دليلك أوتو" "com.dalilakauto.app"

# إضافة منصة Android (إذا لم تكن مضافة)
npx cap add android

# نسخ ملفات الويب
npx cap copy android

# مزامنة المشروع
npx cap sync android
```

### الخطوة 4: فتح Android Studio
```bash
# فتح المشروع في Android Studio
npx cap open android
```

### الخطوة 5: بناء APK في Android Studio

#### الطريقة الأولى: Debug APK (للاختبار)
1. في Android Studio، اذهب إلى: **Build > Build Bundle(s) / APK(s) > Build APK(s)**
2. انتظر حتى اكتمال البناء
3. ستجد APK في: `android/app/build/outputs/apk/debug/app-debug.apk`

#### الطريقة الثانية: Release APK (للنشر)
1. في Android Studio، اذهب إلى: **Build > Generate Signed Bundle / APK**
2. اختر **APK**
3. أنشئ keystore جديد أو استخدم موجود
4. املأ معلومات التوقيع
5. اختر **release** build variant
6. اضغط **Finish**

### الخطوة 6: إعدادات إضافية

#### تحديث أذونات Android
في ملف `android/app/src/main/AndroidManifest.xml`:
```xml
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
<uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
<uses-permission android:name="android.permission.CAMERA" />
<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
<uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
<uses-permission android:name="android.permission.VIBRATE" />
```

#### تحديث أيقونة التطبيق
1. ضع ملف الشعار في: `android/app/src/main/res/`
2. استخدم Android Studio لإنشاء أيقونات بأحجام مختلفة:
   - **Tools > Resource Manager > + > Image Asset**
   - اختر **Launcher Icons (Adaptive and Legacy)**
   - ارفع شعار دليلك
   - اضغط **Next** ثم **Finish**

### الخطوة 7: اختبار APK
```bash
# تشغيل على جهاز متصل أو محاكي
npx cap run android

# أو تثبيت APK يدوياً
adb install android/app/build/outputs/apk/debug/app-debug.apk
```

## 🔧 استكشاف الأخطاء

### مشاكل شائعة:

#### 1. خطأ في Android SDK
```bash
# تأكد من تعيين متغيرات البيئة
export ANDROID_HOME=$HOME/Library/Android/sdk
export PATH=$PATH:$ANDROID_HOME/tools:$ANDROID_HOME/platform-tools
```

#### 2. مشاكل في Gradle
```bash
# تنظيف المشروع
cd android
./gradlew clean

# إعادة البناء
./gradlew build
```

#### 3. مشاكل في Capacitor
```bash
# تنظيف Capacitor
npx cap clean android

# إعادة المزامنة
npx cap sync android
```

## 📋 قائمة التحقق النهائية

- [ ] Node.js مثبت (16+)
- [ ] Android Studio مثبت
- [ ] JDK مثبت (11+)
- [ ] Android SDK مثبت
- [ ] متغيرات البيئة معدة
- [ ] التبعيات مثبتة (`npm install`)
- [ ] المشروع مهيأ (`npx cap init`)
- [ ] منصة Android مضافة (`npx cap add android`)
- [ ] الملفات منسوخة (`npx cap copy android`)
- [ ] المشروع متزامن (`npx cap sync android`)
- [ ] الأذونات محدثة في AndroidManifest.xml
- [ ] الأيقونات محدثة
- [ ] APK مبني بنجاح

## 🎉 النتيجة النهائية

بعد اتباع هذه الخطوات، ستحصل على:
- ✅ APK جاهز للتثبيت
- ✅ أيقونة دليلك الاحترافية
- ✅ جميع الميزات تعمل
- ✅ تطبيق جاهز للنشر

## 📞 الدعم

إذا واجهت أي مشاكل:
1. تحقق من الـ logs في Android Studio
2. راجع وثائق Capacitor: https://capacitorjs.com/docs
3. تأكد من تحديث جميع التبعيات

---

**ملاحظة:** هذا الدليل مخصص لبناء APK من تطبيق دليلك أوتو مع الشعار الاحترافي الجديد.
