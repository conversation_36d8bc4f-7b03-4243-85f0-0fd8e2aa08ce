#!/bin/bash

echo "🚀 تحضير APK سريع لتطبيق دليلك أوتو"
echo "====================================="

# التأكد من وجود الملفات
if [ ! -f "web-preview.html" ]; then
    echo "❌ ملف web-preview.html غير موجود"
    exit 1
fi

echo "✅ الملف الرئيسي موجود: web-preview.html"

# إنشاء مجلد APK
mkdir -p apk-ready

# نسخ الملفات المطلوبة
echo "📋 نسخ الملفات..."
cp web-preview.html apk-ready/index.html
cp manifest.json apk-ready/ 2>/dev/null || echo "⚠️  manifest.json غير موجود"
cp capacitor.config.json apk-ready/ 2>/dev/null || echo "⚠️  capacitor.config.json غير موجود"

# إنشاء ملف معلومات
cat > apk-ready/app-info.txt << EOF
📱 معلومات تطبيق دليلك أوتو
============================

اسم التطبيق: دليلك أوتو - قطع غيار السيارات
Package Name: com.dalilakauto.app
الإصدار: 1.0.0
الشعار: https://dalilakauto.com/storage/main/general/logo.png

الملفات المطلوبة لإنشاء APK:
- index.html (الملف الرئيسي)
- manifest.json (معلومات PWA)
- capacitor.config.json (إعدادات التطبيق)

طرق إنشاء APK:
1. AppsGeyser: https://appsgeyser.com
2. Appy Pie: https://appypie.com
3. BuildFire: https://buildfire.com

تعليمات:
1. ارفع index.html على أحد المواقع
2. أضف الشعار والمعلومات
3. حمل APK جاهز

الميزات:
✅ شعار دليلك بألوانه الطبيعية
✅ بحث متقدم بالسيارة 🚗
✅ قائمة سفلية احترافية
✅ Infinite Scroll
✅ Wishlist API
✅ فلاتر وترتيب شامل
EOF

echo "✅ تم إنشاء مجلد apk-ready"
echo "📁 الملفات الجاهزة:"
ls -la apk-ready/

echo ""
echo "🎯 الخطوات التالية:"
echo "1. اذهب إلى https://appsgeyser.com"
echo "2. اختر 'Website to App'"
echo "3. ارفع ملف apk-ready/index.html"
echo "4. أضف المعلومات من app-info.txt"
echo "5. حمل APK جاهز!"

echo ""
echo "🌟 التطبيق جاهز مع جميع التحسينات!"
echo "📱 اللوجو بألوانه الطبيعية ✅"
echo "🚗 البحث المتقدم ✅"
echo "♾️  Infinite Scroll ✅"
echo "🎨 تصميم احترافي ✅"
