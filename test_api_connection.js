// 🧪 اختبار الاتصال بـ APIs - مشروع دليل
const axios = require('axios');

const BASE_URL = 'https://dalilakauto.com/api/v1';

// إعداد Axios
const apiClient = axios.create({
    baseURL: BASE_URL,
    timeout: 10000,
    headers: {
        'Content-Type': 'application/json',
        Accept: 'application/json',
    },
});

// اختبار الاتصال الأساسي
async function testConnection() {
    console.log('🔗 اختبار الاتصال بالخادم...');
    console.log(`📡 Base URL: ${BASE_URL}`);
    console.log('=' * 50);

  try {
        // اختبار صفحة الصحة
        const healthResponse = await axios.get('https://dalilakauto.com');
        console.log('✅ الخادم يعمل بنجاح');
    } catch (error) {
        console.log('❌ فشل في الاتصال بالخادم:', error.message);
        return false;
    }

  return true;
}

// اختبار APIs المختلفة
async function testAPIs() {
    const tests = [
        {
            name: 'Products API',
            endpoint: '/ecommerce/products',
            method: 'GET',
        },
        {
            name: 'Categories API',
            endpoint: '/ecommerce/product-categories',
            method: 'GET',
        },
        {
            name: 'Brands API',
            endpoint: '/ecommerce/brands',
            method: 'GET',
        },
        {
            name: 'Vehicle Parts - Root Categories',
            endpoint: '/vehicle-parts/root-categories',
            method: 'GET',
        },
        {
            name: 'Vehicle Parts - Makes',
            endpoint: '/vehicle-parts/makes',
            method: 'GET',
        },
        {
            name: 'Countries API',
            endpoint: '/location/countries',
            method: 'GET',
        },
        {
            name: 'Contact Subjects',
            endpoint: '/contact/subjects',
            method: 'GET',
        },
    ];


  console.log('\n🧪 اختبار APIs المختلفة:');
    console.log('=' * 50);

  for (const test of tests) {
        try {
            console.log(`\n📋 اختبار: ${test.name}`);
            console.log(`🔗 ${test.method} ${test.endpoint}`);

      const response = await apiClient.request({
                method: test.method,
                url: test.endpoint,
            });


      console.log(`✅ نجح - Status: ${response.status}`);

      if (response.data) {
                if (response.data.data && Array.isArray(response.data.data)) {
                    console.log(
                        `📊 البيانات: ${response.data.data.length} عنصر`,
                    );
                } else if (Array.isArray(response.data)) {
                    console.log(`📊 البيانات: ${response.data.length} عنصر`);
                } else {
                    console.log(`📊 البيانات: ${typeof response.data}`);
                }
            }
        } catch (error) {
            console.log(
                `❌ فشل - ${error.response?.status || 'Network Error'}: ${
                    error.message
                }`,
            );

            if (error.response?.data) {
                console.log(
                    `📝 تفاصيل الخطأ: ${JSON.stringify(
                        error.response.data,
                        null,
                        2,
                    )}`,
                );
            }
        }
    }
}

// اختبار تسجيل الدخول
async function testAuth() {
    console.log('\n🔐 اختبار نظام المصادقة:');
    console.log('=' * 50);

  try {
        // اختبار فحص البريد الإلكتروني
        console.log('\n📧 اختبار فحص البريد الإلكتروني...');
        const emailCheckResponse = await apiClient.post('/email/check', {
            email: '<EMAIL>',
        });
        console.log('✅ فحص البريد الإلكتروني يعمل');

  } catch (error) {
        console.log(
            `❌ فشل في فحص البريد الإلكتروني: ${
                error.response?.status || 'Network Error'
            }`,
        );
    }

  try {
        // اختبار تسجيل الدخول (متوقع أن يفشل بدون بيانات صحيحة)
        console.log('\n🔑 اختبار تسجيل الدخول...');
        const loginResponse = await apiClient.post('/login', {
            email: '<EMAIL>',
            password: 'wrongpassword',
        });
        console.log('✅ API تسجيل الدخول متاح');

  } catch (error) {
        if (error.response?.status === 422 || error.response?.status === 401) {
            console.log(
                '✅ API تسجيل الدخول يعمل (رفض بيانات خاطئة كما هو متوقع)',
            );
        } else {
            console.log(
                `❌ فشل في تسجيل الدخول: ${
                    error.response?.status || 'Network Error'
                }`,
            );
        }
    }
}

// تشغيل جميع الاختبارات
async function runAllTests() {
    console.log('🚀 بدء اختبار APIs - مشروع دليل');
    console.log('⏰ ' + new Date().toLocaleString('ar-EG'));
    console.log('=' * 60);


  // اختبار الاتصال الأساسي
    const isConnected = await testConnection();


  if (!isConnected) {
        console.log(
            '\n❌ فشل في الاتصال بالخادم. تأكد من الاتصال بالإنترنت والوصول إلى https://dalilakauto.com',
        );
        return;
    }


  // اختبار APIs
    await testAPIs();


  // اختبار المصادقة
    await testAuth();

  console.log('\n' + '=' * 60);
    console.log('✅ انتهى اختبار APIs');
    console.log('📝 ملاحظة: بعض APIs قد تتطلب مصادقة أو إعداد إضافي');
}

// تشغيل الاختبارات
runAllTests().catch(console.error);
