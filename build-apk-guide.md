# 📱 دليل إنشاء APK للتطبيق

## المتطلبات الأساسية:

### 1. تثبيت Node.js
```bash
# تحميل من: https://nodejs.org/
# أو باستخدام Homebrew على macOS:
brew install node
```

### 2. تثبيت Android Studio
```bash
# تحميل من: https://developer.android.com/studio
# تأكد من تثبيت Android SDK
```

### 3. تثبيت Capacitor CLI
```bash
npm install -g @capacitor/cli
```

## خطوات إنشاء APK:

### الخطوة 1: إعداد المشروع
```bash
# انتقل لمجلد التطبيق
cd /Applications/XAMPP/xamppfiles/htdocs/dalila/dalilkMobileApp

# إنشاء package.json
npm init -y

# تثبيت Capacitor
npm install @capacitor/core @capacitor/android
```

### الخطوة 2: إعداد Capacitor
```bash
# تهيئة Capacitor
npx cap init "دليلك لقطع الغيار" "com.dalilakauto.app"

# إضافة منصة Android
npx cap add android
```

### الخطوة 3: نسخ الملفات
```bash
# نسخ ملفات الويب
npx cap copy android

# فتح Android Studio
npx cap open android
```

### الخطوة 4: بناء APK في Android Studio
1. افتح المشروع في Android Studio
2. اذهب إلى Build > Build Bundle(s) / APK(s) > Build APK(s)
3. انتظر حتى اكتمال البناء
4. ستجد APK في: `android/app/build/outputs/apk/debug/`

## إعدادات إضافية:

### تحديث capacitor.config.ts
```typescript
import { CapacitorConfig } from '@capacitor/cli';

const config: CapacitorConfig = {
  appId: 'com.dalilakauto.app',
  appName: 'دليلك لقطع الغيار',
  webDir: '.',
  server: {
    androidScheme: 'https'
  },
  plugins: {
    SplashScreen: {
      launchShowDuration: 2000,
      backgroundColor: "#667eea",
      showSpinner: false
    }
  }
};

export default config;
```

### تحديث android/app/src/main/AndroidManifest.xml
```xml
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
<uses-permission android:name="android.permission.CAMERA" />
<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
```

## الطريقة البديلة: استخدام Cordova

### 1. تثبيت Cordova
```bash
npm install -g cordova
```

### 2. إنشاء مشروع Cordova
```bash
cordova create DalilaApp com.dalilakauto.app "دليلك لقطع الغيار"
cd DalilaApp
```

### 3. إضافة منصة Android
```bash
cordova platform add android
```

### 4. نسخ ملفات التطبيق
```bash
# انسخ جميع ملفات التطبيق إلى مجلد www
cp -r /path/to/your/app/* www/
```

### 5. بناء APK
```bash
cordova build android
# أو للإصدار النهائي:
cordova build android --release
```

## نصائح مهمة:

### 1. تحسين الأداء
- ضغط الصور والملفات
- تصغير CSS و JavaScript
- استخدام Service Workers للتخزين المؤقت

### 2. الأمان
- استخدام HTTPS للـ APIs
- تشفير البيانات الحساسة
- إضافة Content Security Policy

### 3. التوقيع الرقمي (للنشر)
```bash
# إنشاء keystore
keytool -genkey -v -keystore my-release-key.keystore -alias alias_name -keyalg RSA -keysize 2048 -validity 10000

# توقيع APK
jarsigner -verbose -sigalg SHA1withRSA -digestalg SHA1 -keystore my-release-key.keystore app-release-unsigned.apk alias_name

# محاذاة APK
zipalign -v 4 app-release-unsigned.apk app-release.apk
```

## استكشاف الأخطاء:

### مشاكل شائعة:
1. **خطأ في Android SDK**: تأكد من تثبيت SDK Tools
2. **مشاكل في الأذونات**: تحقق من AndroidManifest.xml
3. **أخطاء في البناء**: نظف المشروع وأعد البناء

### حلول:
```bash
# تنظيف المشروع
npx cap clean android

# إعادة المزامنة
npx cap sync android

# تحديث التبعيات
npm update
```
