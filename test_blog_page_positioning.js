/**
 * اختبار موضع صفحة المدونة
 */

console.log('📝 اختبار موضع صفحة المدونة...');

// فحص الحاوية الرئيسية
function checkPhoneContainer() {
    console.log('\n📱 فحص حاوية الهاتف...');
    
    const phoneContainer = document.querySelector('.phone-container');
    if (phoneContainer) {
        const style = window.getComputedStyle(phoneContainer);
        console.log('✅ Phone container موجودة');
        console.log('Position:', style.position);
        console.log('Width:', style.width);
        console.log('Height:', style.height);
        console.log('Margin:', style.margin);
        
        return phoneContainer;
    } else {
        console.log('❌ Phone container غير موجودة');
        return null;
    }
}

// اختبار صفحة المدونة الرئيسية
function testMainBlogPage() {
    console.log('\n📋 اختبار صفحة المدونة الرئيسية...');
    
    try {
        // فتح صفحة المدونة
        showPage('blog');
        
        setTimeout(() => {
            const blogPage = document.getElementById('blogPage');
            if (blogPage) {
                console.log('✅ صفحة المدونة موجودة');
                console.log('Display:', blogPage.style.display);
                
                // فحص الموضع
                const rect = blogPage.getBoundingClientRect();
                const phoneContainer = document.querySelector('.phone-container');
                const phoneRect = phoneContainer?.getBoundingClientRect();
                
                if (phoneRect) {
                    console.log('موضع صفحة المدونة:', {
                        top: rect.top,
                        left: rect.left,
                        width: rect.width,
                        height: rect.height
                    });
                    
                    console.log('موضع حاوية الهاتف:', {
                        top: phoneRect.top,
                        left: phoneRect.left,
                        width: phoneRect.width,
                        height: phoneRect.height
                    });
                    
                    // التحقق من أن الصفحة داخل الحاوية
                    const isInside = rect.left >= phoneRect.left && 
                                   rect.top >= phoneRect.top && 
                                   rect.right <= phoneRect.right && 
                                   rect.bottom <= phoneRect.bottom;
                    
                    console.log('الصفحة داخل حاوية الهاتف:', isInside ? '✅ نعم' : '❌ لا');
                }
                
                // فحص القائمة السفلية
                const bottomNav = blogPage.querySelector('.bottom-nav');
                console.log('القائمة السفلية:', bottomNav ? '✅ موجودة' : '❌ غير موجودة');
                
            } else {
                console.log('❌ صفحة المدونة غير موجودة');
            }
        }, 1000);
        
    } catch (error) {
        console.error('❌ خطأ في اختبار صفحة المدونة:', error);
    }
}

// اختبار صفحة مقال المدونة
function testBlogPostPage() {
    console.log('\n📄 اختبار صفحة مقال المدونة...');
    
    // إنشاء مقال تجريبي
    const testPost = {
        slug: 'test-post',
        name: 'مقال تجريبي للاختبار',
        description: 'وصف المقال التجريبي',
        content: 'محتوى المقال التجريبي',
        image: 'https://via.placeholder.com/400x250',
        created_at: new Date().toISOString(),
        categories: [{ name: 'اختبار' }]
    };
    
    try {
        // محاكاة فتح مقال
        console.log('محاولة فتح مقال تجريبي...');
        
        // إنشاء HTML المقال مباشرة للاختبار
        const blogPostHTML = `
            <div class="page blog-post-page" id="blogPostPage" style="display: block;">
                <div class="page-header">
                    <button class="back-btn" onclick="goBack()">
                        <i class="fas fa-arrow-right"></i>
                    </button>
                    <h2>المقال</h2>
                </div>
                <div class="blog-post-content">
                    <div class="blog-post-header">
                        <img src="${testPost.image}" alt="${testPost.name}" class="blog-post-main-image">
                        <h1 class="blog-post-main-title">${testPost.name}</h1>
                    </div>
                    <div class="blog-post-body">
                        ${testPost.content}
                    </div>
                </div>
                <div class="bottom-nav">
                    <div class="nav-item active">
                        <div class="nav-icon">📝</div>
                        <div>المدونة</div>
                    </div>
                </div>
            </div>
        `;
        
        // إضافة للحاوية الصحيحة
        const phoneContainer = document.querySelector('.phone-container');
        if (phoneContainer) {
            // إخفاء الصفحات الأخرى
            const pages = document.querySelectorAll('.page');
            pages.forEach(page => page.style.display = 'none');
            
            phoneContainer.insertAdjacentHTML('beforeend', blogPostHTML);
            
            setTimeout(() => {
                const blogPostPage = document.getElementById('blogPostPage');
                if (blogPostPage) {
                    console.log('✅ صفحة المقال تم إنشاؤها');
                    
                    // فحص الموضع
                    const rect = blogPostPage.getBoundingClientRect();
                    const phoneRect = phoneContainer.getBoundingClientRect();
                    
                    console.log('موضع صفحة المقال:', {
                        top: rect.top,
                        left: rect.left,
                        width: rect.width,
                        height: rect.height
                    });
                    
                    // التحقق من أن الصفحة داخل الحاوية
                    const isInside = rect.left >= phoneRect.left && 
                                   rect.top >= phoneRect.top && 
                                   rect.right <= phoneRect.right;
                    
                    console.log('صفحة المقال داخل حاوية الهاتف:', isInside ? '✅ نعم' : '❌ لا');
                    
                    // تنظيف الاختبار
                    setTimeout(() => {
                        blogPostPage.remove();
                        console.log('🧹 تم تنظيف صفحة الاختبار');
                    }, 3000);
                    
                } else {
                    console.log('❌ فشل في إنشاء صفحة المقال');
                }
            }, 500);
            
        } else {
            console.log('❌ حاوية الهاتف غير موجودة');
        }
        
    } catch (error) {
        console.error('❌ خطأ في اختبار صفحة المقال:', error);
    }
}

// اختبار وظائف المدونة
function testBlogFunctions() {
    console.log('\n🔧 اختبار وظائف المدونة...');
    
    const functions = [
        'showBlogPost',
        'loadBlogPage',
        'loadBlogSection',
        'searchBlog',
        'filterBlogByCategory',
        'goBack'
    ];
    
    functions.forEach(funcName => {
        if (typeof window[funcName] === 'function') {
            console.log(`✅ ${funcName} موجودة`);
        } else {
            console.log(`❌ ${funcName} غير موجودة`);
        }
    });
}

// اختبار شامل
function runAllBlogTests() {
    console.log('🚀 بدء جميع اختبارات المدونة...');
    
    checkPhoneContainer();
    testBlogFunctions();
    testMainBlogPage();
    
    // اختبار صفحة المقال بعد فترة
    setTimeout(() => {
        testBlogPostPage();
    }, 3000);
    
    console.log('\n✅ انتهت جميع الاختبارات');
}

// تشغيل الاختبارات
runAllBlogTests();

console.log('\n📋 الاختبارات المتاحة:');
console.log('- checkPhoneContainer() - فحص حاوية الهاتف');
console.log('- testMainBlogPage() - اختبار صفحة المدونة الرئيسية');
console.log('- testBlogPostPage() - اختبار صفحة مقال المدونة');
console.log('- testBlogFunctions() - اختبار وظائف المدونة');
console.log('- runAllBlogTests() - تشغيل جميع الاختبارات');

console.log('\n🎯 المتوقع بعد الإصلاح:');
console.log('✅ صفحة المدونة تظهر داخل حاوية الهاتف');
console.log('✅ صفحة المقال تظهر داخل حاوية الهاتف');
console.log('✅ قائمة التنقل السفلية ظاهرة ومتسقة');
console.log('✅ التنقل يعمل بسلاسة بين الصفحات');
