/**
 * اختبار وظيفة التسجيل مع نوع العميل (جملة/مفرد)
 */

console.log('👥 اختبار وظيفة التسجيل مع نوع العميل...');

// فحص عناصر نوع العميل
function checkCustomerTypeElements() {
    console.log('\n📋 فحص عناصر نوع العميل...');
    
    const elements = {
        retailRadio: document.getElementById('retailCustomer'),
        wholesaleRadio: document.getElementById('wholesaleCustomer'),
        wholesaleFields: document.getElementById('wholesaleFields'),
        businessName: document.getElementById('businessName'),
        businessAddress: document.getElementById('businessAddress'),
        businessCity: document.getElementById('businessCity'),
        businessState: document.getElementById('businessState')
    };
    
    Object.entries(elements).forEach(([name, element]) => {
        if (element) {
            console.log(`✅ ${name} موجود`);
            if (element.type === 'radio') {
                console.log(`   - محدد: ${element.checked}`);
            }
            if (element.style && element.style.display) {
                console.log(`   - العرض: ${element.style.display}`);
            }
        } else {
            console.log(`❌ ${name} غير موجود`);
        }
    });
    
    return elements;
}

// اختبار تبديل نوع العميل
function testCustomerTypeToggle() {
    console.log('\n🔄 اختبار تبديل نوع العميل...');
    
    const retailRadio = document.getElementById('retailCustomer');
    const wholesaleRadio = document.getElementById('wholesaleCustomer');
    const wholesaleFields = document.getElementById('wholesaleFields');
    
    if (!retailRadio || !wholesaleRadio || !wholesaleFields) {
        console.log('❌ عناصر نوع العميل غير موجودة');
        return;
    }
    
    // اختبار التبديل إلى عميل جملة
    console.log('1️⃣ التبديل إلى عميل جملة...');
    wholesaleRadio.checked = true;
    wholesaleRadio.dispatchEvent(new Event('change'));
    
    setTimeout(() => {
        const isVisible = wholesaleFields.style.display !== 'none';
        console.log(`   - حقول الجملة ظاهرة: ${isVisible ? '✅' : '❌'}`);
        
        // اختبار التبديل إلى عميل مفرد
        console.log('2️⃣ التبديل إلى عميل مفرد...');
        retailRadio.checked = true;
        retailRadio.dispatchEvent(new Event('change'));
        
        setTimeout(() => {
            const isHidden = wholesaleFields.style.display === 'none';
            console.log(`   - حقول الجملة مخفية: ${isHidden ? '✅' : '❌'}`);
        }, 100);
    }, 100);
}

// اختبار التحقق من صحة البيانات
function testValidation() {
    console.log('\n✅ اختبار التحقق من صحة البيانات...');
    
    // اختبار عميل مفرد
    console.log('1️⃣ اختبار عميل مفرد...');
    const retailValid = validateSignupForm(
        'أحمد محمد',           // name
        '<EMAIL>',   // email
        '***********',         // phone
        'password123',         // password
        'password123',         // confirmPassword
        false,                 // isWholesaleCustomer
        '',                    // businessName
        '',                    // businessAddress
        '',                    // businessCity
        ''                     // businessState
    );
    console.log(`   - صحة البيانات: ${retailValid ? '✅' : '❌'}`);
    
    // اختبار عميل جملة مع بيانات كاملة
    console.log('2️⃣ اختبار عميل جملة مع بيانات كاملة...');
    const wholesaleValidComplete = validateSignupForm(
        'محمد علي',            // name
        '<EMAIL>',   // email
        '***********',         // phone
        'password123',         // password
        'password123',         // confirmPassword
        true,                  // isWholesaleCustomer
        'محل قطع غيار السيارات', // businessName
        'شارع الرشيد - بناية 15', // businessAddress
        'بغداد',               // businessCity
        'بغداد'                // businessState
    );
    console.log(`   - صحة البيانات: ${wholesaleValidComplete ? '✅' : '❌'}`);
    
    // اختبار عميل جملة مع بيانات ناقصة
    console.log('3️⃣ اختبار عميل جملة مع بيانات ناقصة...');
    const wholesaleValidIncomplete = validateSignupForm(
        'سعد أحمد',            // name
        '<EMAIL>',       // email
        '***********',         // phone
        'password123',         // password
        'password123',         // confirmPassword
        true,                  // isWholesaleCustomer
        '',                    // businessName - فارغ
        '',                    // businessAddress - فارغ
        '',                    // businessCity - فارغ
        ''                     // businessState - فارغ
    );
    console.log(`   - صحة البيانات: ${wholesaleValidIncomplete ? '❌ خطأ' : '✅ رفض صحيح'}`);
}

// محاكاة تسجيل عميل مفرد
function simulateRetailCustomerSignup() {
    console.log('\n🛒 محاكاة تسجيل عميل مفرد...');
    
    // ملء النموذج
    document.getElementById('signupName').value = 'علي حسن';
    document.getElementById('signupEmail').value = '<EMAIL>';
    document.getElementById('signupPhone').value = '***********';
    document.getElementById('signupPassword').value = 'password123';
    document.getElementById('signupConfirmPassword').value = 'password123';
    
    // اختيار عميل مفرد
    document.getElementById('retailCustomer').checked = true;
    document.getElementById('wholesaleCustomer').checked = false;
    
    console.log('✅ تم ملء بيانات عميل مفرد');
    console.log('📋 البيانات:');
    console.log('- الاسم: علي حسن');
    console.log('- البريد: <EMAIL>');
    console.log('- الهاتف: ***********');
    console.log('- نوع العميل: مفرد');
}

// محاكاة تسجيل عميل جملة
function simulateWholesaleCustomerSignup() {
    console.log('\n🏪 محاكاة تسجيل عميل جملة...');
    
    // ملء النموذج الأساسي
    document.getElementById('signupName').value = 'حسام التاجر';
    document.getElementById('signupEmail').value = '<EMAIL>';
    document.getElementById('signupPhone').value = '***********';
    document.getElementById('signupPassword').value = 'password123';
    document.getElementById('signupConfirmPassword').value = 'password123';
    
    // اختيار عميل جملة
    document.getElementById('wholesaleCustomer').checked = true;
    document.getElementById('retailCustomer').checked = false;
    
    // تفعيل حقول الجملة
    document.getElementById('wholesaleCustomer').dispatchEvent(new Event('change'));
    
    setTimeout(() => {
        // ملء حقول الجملة
        document.getElementById('businessName').value = 'مؤسسة الرافدين لقطع الغيار';
        document.getElementById('businessAddress').value = 'شارع الكفاح - مجمع الكرادة التجاري';
        document.getElementById('businessCity').value = 'بغداد';
        document.getElementById('businessState').value = 'بغداد';
        
        console.log('✅ تم ملء بيانات عميل جملة');
        console.log('📋 البيانات:');
        console.log('- الاسم: حسام التاجر');
        console.log('- البريد: <EMAIL>');
        console.log('- الهاتف: ***********');
        console.log('- نوع العميل: جملة');
        console.log('- اسم المحل: مؤسسة الرافدين لقطع الغيار');
        console.log('- العنوان: شارع الكفاح - مجمع الكرادة التجاري');
        console.log('- المدينة: بغداد');
        console.log('- المحافظة: بغداد');
    }, 200);
}

// اختبار وظائف التطبيق
function testApplicationFunctions() {
    console.log('\n🔧 اختبار وظائف التطبيق...');
    
    const functions = [
        'initializeCustomerTypeSelector',
        'clearWholesaleFields',
        'validateSignupForm',
        'signup',
        'clearSignupForm'
    ];
    
    functions.forEach(funcName => {
        if (typeof window[funcName] === 'function') {
            console.log(`✅ ${funcName} موجودة`);
        } else {
            console.log(`❌ ${funcName} غير موجودة`);
        }
    });
}

// اختبار شامل
function runAllCustomerTypeTests() {
    console.log('🚀 بدء جميع اختبارات نوع العميل...');
    
    // فحص العناصر والوظائف
    checkCustomerTypeElements();
    testApplicationFunctions();
    
    // اختبار التفاعل
    testCustomerTypeToggle();
    testValidation();
    
    // محاكاة التسجيل
    setTimeout(() => {
        simulateRetailCustomerSignup();
        
        setTimeout(() => {
            simulateWholesaleCustomerSignup();
        }, 2000);
    }, 1000);
    
    console.log('\n✅ انتهت جميع الاختبارات');
}

// تشغيل الاختبارات
runAllCustomerTypeTests();

console.log('\n📋 الاختبارات المتاحة:');
console.log('- checkCustomerTypeElements() - فحص العناصر');
console.log('- testCustomerTypeToggle() - اختبار التبديل');
console.log('- testValidation() - اختبار التحقق');
console.log('- simulateRetailCustomerSignup() - محاكاة تسجيل عميل مفرد');
console.log('- simulateWholesaleCustomerSignup() - محاكاة تسجيل عميل جملة');
console.log('- testApplicationFunctions() - اختبار الوظائف');
console.log('- runAllCustomerTypeTests() - تشغيل جميع الاختبارات');

console.log('\n🎯 المتوقع بعد التحديث:');
console.log('✅ خيار اختيار نوع العميل (مفرد/جملة)');
console.log('✅ حقول إضافية لعملاء الجملة');
console.log('✅ تحقق من صحة البيانات حسب نوع العميل');
console.log('✅ رسائل مختلفة حسب نوع العميل');
console.log('✅ إرسال البيانات الصحيحة للخادم');
