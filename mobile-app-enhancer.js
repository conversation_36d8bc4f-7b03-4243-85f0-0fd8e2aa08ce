/**
 * Mobile App Enhancer - تحسين تطبيق الموبايل لتحميل جميع المنتجات
 */
(function() {
    'use strict';
    
    console.log('🚀 تحميل محسن تطبيق الموبايل');
    
    const CONFIG = {
        API_BASE_URL: window.location.origin,
        MAX_PAGES: 50,
        PRODUCTS_PER_PAGE: 100,
        DELAY: 150
    };
    
    let productsCache = { data: [], timestamp: 0, isLoading: false };
    
    async function loadAllProductsWithPagination() {
        if (productsCache.isLoading) return productsCache.data;
        
        productsCache.isLoading = true;
        console.log('🔄 بدء تحميل جميع المنتجات...');
        
        try {
            const allProducts = [];
            let currentPage = 1;
            let hasMorePages = true;
            
            while (hasMorePages && currentPage <= CONFIG.MAX_PAGES) {
                console.log(`📄 تحميل الصفحة ${currentPage}...`);
                
                const url = `${CONFIG.API_BASE_URL}/ecommerce/products?page=${currentPage}&per_page=${CONFIG.PRODUCTS_PER_PAGE}`;
                const response = await fetch(url);
                
                if (!response.ok) throw new Error(`HTTP ${response.status}`);
                
                const data = await response.json();
                
                if (data.error === false && data.data) {
                    const products = data.data.data || data.data;
                    
                    if (Array.isArray(products) && products.length > 0) {
                        allProducts.push(...products);
                        console.log(`✅ صفحة ${currentPage}: ${products.length} منتج (إجمالي: ${allProducts.length})`);
                        
                        if (data.data.current_page && data.data.last_page) {
                            hasMorePages = data.data.current_page < data.data.last_page;
                        } else if (products.length < CONFIG.PRODUCTS_PER_PAGE) {
                            hasMorePages = false;
                        }
                        
                        currentPage++;
                        if (hasMorePages) await new Promise(r => setTimeout(r, CONFIG.DELAY));
                    } else {
                        hasMorePages = false;
                    }
                } else {
                    hasMorePages = false;
                }
            }
            
            productsCache.data = allProducts;
            productsCache.timestamp = Date.now();
            
            console.log(`🎉 انتهى التحميل! إجمالي المنتجات: ${allProducts.length}`);
            
            // تحديث الإحصائيات
            const totalEl = document.getElementById('totalProducts');
            if (totalEl) totalEl.textContent = allProducts.length.toLocaleString();
            
            return allProducts;
            
        } catch (error) {
            console.error('❌ فشل في تحميل المنتجات:', error);
            return [];
        } finally {
            productsCache.isLoading = false;
        }
    }
    
    // تحسين وظائف البحث
    function enhanceSearchFunctions() {
        if (typeof window.searchProducts === 'function') {
            const originalSearch = window.searchProducts;
            window.searchProducts = async function(query) {
                const allProducts = await loadAllProductsWithPagination();
                if (allProducts.length > 0) {
                    return allProducts.filter(product => 
                        product.name.toLowerCase().includes(query.toLowerCase())
                    );
                }
                return originalSearch(query);
            };
        }
    }
    
    // تهيئة المحسن
    function init() {
        enhanceSearchFunctions();
        setTimeout(loadAllProductsWithPagination, 3000);
    }
    
    // تصدير للاستخدام الخارجي
    window.MobileAppEnhancer = {
        loadAllProducts: loadAllProductsWithPagination,
        getCache: () => productsCache
    };
    
    // بدء التحسين
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }
    
    console.log('✅ تم تحميل محسن تطبيق الموبايل');
})();
