// اختبار مبسط للـ APIs
const axios = require('axios');

async function testAPI() {
    try {
        console.log('Testing API connection...');

        // اختبار API مباشر
        const response = await axios.get(
            'http://0.0.0.0:8080/api/v1/ecommerce/products',
            {
                timeout: 5000,
                headers: {
                    Accept: 'application/json',
                    'Content-Type': 'application/json',
                },
            },
        );

        console.log('✅ API Response Status:', response.status);
        console.log('📊 Data:', response.data);
    } catch (error) {
        console.log('❌ Error:', error.message);
        if (error.response) {
            console.log('Status:', error.response.status);
            console.log('Data:', error.response.data);
        }
    }
}

testAPI();
