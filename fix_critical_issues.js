/**
 * 🔧 إصلاح المشاكل الحرجة في مشروع دليل كار
 * Critical Issues Fix for Dalila Car Project
 */

(function() {
    'use strict';
    
    console.log('🔧 بدء إصلاح المشاكل الحرجة...');
    
    // ================================
    // 1. إصلاح مشاكل APIs
    // ================================
    
    /**
     * إصلاح معالجة الأخطاء في APIs
     */
    function fixAPIErrorHandling() {
        console.log('🔧 إصلاح معالجة أخطاء APIs...');
        
        // إنشاء wrapper محسن للـ fetch
        window.safeFetch = async function(url, options = {}) {
            const defaultOptions = {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                    'User-Agent': 'DalilaApp/1.0'
                },
                timeout: 15000
            };
            
            const finalOptions = { ...defaultOptions, ...options };
            
            try {
                console.log(`🔗 API Request: ${finalOptions.method} ${url}`);
                
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), finalOptions.timeout);
                
                const response = await fetch(url, {
                    ...finalOptions,
                    signal: controller.signal
                });
                
                clearTimeout(timeoutId);
                
                console.log(`📡 API Response: ${response.status} ${url}`);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                return {
                    success: true,
                    data: data,
                    status: response.status
                };
                
            } catch (error) {
                console.error(`❌ API Error for ${url}:`, error.message);
                
                return {
                    success: false,
                    error: error.message,
                    status: error.name === 'AbortError' ? 408 : 0
                };
            }
        };
        
        console.log('✅ تم إصلاح معالجة أخطاء APIs');
    }
    
    /**
     * إصلاح مشكلة Countries API
     */
    function fixCountriesAPI() {
        console.log('🔧 إصلاح مشكلة Countries API...');
        
        // استخدام API بديل للدول
        window.getCountriesAlternative = async function() {
            const alternatives = [
                'https://dalilakauto.com/api/v1/location/countries',
                'https://restcountries.com/v3.1/all?fields=name,cca2',
                'https://api.first.org/data/v1/countries'
            ];
            
            for (const apiUrl of alternatives) {
                try {
                    const result = await window.safeFetch(apiUrl);
                    if (result.success) {
                        console.log(`✅ نجح Countries API البديل: ${apiUrl}`);
                        return result.data;
                    }
                } catch (error) {
                    console.log(`❌ فشل Countries API البديل: ${apiUrl}`);
                }
            }
            
            // إرجاع بيانات افتراضية
            console.log('📋 استخدام بيانات الدول الافتراضية');
            return [
                { id: 1, name: 'العراق', code: 'IQ' },
                { id: 2, name: 'السعودية', code: 'SA' },
                { id: 3, name: 'الكويت', code: 'KW' },
                { id: 4, name: 'الإمارات', code: 'AE' },
                { id: 5, name: 'قطر', code: 'QA' },
                { id: 6, name: 'البحرين', code: 'BH' },
                { id: 7, name: 'عمان', code: 'OM' }
            ];
        };
        
        console.log('✅ تم إصلاح مشكلة Countries API');
    }
    
    // ================================
    // 2. إصلاح مشاكل تحميل المنتجات
    // ================================
    
    /**
     * إصلاح نظام تحميل المنتجات
     */
    function fixProductLoading() {
        console.log('🔧 إصلاح نظام تحميل المنتجات...');
        
        // نظام تحميل محسن مع pagination
        window.optimizedProductLoader = {
            cache: new Map(),
            loading: false,
            
            async loadProducts(page = 1, perPage = 20, useCache = true) {
                const cacheKey = `products_${page}_${perPage}`;
                
                // فحص الكاش أولاً
                if (useCache && this.cache.has(cacheKey)) {
                    console.log(`📦 تحميل من الكاش: صفحة ${page}`);
                    return this.cache.get(cacheKey);
                }
                
                if (this.loading) {
                    console.log('⏳ التحميل قيد التقدم...');
                    return null;
                }
                
                this.loading = true;
                
                try {
                    const url = `https://dalilakauto.com/api/v1/ecommerce/products?page=${page}&per_page=${perPage}`;
                    const result = await window.safeFetch(url);
                    
                    if (result.success) {
                        // حفظ في الكاش
                        this.cache.set(cacheKey, result.data);
                        
                        // تنظيف الكاش إذا أصبح كبيراً
                        if (this.cache.size > 50) {
                            const firstKey = this.cache.keys().next().value;
                            this.cache.delete(firstKey);
                        }
                        
                        console.log(`✅ تم تحميل صفحة ${page}: ${result.data.data?.length || 0} منتج`);
                        return result.data;
                    } else {
                        throw new Error(result.error);
                    }
                } catch (error) {
                    console.error(`❌ فشل في تحميل المنتجات صفحة ${page}:`, error);
                    return null;
                } finally {
                    this.loading = false;
                }
            },
            
            clearCache() {
                this.cache.clear();
                console.log('🗑️ تم مسح كاش المنتجات');
            }
        };
        
        console.log('✅ تم إصلاح نظام تحميل المنتجات');
    }
    
    // ================================
    // 3. إصلاح مشاكل الأمان
    // ================================
    
    /**
     * إضافة validation للمدخلات
     */
    function addInputValidation() {
        console.log('🔧 إضافة validation للمدخلات...');
        
        window.validateInput = {
            email(email) {
                const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                return regex.test(email);
            },
            
            phone(phone) {
                const regex = /^[\+]?[1-9][\d]{0,15}$/;
                return regex.test(phone.replace(/\s/g, ''));
            },
            
            password(password) {
                return password && password.length >= 6;
            },
            
            name(name) {
                return name && name.trim().length >= 2;
            },
            
            sanitizeHTML(str) {
                const div = document.createElement('div');
                div.textContent = str;
                return div.innerHTML;
            },
            
            sanitizeInput(input) {
                if (typeof input !== 'string') return input;
                return input.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
                           .replace(/javascript:/gi, '')
                           .replace(/on\w+\s*=/gi, '');
            }
        };
        
        console.log('✅ تم إضافة validation للمدخلات');
    }
    
    // ================================
    // 4. إصلاح مشاكل الأداء
    // ================================
    
    /**
     * إضافة lazy loading للصور
     */
    function addLazyLoading() {
        console.log('🔧 إضافة lazy loading للصور...');
        
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        observer.unobserve(img);
                    }
                });
            });
            
            // مراقبة جميع الصور الموجودة
            document.querySelectorAll('img[data-src]').forEach(img => {
                imageObserver.observe(img);
            });
            
            // مراقبة الصور الجديدة
            window.observeNewImages = function() {
                document.querySelectorAll('img[data-src]:not(.observed)').forEach(img => {
                    img.classList.add('observed');
                    imageObserver.observe(img);
                });
            };
        }
        
        console.log('✅ تم إضافة lazy loading للصور');
    }
    
    // ================================
    // 5. إصلاح مشاكل UI/UX
    // ================================
    
    /**
     * إصلاح مشاكل التنقل
     */
    function fixNavigation() {
        console.log('🔧 إصلاح مشاكل التنقل...');
        
        // توحيد نظام التنقل
        window.navigationManager = {
            currentPage: 'home',
            history: ['home'],
            
            navigateTo(page, addToHistory = true) {
                if (this.currentPage === page) return;
                
                // إخفاء الصفحة الحالية
                const currentPageElement = document.getElementById(this.currentPage + 'Page') || 
                                         document.querySelector(`.screen.active`);
                if (currentPageElement) {
                    currentPageElement.classList.remove('active');
                    currentPageElement.style.display = 'none';
                }
                
                // إظهار الصفحة الجديدة
                const newPageElement = document.getElementById(page + 'Page') || 
                                      document.getElementById(page);
                if (newPageElement) {
                    newPageElement.classList.add('active');
                    newPageElement.style.display = 'block';
                }
                
                // تحديث navigation
                document.querySelectorAll('.nav-item').forEach(item => {
                    item.classList.remove('active');
                });
                
                const activeNavItem = document.querySelector(`[onclick*="${page}"]`);
                if (activeNavItem) {
                    activeNavItem.classList.add('active');
                }
                
                // تحديث التاريخ
                if (addToHistory) {
                    this.history.push(page);
                    if (this.history.length > 10) {
                        this.history.shift();
                    }
                }
                
                this.currentPage = page;
                console.log(`📱 تم التنقل إلى: ${page}`);
            },
            
            goBack() {
                if (this.history.length > 1) {
                    this.history.pop(); // إزالة الصفحة الحالية
                    const previousPage = this.history[this.history.length - 1];
                    this.navigateTo(previousPage, false);
                }
            }
        };
        
        // ربط الدوال الموجودة بالنظام الجديد
        window.showPage = function(page) {
            window.navigationManager.navigateTo(page);
        };
        
        console.log('✅ تم إصلاح مشاكل التنقل');
    }
    
    // ================================
    // تشغيل جميع الإصلاحات
    // ================================
    
    function runAllFixes() {
        console.log('🚀 تشغيل جميع الإصلاحات...');
        
        try {
            fixAPIErrorHandling();
            fixCountriesAPI();
            fixProductLoading();
            addInputValidation();
            addLazyLoading();
            fixNavigation();
            
            console.log('✅ تم تطبيق جميع الإصلاحات بنجاح!');
            
            // إظهار رسالة نجاح
            if (typeof showToast === 'function') {
                showToast('🎉 تم إصلاح جميع المشاكل الحرجة!');
            }
            
        } catch (error) {
            console.error('❌ خطأ في تطبيق الإصلاحات:', error);
        }
    }
    
    // تشغيل الإصلاحات عند تحميل الصفحة
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', runAllFixes);
    } else {
        runAllFixes();
    }
    
    console.log('✅ تم تحميل ملف إصلاح المشاكل الحرجة');
    
})();
