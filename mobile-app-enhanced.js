// Enhanced Mobile App JavaScript for Loading All Products

// Configuration
const API_BASE_URL = window.location.origin;
let allProducts = [];
let currentPage = 1;
let isLoading = false;
let hasMorePages = true;

// Function to load all products with pagination
async function loadAllProductsWithPagination() {
    console.log('🚀 بدء تحميل جميع المنتجات...');
    
    allProducts = [];
    currentPage = 1;
    hasMorePages = true;
    
    while (hasMorePages && currentPage <= 100) { // حد أقصى 100 صفحة للأمان
        try {
            console.log(`📄 تحميل الصفحة ${currentPage}...`);
            
            const response = await fetch(`${API_BASE_URL}/ecommerce/products?page=${currentPage}&per_page=100`);
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const data = await response.json();
            console.log(`✅ تم تحميل الصفحة ${currentPage}:`, data);
            
            if (data.error === false && data.data && data.data.data) {
                const products = data.data.data;
                allProducts = allProducts.concat(products);
                
                // Check if there are more pages
                const pagination = data.data;
                hasMorePages = pagination.current_page < pagination.last_page;
                currentPage++;
                
                console.log(`📊 تم تحميل ${products.length} منتج من الصفحة ${currentPage - 1}`);
                console.log(`📈 إجمالي المنتجات المحملة: ${allProducts.length}`);
                
                if (!hasMorePages) {
                    console.log('✅ تم تحميل جميع المنتجات!');
                    break;
                }
                
                // Add small delay to avoid overwhelming the server
                await new Promise(resolve => setTimeout(resolve, 100));
                
            } else {
                console.log('❌ لا توجد بيانات في الاستجابة');
                hasMorePages = false;
            }
            
        } catch (error) {
            console.error(`❌ خطأ في تحميل الصفحة ${currentPage}:`, error);
            hasMorePages = false;
        }
    }
    
    console.log(`🎉 انتهى التحميل! إجمالي المنتجات: ${allProducts.length}`);
    return allProducts;
}

// Function to update the original mobile-app.html functions
function enhanceMobileApp() {
    // Override the original loadProducts function
    if (typeof window.loadProducts === 'function') {
        const originalLoadProducts = window.loadProducts;
        
        window.loadProducts = async function() {
            console.log('🔄 استخدام النسخة المحسنة لتحميل المنتجات...');
            
            try {
                const products = await loadAllProductsWithPagination();
                
                // Update the products display
                if (products.length > 0) {
                    // Update stats
                    const totalProductsEl = document.getElementById('totalProducts');
                    if (totalProductsEl) {
                        totalProductsEl.textContent = products.length.toLocaleString();
                    }
                    
                    // Store products globally for other functions to use
                    window.allLoadedProducts = products;
                    
                    console.log('✅ تم تحديث عرض المنتجات');
                    return products;
                } else {
                    console.log('⚠️ لم يتم العثور على منتجات');
                    return [];
                }
                
            } catch (error) {
                console.error('❌ فشل في تحميل المنتجات:', error);
                // Fallback to original function
                return originalLoadProducts();
            }
        };
    }
    
    console.log('✅ تم تحسين تطبيق الموبايل لتحميل جميع المنتجات');
}

// Auto-enhance when script loads
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', enhanceMobileApp);
} else {
    enhanceMobileApp();
}

// Export for manual use
window.loadAllProductsWithPagination = loadAllProductsWithPagination;
window.enhanceMobileApp = enhanceMobileApp;
