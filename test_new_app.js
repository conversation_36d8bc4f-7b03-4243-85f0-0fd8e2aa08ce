// 🧪 اختبار التطبيق الجديد المحسن
const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

class AppTester {
    constructor() {
        this.browser = null;
        this.page = null;
        this.results = {
            passed: 0,
            failed: 0,
            tests: []
        };
    }

    async init() {
        console.log('🚀 بدء اختبار التطبيق الجديد...');
        
        this.browser = await puppeteer.launch({
            headless: false,
            args: ['--no-sandbox', '--disable-setuid-sandbox']
        });
        
        this.page = await this.browser.newPage();
        await this.page.setViewport({ width: 375, height: 667 }); // iPhone size
        
        // تمكين console logs
        this.page.on('console', msg => {
            console.log('📱 Console:', msg.text());
        });
        
        // تمكين error handling
        this.page.on('pageerror', error => {
            console.error('❌ Page Error:', error.message);
        });
    }

    async test(name, testFunction) {
        try {
            console.log(`🧪 اختبار: ${name}`);
            await testFunction();
            this.results.passed++;
            this.results.tests.push({ name, status: 'PASSED', error: null });
            console.log(`✅ نجح: ${name}`);
        } catch (error) {
            this.results.failed++;
            this.results.tests.push({ name, status: 'FAILED', error: error.message });
            console.error(`❌ فشل: ${name} - ${error.message}`);
        }
    }

    async runAllTests() {
        await this.init();

        // اختبار تحميل الصفحة الأساسية
        await this.test('تحميل الصفحة الرئيسية', async () => {
            await this.page.goto('http://localhost:8080/index-new.html');
            await this.page.waitForSelector('.app-container', { timeout: 10000 });
            
            const title = await this.page.title();
            if (!title.includes('دليل كار')) {
                throw new Error('عنوان الصفحة غير صحيح');
            }
        });

        // اختبار تحميل CSS
        await this.test('تحميل ملفات CSS', async () => {
            const cssLoaded = await this.page.evaluate(() => {
                const links = document.querySelectorAll('link[rel="stylesheet"]');
                return links.length >= 2; // app.css + components.css
            });
            
            if (!cssLoaded) {
                throw new Error('ملفات CSS لم يتم تحميلها');
            }
        });

        // اختبار تحميل JavaScript
        await this.test('تحميل ملفات JavaScript', async () => {
            const jsLoaded = await this.page.evaluate(() => {
                return typeof window.AppConfig !== 'undefined' && 
                       typeof window.apiService !== 'undefined' &&
                       typeof window.DalilaCarApp !== 'undefined';
            });
            
            if (!jsLoaded) {
                throw new Error('ملفات JavaScript لم يتم تحميلها بشكل صحيح');
            }
        });

        // اختبار إخفاء شاشة التحميل
        await this.test('إخفاء شاشة التحميل', async () => {
            await this.page.waitForFunction(() => {
                const loadingScreen = document.getElementById('loadingScreen');
                return loadingScreen && loadingScreen.style.display === 'none';
            }, { timeout: 5000 });
        });

        // اختبار عرض التطبيق
        await this.test('عرض التطبيق الرئيسي', async () => {
            const appVisible = await this.page.evaluate(() => {
                const appContainer = document.getElementById('appContainer');
                return appContainer && appContainer.style.display !== 'none';
            });
            
            if (!appVisible) {
                throw new Error('التطبيق الرئيسي غير مرئي');
            }
        });

        // اختبار الهيدر
        await this.test('عرض الهيدر', async () => {
            await this.page.waitForSelector('.app-header');
            
            const headerElements = await this.page.evaluate(() => {
                return {
                    logo: !!document.querySelector('.logo'),
                    search: !!document.querySelector('.search-container'),
                    cart: !!document.querySelector('.cart-button')
                };
            });
            
            if (!headerElements.logo || !headerElements.search || !headerElements.cart) {
                throw new Error('عناصر الهيدر مفقودة');
            }
        });

        // اختبار التنقل السفلي
        await this.test('عرض التنقل السفلي', async () => {
            await this.page.waitForSelector('.bottom-nav');
            
            const navItems = await this.page.$$('.nav-item');
            if (navItems.length !== 4) {
                throw new Error('عدد عناصر التنقل غير صحيح');
            }
        });

        // اختبار التنقل بين الصفحات
        await this.test('التنقل بين الصفحات', async () => {
            // النقر على صفحة المنتجات
            await this.page.click('[data-nav="products"]');
            await this.page.waitForSelector('#page-products.active');
            
            // النقر على صفحة الفئات
            await this.page.click('[data-nav="categories"]');
            await this.page.waitForSelector('#page-categories.active');
            
            // العودة للصفحة الرئيسية
            await this.page.click('[data-nav="home"]');
            await this.page.waitForSelector('#page-home.active');
        });

        // اختبار البحث
        await this.test('وظيفة البحث', async () => {
            await this.page.type('#searchInput', 'فلتر');
            await this.page.click('#searchButton');
            
            // انتظار الانتقال لصفحة البحث
            await this.page.waitForSelector('#page-search.active', { timeout: 5000 });
        });

        // اختبار API Service
        await this.test('خدمة API', async () => {
            const apiWorking = await this.page.evaluate(async () => {
                try {
                    // اختبار جلب الفئات
                    const categories = await window.apiService.getCategories();
                    return categories && (categories.data || categories.error);
                } catch (error) {
                    return false;
                }
            });
            
            if (!apiWorking) {
                throw new Error('خدمة API لا تعمل');
            }
        });

        // اختبار Service Worker
        await this.test('Service Worker', async () => {
            const swRegistered = await this.page.evaluate(() => {
                return 'serviceWorker' in navigator;
            });
            
            if (!swRegistered) {
                throw new Error('Service Worker غير مدعوم');
            }
        });

        // اختبار PWA Manifest
        await this.test('PWA Manifest', async () => {
            const manifestLink = await this.page.$('link[rel="manifest"]');
            if (!manifestLink) {
                throw new Error('PWA Manifest مفقود');
            }
        });

        // اختبار الاستجابة للشاشات المختلفة
        await this.test('التصميم المتجاوب', async () => {
            // اختبار شاشة الكمبيوتر
            await this.page.setViewport({ width: 1200, height: 800 });
            await this.page.waitForTimeout(1000);
            
            // اختبار شاشة التابلت
            await this.page.setViewport({ width: 768, height: 1024 });
            await this.page.waitForTimeout(1000);
            
            // العودة لشاشة الهاتف
            await this.page.setViewport({ width: 375, height: 667 });
            await this.page.waitForTimeout(1000);
        });

        // اختبار الأداء
        await this.test('أداء التطبيق', async () => {
            const metrics = await this.page.metrics();
            
            if (metrics.JSHeapUsedSize > 50 * 1024 * 1024) { // 50MB
                throw new Error('استهلاك ذاكرة مفرط');
            }
            
            if (metrics.ScriptDuration > 5000) { // 5 ثواني
                throw new Error('وقت تنفيذ JavaScript مفرط');
            }
        });

        await this.generateReport();
        await this.cleanup();
    }

    async generateReport() {
        const report = {
            timestamp: new Date().toISOString(),
            summary: {
                total: this.results.passed + this.results.failed,
                passed: this.results.passed,
                failed: this.results.failed,
                successRate: Math.round((this.results.passed / (this.results.passed + this.results.failed)) * 100)
            },
            tests: this.results.tests,
            environment: {
                userAgent: await this.page.evaluate(() => navigator.userAgent),
                viewport: await this.page.viewport(),
                url: this.page.url()
            }
        };

        // حفظ التقرير
        const reportPath = path.join(__dirname, 'test-report-new-app.json');
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));

        // طباعة الملخص
        console.log('\n📊 ملخص نتائج الاختبار:');
        console.log(`✅ نجح: ${report.summary.passed}`);
        console.log(`❌ فشل: ${report.summary.failed}`);
        console.log(`📈 معدل النجاح: ${report.summary.successRate}%`);
        console.log(`📄 تقرير مفصل: ${reportPath}`);

        if (report.summary.failed > 0) {
            console.log('\n❌ الاختبارات الفاشلة:');
            report.tests.filter(t => t.status === 'FAILED').forEach(test => {
                console.log(`  - ${test.name}: ${test.error}`);
            });
        }
    }

    async cleanup() {
        if (this.browser) {
            await this.browser.close();
        }
    }
}

// تشغيل الاختبارات
async function runTests() {
    const tester = new AppTester();
    
    try {
        await tester.runAllTests();
    } catch (error) {
        console.error('❌ خطأ في تشغيل الاختبارات:', error);
        await tester.cleanup();
        process.exit(1);
    }
}

// تشغيل الاختبارات إذا تم استدعاء الملف مباشرة
if (require.main === module) {
    runTests();
}

module.exports = AppTester;
