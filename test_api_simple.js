/**
 * اختبار بسيط لـ API
 */

const API_BASE_URL = 'https://dalilakauto.com/api/v1';

async function testAPI() {
    console.log('🧪 اختبار API...');
    
    try {
        const response = await fetch(`${API_BASE_URL}/ecommerce/products?page=1&per_page=5`);
        console.log('Response Status:', response.status);
        console.log('Response OK:', response.ok);
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
        }
        
        const data = await response.json();
        console.log('✅ API يعمل بشكل صحيح');
        console.log('عدد المنتجات:', data.data?.length || 0);
        console.log('إجمالي المنتجات:', data.meta?.total || 'غير محدد');
        
        if (data.data && data.data.length > 0) {
            console.log('مثال منتج:', data.data[0].name);
        }
        
        return data;
        
    } catch (error) {
        console.error('❌ خطأ في API:', error.message);
        return null;
    }
}

// تشغيل الاختبار
testAPI();
