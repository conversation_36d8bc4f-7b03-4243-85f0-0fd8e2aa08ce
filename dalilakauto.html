<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>دليلك أوتو - قطع غيار السيارات</title>
    
    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#667eea">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="دليلك أوتو">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            direction: rtl;
        }
        
        .container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
            max-width: 400px;
            width: 90%;
        }
        
        .logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 20px;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            font-weight: bold;
        }
        
        h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 28px;
        }
        
        .subtitle {
            color: #666;
            margin-bottom: 30px;
            font-size: 16px;
        }
        
        .features {
            text-align: right;
            margin: 30px 0;
        }
        
        .feature {
            display: flex;
            align-items: center;
            margin: 15px 0;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 10px;
        }
        
        .feature-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            margin-left: 15px;
            font-size: 18px;
        }
        
        .download-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 50px;
            font-size: 18px;
            font-weight: bold;
            cursor: pointer;
            width: 100%;
            margin-top: 20px;
            transition: transform 0.2s;
        }
        
        .download-btn:hover {
            transform: translateY(-2px);
        }
        
        .info {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
            font-size: 14px;
            color: #1976d2;
        }
        
        .steps {
            text-align: right;
            margin-top: 20px;
        }
        
        .step {
            margin: 10px 0;
            padding: 10px;
            background: #f0f8ff;
            border-radius: 8px;
            border-right: 4px solid #667eea;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">دليلك</div>
        <h1>دليلك أوتو</h1>
        <p class="subtitle">تطبيق قطع غيار السيارات</p>
        
        <div class="features">
            <div class="feature">
                <div class="feature-icon">🔍</div>
                <div>
                    <strong>بحث متقدم</strong><br>
                    <small>بحث هرمي بالعلامة والموديل والسنة</small>
                </div>
            </div>
            
            <div class="feature">
                <div class="feature-icon">🛒</div>
                <div>
                    <strong>تسوق سهل</strong><br>
                    <small>إضافة للسلة وقائمة الأمنيات</small>
                </div>
            </div>
            
            <div class="feature">
                <div class="feature-icon">📱</div>
                <div>
                    <strong>تطبيق محمول</strong><br>
                    <small>يعمل على جميع الأجهزة</small>
                </div>
            </div>
        </div>
        
        <button class="download-btn" onclick="downloadAPK()">
            📱 تحميل التطبيق (APK)
        </button>
        
        <div class="info">
            <strong>📋 خطوات إنشاء APK:</strong>
            <div class="steps">
                <div class="step">1. انتقل إلى PWABuilder.com</div>
                <div class="step">2. أدخل رابط هذه الصفحة</div>
                <div class="step">3. اختر Android واضغط Download</div>
                <div class="step">4. ثبت APK على هاتفك</div>
            </div>
        </div>
    </div>

    <script>
        // PWA Manifest
        const manifest = {
            "name": "دليلك أوتو - قطع غيار السيارات",
            "short_name": "دليلك أوتو",
            "description": "تطبيق شامل لبيع قطع غيار السيارات",
            "start_url": "./",
            "display": "standalone",
            "orientation": "portrait",
            "theme_color": "#667eea",
            "background_color": "#ffffff",
            "lang": "ar",
            "dir": "rtl",
            "icons": [
                {
                    "src": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTEyIiBoZWlnaHQ9IjUxMiIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZGVmcz48bGluZWFyR3JhZGllbnQgaWQ9ImEiIHgxPSIwJSIgeTE9IjAlIiB4Mj0iMTAwJSIgeTI9IjEwMCUiPjxzdG9wIG9mZnNldD0iMCUiIHN0b3AtY29sb3I9IiM2NjdlZWEiLz48c3RvcCBvZmZzZXQ9IjEwMCUiIHN0b3AtY29sb3I9IiM3NjRiYTIiLz48L2xpbmVhckdyYWRpZW50PjwvZGVmcz48cmVjdCB3aWR0aD0iNTEyIiBoZWlnaHQ9IjUxMiIgZmlsbD0idXJsKCNhKSIgcng9IjY0Ii8+PHRleHQgeD0iMjU2IiB5PSIyODAiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxMjgiIGZvbnQtd2VpZ2h0PSJib2xkIiBmaWxsPSIjZmZmIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj7Yr9mE2YrZhDwvdGV4dD48dGV4dCB4PSIyNTYiIHk9IjM2MCIgZm9udC1mYW1pbHk9IkFyaWFsLCBzYW5zLXNlcmlmIiBmb250LXNpemU9IjQ4IiBmaWxsPSIjZmZmIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj7ZgtmC2Lkg2LrZitin2LE8L3RleHQ+PC9zdmc+",
                    "sizes": "512x512",
                    "type": "image/svg+xml"
                }
            ]
        };

        // Create manifest blob and link
        const manifestBlob = new Blob([JSON.stringify(manifest)], {type: 'application/json'});
        const manifestURL = URL.createObjectURL(manifestBlob);
        
        // Add manifest link to head
        const link = document.createElement('link');
        link.rel = 'manifest';
        link.href = manifestURL;
        document.head.appendChild(link);

        // Service Worker
        const swCode = `
            const CACHE_NAME = 'dalilakauto-v1';
            const urlsToCache = ['/'];
            
            self.addEventListener('install', event => {
                event.waitUntil(
                    caches.open(CACHE_NAME)
                        .then(cache => cache.addAll(urlsToCache))
                );
            });
            
            self.addEventListener('fetch', event => {
                event.respondWith(
                    caches.match(event.request)
                        .then(response => response || fetch(event.request))
                );
            });
        `;

        // Register Service Worker
        if ('serviceWorker' in navigator) {
            const swBlob = new Blob([swCode], {type: 'application/javascript'});
            const swURL = URL.createObjectURL(swBlob);
            
            navigator.serviceWorker.register(swURL)
                .then(registration => console.log('SW registered'))
                .catch(error => console.log('SW registration failed'));
        }

        function downloadAPK() {
            const instructions = `
📱 لإنشاء APK لتطبيق دليلك أوتو:

1. انتقل إلى: https://www.pwabuilder.com
2. أدخل رابط هذه الصفحة: ${window.location.href}
3. اضغط "Start" وانتظر التحليل
4. اختر "Android" من الخيارات
5. اضغط "Download" للحصول على APK
6. ثبت APK على هاتفك الأندرويد

✨ ستحصل على تطبيق دليلك أوتو كاملاً على هاتفك!
            `;
            
            alert(instructions);
            
            // فتح PWABuilder في تاب جديد
            window.open('https://www.pwabuilder.com', '_blank');
        }

        // PWA Install prompt
        let deferredPrompt;
        window.addEventListener('beforeinstallprompt', (e) => {
            e.preventDefault();
            deferredPrompt = e;
            
            const installBtn = document.createElement('button');
            installBtn.textContent = '📲 تثبيت التطبيق';
            installBtn.className = 'download-btn';
            installBtn.style.marginTop = '10px';
            installBtn.onclick = async () => {
                deferredPrompt.prompt();
                const { outcome } = await deferredPrompt.userChoice;
                deferredPrompt = null;
                installBtn.remove();
            };
            
            document.querySelector('.container').appendChild(installBtn);
        });
    </script>
</body>
</html>
