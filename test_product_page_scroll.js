/**
 * اختبار الاسكرول في صفحة تفاصيل المنتج
 */

console.log('🧪 اختبار الاسكرول في صفحة المنتج...');

// وظيفة اختبار الاسكرول
function testProductPageScroll() {
    console.log('\n📱 اختبار صفحة المنتج...');
    
    // فتح صفحة منتج تجريبية
    const testProduct = {
        id: 1,
        name: 'منتج تجريبي للاختبار',
        price: '100',
        image: 'https://via.placeholder.com/300x300',
        description: 'وصف طويل للمنتج لاختبار الاسكرول في صفحة التفاصيل. هذا النص طويل جداً لضمان أن المحتوى يتطلب اسكرول للوصول لنهاية الصفحة.',
        specifications: {
            'المادة': 'معدن عالي الجودة',
            'الوزن': '2.5 كيلو',
            'الأبعاد': '30x20x15 سم',
            'اللون': 'أسود',
            'الضمان': 'سنة واحدة'
        }
    };
    
    try {
        // فتح صفحة المنتج
        showProduct(testProduct.id, testProduct);
        
        setTimeout(() => {
            const productPage = document.getElementById('productPage');
            console.log('Product Page:', productPage ? '✅ موجودة' : '❌ غير موجودة');
            
            if (productPage) {
                console.log('Display:', productPage.style.display);
                console.log('CSS Classes:', productPage.className);
                
                // فحص CSS الخاص بالاسكرول
                const computedStyle = window.getComputedStyle(productPage);
                console.log('Overflow Y:', computedStyle.overflowY);
                console.log('Height:', computedStyle.height);
                console.log('Position:', computedStyle.position);
                
                // فحص المحتوى
                const productDetails = productPage.querySelector('.product-details');
                if (productDetails) {
                    console.log('Product Details:', '✅ موجودة');
                    const detailsStyle = window.getComputedStyle(productDetails);
                    console.log('Details Overflow Y:', detailsStyle.overflowY);
                    console.log('Details Height:', detailsStyle.height);
                    console.log('Details Padding Bottom:', detailsStyle.paddingBottom);
                } else {
                    console.log('Product Details:', '❌ غير موجودة');
                }
                
                // فحص القائمة السفلية
                const bottomNav = productPage.querySelector('.bottom-nav');
                console.log('Bottom Navigation:', bottomNav ? '✅ موجودة' : '❌ غير موجودة');
                
                // اختبار الاسكرول
                console.log('\n🔄 اختبار الاسكرول...');
                console.log('Scroll Height:', productPage.scrollHeight);
                console.log('Client Height:', productPage.clientHeight);
                console.log('Can Scroll:', productPage.scrollHeight > productPage.clientHeight ? '✅ نعم' : '❌ لا');
                
                // محاولة الاسكرول
                productPage.scrollTop = 100;
                setTimeout(() => {
                    console.log('Scroll Top after scroll:', productPage.scrollTop);
                    console.log('Scroll Test:', productPage.scrollTop > 0 ? '✅ يعمل' : '❌ لا يعمل');
                }, 500);
            }
        }, 1000);
        
    } catch (error) {
        console.error('❌ خطأ في اختبار صفحة المنتج:', error);
    }
}

// وظيفة اختبار مقارنة مع الصفحات الأخرى
function compareWithOtherPages() {
    console.log('\n📊 مقارنة مع الصفحات الأخرى...');
    
    const pages = ['homePage', 'searchPage', 'cartPage', 'profilePage'];
    
    pages.forEach(pageId => {
        const page = document.getElementById(pageId);
        if (page) {
            const style = window.getComputedStyle(page);
            console.log(`${pageId}:`, {
                overflow: style.overflowY,
                height: style.height,
                position: style.position
            });
        }
    });
}

// تشغيل الاختبارات
console.log('\n📋 الاختبارات المتاحة:');
console.log('- testProductPageScroll() - اختبار الاسكرول في صفحة المنتج');
console.log('- compareWithOtherPages() - مقارنة مع الصفحات الأخرى');

console.log('\n🎯 المتوقع بعد الإصلاح:');
console.log('✅ الاسكرول يعمل بسلاسة في صفحة المنتج');
console.log('✅ لا ينتقل تلقائياً لآخر الصفحة');
console.log('✅ قائمة التنقل السفلية ظاهرة ومثبتة');
console.log('✅ المحتوى قابل للاسكرول بالكامل');
