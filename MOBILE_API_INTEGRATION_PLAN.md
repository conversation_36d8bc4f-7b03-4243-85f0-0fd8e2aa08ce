# 📱 خطة ربط APIs بالتطبيق المحمول - مشروع دليل

## 🎯 الهدف
ربط التطبيق المحمول dalilkMobileApp بجميع APIs الموجودة في المشروع الرئيسي وتشغيله بنجاح.

## 📊 تحليل الوضع الحالي

### التطبيق المحمول الحالي:
- **Framework:** React Native 0.70.6
- **State Management:** MobX
- **HTTP Client:** Axios
- **Base URL:** فارغ حالياً
- **APIs:** يستخدم بيانات وهمية (mock data)

### APIs المتاحة في المشروع:
1. **Ecommerce APIs** (`/api/v1/ecommerce/`)
   - Products, Categories, Brands
   - Cart, Checkout, Orders
   - Reviews, Compare, Coupons
   - Flash Sales, Currencies

2. **Authentication APIs** (`/api/v1/`)
   - Register, Login, Logout
   - Password Reset, Email Verification
   - OTP System

3. **Vehicle Parts Finder APIs** (`/api/v1/vehicle-parts/`)
   - Root Categories, Makes, Models
   - Parts Search, Popular Parts

4. **Location APIs** (`/api/v1/location/`)
   - Countries, States, Cities

5. **Contact APIs** (`/api/v1/contact/`)
   - Send Messages, Subjects

6. **Newsletter APIs** (`/api/v1/newsletter/`)
   - Subscribe, Unsubscribe

7. **Blog APIs** (`/api/v1/`)
   - Posts, Categories, Tags

8. **Gallery APIs** (`/api/v1/galleries/`)
   - Image Galleries

9. **Testimonial APIs** (`/api/v1/testimonials/`)
   - Customer Reviews

10. **Marketplace APIs** (`/api/v1/marketplace/`)
    - Stores, Store Products

## 🔧 خطة التنفيذ

### المرحلة 1: إعداد البيئة الأساسية
- [ ] تحديث Base URL
- [ ] إنشاء ملف API constants
- [ ] إعداد Axios interceptors

### المرحلة 2: ربط APIs الأساسية
- [ ] Authentication APIs
- [ ] Products & Categories APIs
- [ ] Cart & Orders APIs

### المرحلة 3: ربط APIs المتقدمة
- [ ] Vehicle Parts Finder
- [ ] Location Services
- [ ] Search & Filters

### المرحلة 4: اختبار وتحسين
- [ ] اختبار جميع الوظائف
- [ ] معالجة الأخطاء
- [ ] تحسين الأداء

## 📝 التفاصيل التقنية

### Base URL المطلوب:
```javascript
export const BASE_URL = 'http://0.0.0.0:8080/api/v1';
```

### APIs الأولوية العالية:
1. Authentication (Login/Register)
2. Product Categories
3. Products List & Details
4. Cart Management
5. User Profile

### APIs الأولوية المتوسطة:
1. Vehicle Parts Finder
2. Orders Management
3. Reviews & Ratings
4. Search & Filters

### APIs الأولوية المنخفضة:
1. Newsletter
2. Contact Forms
3. Testimonials
4. Gallery

## 🚀 خطوات التشغيل

1. **تحديث التبعيات:**
   ```bash
   cd dalilkMobileApp
   npm install
   ```

2. **تشغيل الخادم المحلي:**
   ```bash
   # في مجلد المشروع الرئيسي
   php artisan serve --host=0.0.0.0 --port=8080
   ```

3. **تشغيل التطبيق المحمول:**
   ```bash
   # Android
   npx react-native run-android
   
   # iOS
   npx react-native run-ios
   ```

## 📋 قائمة المراجعة

- [ ] فحص جميع APIs
- [ ] تحديث URL configuration
- [ ] إنشاء API constants
- [ ] تحديث Auth store
- [ ] تحديث Product store
- [ ] إضافة خدمات جديدة
- [ ] تحديث التبعيات
- [ ] اختبار التطبيق

## 🔍 ملاحظات مهمة

1. **الخادم المحلي:** يجب تشغيل الخادم على `http://0.0.0.0:8080`
2. **CORS:** قد نحتاج لإعداد CORS headers
3. **Authentication:** استخدام Sanctum tokens
4. **Error Handling:** إضافة معالجة شاملة للأخطاء
5. **Loading States:** إضافة حالات التحميل للواجهات

## 📚 المراجع

- [API Documentation](../API_DOCUMENTATION.md)
- [Postman Collection](../Dalil_API_Collection.postman_collection.json)
- [React Native Docs](https://reactnative.dev/)
- [MobX Docs](https://mobx.js.org/)
