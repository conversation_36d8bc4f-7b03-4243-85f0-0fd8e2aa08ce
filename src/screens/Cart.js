import React, {useState, useEffect} from 'react';
import {
    View,
    ScrollView,
    Image,
    Text,
    TouchableOpacity,
    SafeAreaView,
    StyleSheet,
    Alert
} from 'react-native';

import {ProductStore} from '../store/product';
import {observer} from 'mobx-react';

import {FontAwesomeIcon} from '@fortawesome/react-native-fontawesome';
import {
    faPlus,
    faMinus,
    faShoppingCart,
    faArrowLeft,
    faPercent
} from '@fortawesome/free-solid-svg-icons';
import {faTrashCan} from '@fortawesome/free-regular-svg-icons';

import { Button, Card, Divider, Badge, ToastManager } from '../components/ui';
import { theme } from '../styles/theme';

export const Cart = observer(({navigation}) => {
    const {
        state: {cart},
        updateCartQuantity,
        removeFromCart,
        clearCart
    } = ProductStore;

    // حساب المجموع
    const calculateTotal = () => {
        return cart.reduce((total, item) => {
            return total + (item.product.price * item.quantity);
        }, 0);
    };

    const calculateItemsCount = () => {
        return cart.reduce((total, item) => total + item.quantity, 0);
    };

    const handleConfirmOrder = () => {
        Alert.alert(
            'تأكيد الطلب',
            'هل أنت متأكد من تأكيد هذا الطلب؟',
            [
                { text: 'إلغاء', style: 'cancel' },
                {
                    text: 'تأكيد',
                    onPress: () => {
                        // TODO: إضافة منطق تأكيد الطلب
                        ToastManager.success('تم تأكيد الطلب بنجاح');
                        navigation.navigate('Orders');
                    }
                }
            ]
        );
    };

    const handleClearCart = () => {
        Alert.alert(
            'مسح السلة',
            'هل أنت متأكد من مسح جميع المنتجات من السلة؟',
            [
                { text: 'إلغاء', style: 'cancel' },
                {
                    text: 'مسح',
                    style: 'destructive',
                    onPress: () => {
                        clearCart();
                        ToastManager.success('تم مسح السلة');
                    }
                }
            ]
        );
    };

    return (
        <SafeAreaView style={cartStyles.container}>
            {/* Header */}
            <View style={cartStyles.header}>
                <TouchableOpacity
                    style={cartStyles.backButton}
                    onPress={() => navigation.goBack()}
                    activeOpacity={0.7}
                >
                    <FontAwesomeIcon
                        icon={faArrowLeft}
                        size={20}
                        color={theme.colors.gray[700]}
                    />
                </TouchableOpacity>

                <Text style={cartStyles.headerTitle}>
                    السلة ({calculateItemsCount()})
                </Text>

                {cart.length > 0 && (
                    <TouchableOpacity
                        style={cartStyles.clearButton}
                        onPress={handleClearCart}
                        activeOpacity={0.7}
                    >
                        <Text style={cartStyles.clearButtonText}>مسح الكل</Text>
                    </TouchableOpacity>
                )}
            </View>

            {cart.length > 0 ? (
                <>
                    <ScrollView
                        style={cartStyles.scrollView}
                        showsVerticalScrollIndicator={false}
                    >
                        {cart.map((item, index) => (
                            <CartItem
                                key={`${item.product.id}-${index}`}
                                item={item}
                                navigation={navigation}
                                onUpdateQuantity={updateCartQuantity}
                                onRemove={removeFromCart}
                            />
                        ))}

                        {/* ملخص الطلب */}
                        <Card style={cartStyles.summaryCard}>
                            <Text style={cartStyles.summaryTitle}>ملخص الطلب</Text>

                            <View style={cartStyles.summaryRow}>
                                <Text style={cartStyles.summaryLabel}>
                                    المجموع الفرعي ({calculateItemsCount()} منتج)
                                </Text>
                                <Text style={cartStyles.summaryValue}>
                                    {calculateTotal().toFixed(2)} ر.س
                                </Text>
                            </View>

                            <View style={cartStyles.summaryRow}>
                                <Text style={cartStyles.summaryLabel}>رسوم التوصيل</Text>
                                <Text style={cartStyles.summaryValue}>مجاني</Text>
                            </View>

                            <Divider spacing={theme.spacing.sm} />

                            <View style={cartStyles.totalRow}>
                                <Text style={cartStyles.totalLabel}>المجموع الكلي</Text>
                                <Text style={cartStyles.totalValue}>
                                    {calculateTotal().toFixed(2)} ر.س
                                </Text>
                            </View>
                        </Card>

                        <View style={cartStyles.bottomSpacing} />
                    </ScrollView>

                    {/* زر تأكيد الطلب */}
                    <View style={cartStyles.bottomBar}>
                        <Button
                            variant="primary"
                            size="large"
                            fullWidth
                            onPress={handleConfirmOrder}
                            icon={
                                <FontAwesomeIcon
                                    icon={faShoppingCart}
                                    size={18}
                                    color={theme.colors.white}
                                />
                            }
                        >
                            تأكيد الطلب ({calculateTotal().toFixed(2)} ر.س)
                        </Button>
                    </View>
                </>
            ) : (
                <EmptyCart navigation={navigation} />
            )}
        </SafeAreaView>
    );
});

// مكون السلة الفارغة
const EmptyCart = ({ navigation }) => {
    return (
        <View style={cartStyles.emptyContainer}>
            <View style={cartStyles.emptyIconContainer}>
                <FontAwesomeIcon
                    icon={faShoppingCart}
                    size={80}
                    color={theme.colors.gray[300]}
                />
            </View>

            <Text style={cartStyles.emptyTitle}>السلة فارغة</Text>
            <Text style={cartStyles.emptySubtitle}>
                لم تقم بإضافة أي منتجات إلى السلة بعد
            </Text>

            <Button
                variant="primary"
                size="large"
                onPress={() => navigation.navigate('Dashboard')}
                style={cartStyles.continueShoppingButton}
            >
                تصفح المنتجات
            </Button>
        </View>
    );
};

// مكون عنصر السلة المحسن
const CartItem = ({ item: { product, quantity }, navigation, onUpdateQuantity, onRemove }) => {
    const { setProduct } = ProductStore;

    const handleProductPress = () => {
        setProduct(product);
        navigation.navigate('Product');
    };

    const handleIncreaseQuantity = () => {
        onUpdateQuantity(product.id, quantity + 1);
    };

    const handleDecreaseQuantity = () => {
        if (quantity === 1) {
            Alert.alert(
                'إزالة المنتج',
                'هل أنت متأكد من إزالة هذا المنتج من السلة؟',
                [
                    { text: 'إلغاء', style: 'cancel' },
                    {
                        text: 'إزالة',
                        style: 'destructive',
                        onPress: () => onRemove(product.id)
                    }
                ]
            );
        } else {
            onUpdateQuantity(product.id, quantity - 1);
        }
    };

    const handleRemove = () => {
        Alert.alert(
            'إزالة المنتج',
            'هل أنت متأكد من إزالة هذا المنتج من السلة؟',
            [
                { text: 'إلغاء', style: 'cancel' },
                {
                    text: 'إزالة',
                    style: 'destructive',
                    onPress: () => onRemove(product.id)
                }
            ]
        );
    };

    return (
        <Card style={cartStyles.cartItem}>
            <TouchableOpacity
                style={cartStyles.itemContent}
                onPress={handleProductPress}
                activeOpacity={0.9}
            >
                {/* صورة المنتج */}
                <View style={cartStyles.imageContainer}>
                    <Image
                        style={cartStyles.productImage}
                        source={{ uri: product.imgs?.[0] }}
                        resizeMode="cover"
                    />
                    {product.discount && (
                        <Badge
                            variant="error"
                            size="small"
                            style={cartStyles.discountBadge}
                        >
                            -{product.discount}%
                        </Badge>
                    )}
                </View>

                {/* معلومات المنتج */}
                <View style={cartStyles.productInfo}>
                    <Text style={cartStyles.productName} numberOfLines={2}>
                        {product.name}
                    </Text>

                    {product.brand && (
                        <Text style={cartStyles.productBrand}>
                            {product.brand}
                        </Text>
                    )}

                    <View style={cartStyles.priceContainer}>
                        <Text style={cartStyles.currentPrice}>
                            {(product.price * quantity).toFixed(2)} ر.س
                        </Text>
                        <Text style={cartStyles.unitPrice}>
                            {product.price} ر.س للقطعة
                        </Text>
                    </View>
                </View>
            </TouchableOpacity>

            {/* أدوات التحكم */}
            <View style={cartStyles.controls}>
                {/* زر الحذف */}
                <TouchableOpacity
                    style={cartStyles.removeButton}
                    onPress={handleRemove}
                    activeOpacity={0.7}
                >
                    <FontAwesomeIcon
                        icon={faTrashCan}
                        size={16}
                        color={theme.colors.error[500]}
                    />
                </TouchableOpacity>

                {/* أدوات الكمية */}
                <View style={cartStyles.quantityControls}>
                    <TouchableOpacity
                        style={cartStyles.quantityButton}
                        onPress={handleDecreaseQuantity}
                        activeOpacity={0.7}
                    >
                        <FontAwesomeIcon
                            icon={quantity === 1 ? faTrashCan : faMinus}
                            size={14}
                            color={quantity === 1 ? theme.colors.error[500] : theme.colors.primary[600]}
                        />
                    </TouchableOpacity>

                    <Text style={cartStyles.quantityText}>{quantity}</Text>

                    <TouchableOpacity
                        style={cartStyles.quantityButton}
                        onPress={handleIncreaseQuantity}
                        activeOpacity={0.7}
                    >
                        <FontAwesomeIcon
                            icon={faPlus}
                            size={14}
                            color={theme.colors.primary[600]}
                        />
                    </TouchableOpacity>
                </View>
            </View>
        </Card>
    );
};

// أنماط شاشة Cart المحسنة
const cartStyles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: theme.colors.gray[50],
    },

    // Header
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: theme.spacing.md,
        paddingVertical: theme.spacing.sm,
        backgroundColor: theme.colors.white,
        borderBottomWidth: 1,
        borderBottomColor: theme.colors.gray[100],
        ...theme.shadows.sm,
    },

    backButton: {
        width: 40,
        height: 40,
        borderRadius: theme.borderRadius.md,
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: theme.colors.gray[50],
    },

    headerTitle: {
        fontSize: theme.typography.fontSize.xl,
        fontFamily: theme.typography.fontFamily.primaryBold,
        color: theme.colors.gray[900],
        flex: 1,
        textAlign: 'center',
    },

    clearButton: {
        paddingHorizontal: theme.spacing.sm,
        paddingVertical: theme.spacing.xs,
    },

    clearButtonText: {
        fontSize: theme.typography.fontSize.sm,
        fontFamily: theme.typography.fontFamily.primary,
        color: theme.colors.error[500],
    },

    scrollView: {
        flex: 1,
        paddingHorizontal: theme.spacing.md,
    },

    // السلة الفارغة
    emptyContainer: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
        paddingHorizontal: theme.spacing.xl,
    },

    emptyIconContainer: {
        width: 120,
        height: 120,
        borderRadius: theme.borderRadius.full,
        backgroundColor: theme.colors.gray[100],
        alignItems: 'center',
        justifyContent: 'center',
        marginBottom: theme.spacing.xl,
    },

    emptyTitle: {
        fontSize: theme.typography.fontSize['2xl'],
        fontFamily: theme.typography.fontFamily.primaryBold,
        color: theme.colors.gray[900],
        marginBottom: theme.spacing.sm,
        textAlign: 'center',
    },

    emptySubtitle: {
        fontSize: theme.typography.fontSize.md,
        fontFamily: theme.typography.fontFamily.primary,
        color: theme.colors.gray[600],
        textAlign: 'center',
        lineHeight: 22,
        marginBottom: theme.spacing.xl,
    },

    continueShoppingButton: {
        minWidth: 200,
    },

    // عنصر السلة
    cartItem: {
        marginVertical: theme.spacing.sm,
        padding: 0,
    },

    itemContent: {
        flexDirection: 'row',
        padding: theme.spacing.md,
    },

    imageContainer: {
        position: 'relative',
        marginRight: theme.spacing.md,
    },

    productImage: {
        width: 80,
        height: 80,
        borderRadius: theme.borderRadius.md,
    },

    discountBadge: {
        position: 'absolute',
        top: -4,
        right: -4,
    },

    productInfo: {
        flex: 1,
        justifyContent: 'space-between',
    },

    productName: {
        fontSize: theme.typography.fontSize.md,
        fontFamily: theme.typography.fontFamily.primarySemiBold,
        color: theme.colors.gray[900],
        marginBottom: theme.spacing.xs,
        textAlign: 'right',
    },

    productBrand: {
        fontSize: theme.typography.fontSize.sm,
        fontFamily: theme.typography.fontFamily.primary,
        color: theme.colors.gray[600],
        marginBottom: theme.spacing.sm,
        textAlign: 'right',
    },

    priceContainer: {
        alignItems: 'flex-end',
    },

    currentPrice: {
        fontSize: theme.typography.fontSize.lg,
        fontFamily: theme.typography.fontFamily.primaryBold,
        color: theme.colors.primary[600],
    },

    unitPrice: {
        fontSize: theme.typography.fontSize.xs,
        fontFamily: theme.typography.fontFamily.primary,
        color: theme.colors.gray[500],
    },

    // أدوات التحكم
    controls: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: theme.spacing.md,
        paddingBottom: theme.spacing.md,
        borderTopWidth: 1,
        borderTopColor: theme.colors.gray[100],
    },

    removeButton: {
        width: 36,
        height: 36,
        borderRadius: theme.borderRadius.md,
        backgroundColor: theme.colors.error[50],
        alignItems: 'center',
        justifyContent: 'center',
    },

    quantityControls: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: theme.spacing.md,
    },

    quantityButton: {
        width: 32,
        height: 32,
        borderRadius: theme.borderRadius.md,
        backgroundColor: theme.colors.primary[50],
        alignItems: 'center',
        justifyContent: 'center',
        borderWidth: 1,
        borderColor: theme.colors.primary[200],
    },

    quantityText: {
        fontSize: theme.typography.fontSize.lg,
        fontFamily: theme.typography.fontFamily.primaryBold,
        color: theme.colors.gray[900],
        minWidth: 30,
        textAlign: 'center',
    },

    // ملخص الطلب
    summaryCard: {
        marginVertical: theme.spacing.lg,
    },

    summaryTitle: {
        fontSize: theme.typography.fontSize.lg,
        fontFamily: theme.typography.fontFamily.primaryBold,
        color: theme.colors.gray[900],
        marginBottom: theme.spacing.md,
        textAlign: 'right',
    },

    summaryRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: theme.spacing.sm,
    },

    summaryLabel: {
        fontSize: theme.typography.fontSize.md,
        fontFamily: theme.typography.fontFamily.primary,
        color: theme.colors.gray[600],
    },

    summaryValue: {
        fontSize: theme.typography.fontSize.md,
        fontFamily: theme.typography.fontFamily.primarySemiBold,
        color: theme.colors.gray[900],
    },

    totalRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginTop: theme.spacing.sm,
    },

    totalLabel: {
        fontSize: theme.typography.fontSize.lg,
        fontFamily: theme.typography.fontFamily.primaryBold,
        color: theme.colors.gray[900],
    },

    totalValue: {
        fontSize: theme.typography.fontSize.xl,
        fontFamily: theme.typography.fontFamily.primaryBold,
        color: theme.colors.primary[600],
    },

    // الزر السفلي
    bottomBar: {
        backgroundColor: theme.colors.white,
        paddingHorizontal: theme.spacing.lg,
        paddingVertical: theme.spacing.md,
        borderTopWidth: 1,
        borderTopColor: theme.colors.gray[100],
        ...theme.shadows.lg,
    },

    bottomSpacing: {
        height: theme.spacing.xl,
    },
});
