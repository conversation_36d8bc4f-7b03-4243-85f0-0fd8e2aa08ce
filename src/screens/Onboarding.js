import {View, Text, Image} from 'react-native';
import React from 'react';
import Onboarding from 'react-native-onboarding-swiper';
import Svg, {Path} from 'react-native-svg';

const Tutorial = ({navigation}) => {
    return (
        <Onboarding
            onSkip={() => navigation.navigate('login')}
            onDone={() => navigation.navigate('login')}
            pages={[
                {
                    backgroundColor: '#fff',
                    titleStyles: {
                        fontFamily: 'Poppins-SemiBold',
                        color: '#333',
                    },
                    subTitleStyles: {
                        fontFamily: 'Poppins-Regular',
                        color: '#666',
                    },
                    image: (
                        <View style={{alignItems: 'center', marginBottom: 20}}>
                            <Svg height="120" width="120" viewBox="0 0 512 512">
                                <Path
                                    fill="#9551E8"
                                    d="M135.2 117.4L109.1 192H402.9l-26.1-74.6C372.3 104.6 360.2 96 346.6 96H165.4c-13.6 0-25.7 8.6-30.2 21.4zM39.6 196.8L74.8 96.3C88.3 57.8 124.6 32 165.4 32H346.6c40.8 0 77.1 25.8 90.6 64.3l35.2 100.5c23.2 9.6 39.6 32.5 39.6 59.2V304c0 26.5-21.5 48-48 48H416c-26.5 0-48-21.5-48-48V288H144v16c0 26.5-21.5 48-48 48H48c-26.5 0-48-21.5-48-48V256c0-26.7 16.4-49.6 39.6-59.2z"
                                />
                            </Svg>
                        </View>
                    ),
                    title: 'مرحباً بك في دليلك',
                    subtitle: 'أفضل تطبيق لقطع غيار السيارات في المنطقة',
                },
                {
                    backgroundColor: '#fff',
                    titleStyles: {
                        fontFamily: 'Poppins-SemiBold',
                        color: '#333',
                    },
                    subTitleStyles: {
                        fontFamily: 'Poppins-Regular',
                        color: '#666',
                    },
                    image: (
                        <View style={{alignItems: 'center', marginBottom: 20}}>
                            <Svg height="120" width="120" viewBox="0 0 576 512">
                                <Path
                                    fill="#9551E8"
                                    d="M0 24C0 10.7 10.7 0 24 0H69.5c22 0 41.5 12.8 50.6 32h411c26.3 0 45.5 25 38.6 50.4l-41 152.3c-8.5 31.4-37 53.3-69.5 53.3H170.7l5.4 28.5c2.2 11.3 12.1 19.5 23.6 19.5H488c13.3 0 24 10.7 24 24s-10.7 24-24 24H199.7c-34.6 0-64.3-24.6-70.7-58.5L77.4 54.5c-.7-3.8-4-6.5-7.9-6.5H24C10.7 48 0 37.3 0 24zM128 464a48 48 0 1 1 96 0 48 48 0 1 1 -96 0zm336-48a48 48 0 1 1 0 96 48 48 0 1 1 0-96z"
                                />
                            </Svg>
                        </View>
                    ),
                    title: 'تسوق بسهولة',
                    subtitle: 'اختر من آلاف قطع الغيار الأصلية والمتوافقة',
                },
                {
                    backgroundColor: '#fff',
                    titleStyles: {
                        fontFamily: 'Poppins-SemiBold',
                        color: '#333',
                    },
                    subTitleStyles: {
                        fontFamily: 'Poppins-Regular',
                        color: '#666',
                    },
                    image: (
                        <View style={{alignItems: 'center', marginBottom: 20}}>
                            <Svg height="120" width="120" viewBox="0 0 576 512">
                                <Path
                                    fill="#9551E8"
                                    d="M512 80c8.8 0 16 7.2 16 16v32H48V96c0-8.8 7.2-16 16-16H512zm16 144V416c0 8.8-7.2 16-16 16H64c-8.8 0-16-7.2-16-16V224H528zM64 32C28.7 32 0 60.7 0 96V416c0 35.3 28.7 64 64 64H512c35.3 0 64-28.7 64-64V96c0-35.3-28.7-64-64-64H64zm56 304c-13.3 0-24 10.7-24 24s10.7 24 24 24h48c13.3 0 24-10.7 24-24s-10.7-24-24-24H120zm128 0c-13.3 0-24 10.7-24 24s10.7 24 24 24H360c13.3 0 24-10.7 24-24s-10.7-24-24-24H248z"
                                />
                            </Svg>
                        </View>
                    ),
                    title: 'دفع آمن',
                    subtitle: 'طرق دفع متعددة وآمنة لراحتك',
                },
            ]}
        />
    );
};

export default Tutorial;
