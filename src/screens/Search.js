import React, {useState, useEffect} from 'react';
import {
    View,
    ScrollView,
    Text,
    TextInput,
    TouchableOpacity,
    BackHandler,
} from 'react-native';

import {ProductGrid} from '../components/ProductGrid';
import {Categories} from '../components/Categories';

import {FontAwesomeIcon} from '@fortawesome/react-native-fontawesome';
import {faSearch, faTimes} from '@fortawesome/free-solid-svg-icons';

import styles from '../styles';

const Search = ({navigation}) => {
    const [searchText, setSearchText] = useState('');
    const [searched, setSearched] = useState(false);
    const [recentSearches, setRecentSearches] = useState([
        'قطع غيار BMW',
        'فلاتر الهواء',
        'زيوت المحرك',
        'إطارات',
    ]);

    const handleBackButtonClick = () => {
        if (searched) {
            setSearched(false);
            setSearchText('');
            return true;
        }
        return false;
    };

    useEffect(() => {
        BackHandler.addEventListener(
            'hardwareBackPress',
            handleBackButtonClick,
        );
        return () => {
            BackHandler.removeEventListener(
                'hardwareBackPress',
                handleBackButtonClick,
            );
        };
    }, [searched]);

    const handleSearch = () => {
        if (searchText.trim().length > 0) {
            setSearched(true);
            // Add to recent searches if not already there
            if (!recentSearches.includes(searchText.trim())) {
                setRecentSearches(prev => [
                    searchText.trim(),
                    ...prev.slice(0, 4),
                ]);
            }
        }
    };

    const handleRecentSearch = text => {
        setSearchText(text);
        setSearched(true);
    };

    const clearSearch = () => {
        setSearchText('');
        setSearched(false);
    };

    return (
        <View style={{flex: 1}}>
            {/* Search Header */}
            <View style={styles.searchHeader}>
                <View style={styles.searchBarContainer}>
                    <TextInput
                        value={searchText}
                        onChangeText={setSearchText}
                        style={styles.searchInputFull}
                        placeholder="ابحث عن قطع الغيار..."
                        placeholderTextColor="#999"
                        selectionColor="#000"
                        onSubmitEditing={handleSearch}
                        autoFocus={!searched}
                    />
                    {searchText.length > 0 && (
                        <TouchableOpacity
                            onPress={clearSearch}
                            style={styles.clearSearchBtn}>
                            <FontAwesomeIcon
                                icon={faTimes}
                                color="#999"
                                size={16}
                            />
                        </TouchableOpacity>
                    )}
                    <TouchableOpacity
                        onPress={handleSearch}
                        style={styles.searchBtnFull}>
                        <FontAwesomeIcon
                            icon={faSearch}
                            color="#fff"
                            size={16}
                        />
                    </TouchableOpacity>
                </View>
            </View>

            <ScrollView style={{flex: 1, paddingTop: 10}}>
                {searched ? (
                    <>
                        {/* Search Results */}
                        <View style={{paddingHorizontal: 15, marginBottom: 10}}>
                            <Text style={styles.searchResultsTitle}>
                                نتائج البحث عن "{searchText}"
                            </Text>
                        </View>
                        <ProductGrid
                            navigation={navigation}
                            searchText={searchText}
                        />
                    </>
                ) : (
                    <>
                        {/* Categories Section */}
                        <View style={{marginBottom: 20}}>
                            <Text style={styles.sectionTitle}>الفئات</Text>
                            <Categories />
                        </View>

                        {/* Recent Searches */}
                        {recentSearches.length > 0 && (
                            <View
                                style={{
                                    paddingHorizontal: 15,
                                    marginBottom: 20,
                                }}>
                                <Text style={styles.sectionTitle}>
                                    عمليات البحث الأخيرة
                                </Text>
                                {recentSearches.map((item, index) => (
                                    <TouchableOpacity
                                        key={index}
                                        style={styles.recentSearchItem}
                                        onPress={() =>
                                            handleRecentSearch(item)
                                        }>
                                        <FontAwesomeIcon
                                            icon={faSearch}
                                            color="#999"
                                            size={14}
                                            style={{marginRight: 10}}
                                        />
                                        <Text style={styles.recentSearchText}>
                                            {item}
                                        </Text>
                                    </TouchableOpacity>
                                ))}
                            </View>
                        )}

                        {/* Popular Searches */}
                        <View style={{paddingHorizontal: 15, marginBottom: 20}}>
                            <Text style={styles.sectionTitle}>
                                البحث الشائع
                            </Text>
                            <View style={styles.popularSearchContainer}>
                                {[
                                    'فرامل',
                                    'زيوت',
                                    'فلاتر',
                                    'إطارات',
                                    'بطاريات',
                                    'مصابيح',
                                ].map((item, index) => (
                                    <TouchableOpacity
                                        key={index}
                                        style={styles.popularSearchTag}
                                        onPress={() =>
                                            handleRecentSearch(item)
                                        }>
                                        <Text style={styles.popularSearchText}>
                                            {item}
                                        </Text>
                                    </TouchableOpacity>
                                ))}
                            </View>
                        </View>

                        <View style={{height: 100}} />
                    </>
                )}
            </ScrollView>
        </View>
    );
};

export default Search;
