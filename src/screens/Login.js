import React, {useState} from 'react';
import {
    Text,
    View,
    ScrollView,
    SafeAreaView,
    StyleSheet,
    KeyboardAvoidingView,
    Platform,
    TouchableOpacity
} from 'react-native';
import { Button, Input, Card, ToastManager } from '../components/ui';
import styles from '../styles';

import {AuthStore} from '../store/auth';
import { theme } from '../styles/theme';

import {faUser, faLock, faEye, faEyeSlash} from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-native-fontawesome';
import Svg, {Path} from 'react-native-svg';

const Login = ({navigation}) => {
    const [email, setEmail] = useState('');
    const [password, setPassword] = useState('');
    const [showPassword, setShowPassword] = useState(false);
    const [loading, setLoading] = useState(false);
    const [errors, setErrors] = useState({});

    // التحقق من صحة البيانات
    const validateForm = () => {
        const newErrors = {};

        if (!email.trim()) {
            newErrors.email = 'البريد الإلكتروني مطلوب';
        } else if (!isValidEmail(email) && !isValidPhone(email)) {
            newErrors.email = 'يرجى إدخال بريد إلكتروني أو رقم هاتف صحيح';
        }

        if (!password.trim()) {
            newErrors.password = 'كلمة المرور مطلوبة';
        } else if (password.length < 6) {
            newErrors.password = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const isValidEmail = (email) => {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    };

    const isValidPhone = (phone) => {
        const phoneRegex = /^[0-9]{10,}$/;
        return phoneRegex.test(phone.replace(/\s/g, ''));
    };

    const onSubmit = async () => {
        if (!validateForm()) {
            ToastManager.error('يرجى تصحيح الأخطاء أولاً');
            return;
        }

        setLoading(true);
        try {
            await AuthStore.login({email: email.trim(), password: password});
            ToastManager.success('تم تسجيل الدخول بنجاح');
            // التنقل سيتم تلقائياً من خلال AuthStore
        } catch (error) {
            ToastManager.error('خطأ في تسجيل الدخول. يرجى المحاولة مرة أخرى');
        } finally {
            setLoading(false);
        }
    };

    const togglePasswordVisibility = () => {
        setShowPassword(!showPassword);
    };

    return (
        <SafeAreaView style={loginStyles.container}>
            <KeyboardAvoidingView
                style={loginStyles.keyboardView}
                behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
            >
                <ScrollView
                    style={loginStyles.scrollView}
                    contentContainerStyle={loginStyles.scrollContent}
                    showsVerticalScrollIndicator={false}
                    keyboardShouldPersistTaps="handled"
                >
                    {/* شعار التطبيق */}
                    <View style={loginStyles.logoContainer}>
                        <View style={loginStyles.logoWrapper}>
                            <Svg height="100" width="100" viewBox="0 0 512 512">
                                <Path
                                    fill={theme.colors.primary[600]}
                                    d="M135.2 117.4L109.1 192H402.9l-26.1-74.6C372.3 104.6 360.2 96 346.6 96H165.4c-13.6 0-25.7 8.6-30.2 21.4zM39.6 196.8L74.8 96.3C88.3 57.8 124.6 32 165.4 32H346.6c40.8 0 77.1 25.8 90.6 64.3l35.2 100.5c23.2 9.6 39.6 32.5 39.6 59.2V304c0 26.5-21.5 48-48 48H416c-26.5 0-48-21.5-48-48V288H144v16c0 26.5-21.5 48-48 48H48c-26.5 0-48-21.5-48-48V256c0-26.7 16.4-49.6 39.6-59.2z"
                                />
                            </Svg>
                        </View>
                        <Text style={loginStyles.logoText}>دليلك</Text>
                        <Text style={loginStyles.logoSubtext}>قطع غيار السيارات</Text>
                    </View>

                    {/* نموذج تسجيل الدخول */}
                    <Card style={loginStyles.formCard} variant="elevated">
                        <Text style={loginStyles.formTitle}>تسجيل الدخول</Text>
                        <Text style={loginStyles.formSubtitle}>
                            أدخل بياناتك للوصول إلى حسابك
                        </Text>

                        {/* حقل البريد الإلكتروني */}
                        <Input
                            label="البريد الإلكتروني أو رقم الهاتف"
                            placeholder="<EMAIL> أو 05xxxxxxxx"
                            value={email}
                            onChangeText={(text) => {
                                setEmail(text);
                                if (errors.email) {
                                    setErrors(prev => ({...prev, email: null}));
                                }
                            }}
                            error={errors.email}
                            leftIcon={
                                <FontAwesomeIcon
                                    icon={faUser}
                                    size={18}
                                    color={theme.colors.gray[500]}
                                />
                            }
                            keyboardType="email-address"
                            autoCapitalize="none"
                            autoCorrect={false}
                        />

                        {/* حقل كلمة المرور */}
                        <Input
                            label="كلمة المرور"
                            placeholder="أدخل كلمة المرور"
                            value={password}
                            onChangeText={(text) => {
                                setPassword(text);
                                if (errors.password) {
                                    setErrors(prev => ({...prev, password: null}));
                                }
                            }}
                            error={errors.password}
                            secureTextEntry={!showPassword}
                            leftIcon={
                                <FontAwesomeIcon
                                    icon={faLock}
                                    size={18}
                                    color={theme.colors.gray[500]}
                                />
                            }
                            rightIcon={
                                <FontAwesomeIcon
                                    icon={showPassword ? faEyeSlash : faEye}
                                    size={18}
                                    color={theme.colors.gray[500]}
                                />
                            }
                            onRightIconPress={togglePasswordVisibility}
                        />

                        {/* رابط إنشاء حساب جديد */}
                        <TouchableOpacity
                            style={loginStyles.signupLink}
                            onPress={() => navigation.navigate('signup')}
                            activeOpacity={0.7}
                        >
                            <Text style={loginStyles.signupText}>
                                ليس لديك حساب؟ إنشاء حساب جديد
                            </Text>
                        </TouchableOpacity>

                        {/* زر تسجيل الدخول */}
                        <Button
                            variant="primary"
                            size="large"
                            fullWidth
                            loading={loading}
                            onPress={onSubmit}
                            style={loginStyles.loginButton}
                        >
                            تسجيل الدخول
                        </Button>

                        {/* رابط نسيت كلمة المرور */}
                        <TouchableOpacity
                            style={loginStyles.forgotPasswordLink}
                            onPress={() => navigation.navigate('ForgotPassword')}
                            activeOpacity={0.7}
                        >
                            <Text style={loginStyles.forgotPasswordText}>
                                نسيت كلمة المرور؟
                            </Text>
                        </TouchableOpacity>
                    </Card>

                    {/* مساحة إضافية في الأسفل */}
                    <View style={loginStyles.bottomSpacing} />
                </ScrollView>
            </KeyboardAvoidingView>
        </SafeAreaView>
    );
};

// أنماط شاشة Login المحسنة
const loginStyles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: theme.colors.gray[50],
    },

    keyboardView: {
        flex: 1,
    },

    scrollView: {
        flex: 1,
    },

    scrollContent: {
        flexGrow: 1,
        paddingHorizontal: theme.spacing.lg,
    },

    // شعار التطبيق
    logoContainer: {
        alignItems: 'center',
        paddingVertical: theme.spacing['4xl'],
    },

    logoWrapper: {
        width: 120,
        height: 120,
        borderRadius: theme.borderRadius.full,
        backgroundColor: theme.colors.primary[50],
        alignItems: 'center',
        justifyContent: 'center',
        marginBottom: theme.spacing.lg,
        ...theme.shadows.md,
    },

    logoText: {
        fontSize: theme.typography.fontSize['3xl'],
        fontFamily: theme.typography.fontFamily.primaryBold,
        color: theme.colors.primary[600],
        marginBottom: theme.spacing.xs,
    },

    logoSubtext: {
        fontSize: theme.typography.fontSize.md,
        fontFamily: theme.typography.fontFamily.primary,
        color: theme.colors.gray[600],
        textAlign: 'center',
    },

    // نموذج تسجيل الدخول
    formCard: {
        marginBottom: theme.spacing.xl,
    },

    formTitle: {
        fontSize: theme.typography.fontSize['2xl'],
        fontFamily: theme.typography.fontFamily.primaryBold,
        color: theme.colors.gray[900],
        textAlign: 'center',
        marginBottom: theme.spacing.sm,
    },

    formSubtitle: {
        fontSize: theme.typography.fontSize.md,
        fontFamily: theme.typography.fontFamily.primary,
        color: theme.colors.gray[600],
        textAlign: 'center',
        marginBottom: theme.spacing.xl,
        lineHeight: 22,
    },

    // الروابط
    signupLink: {
        alignSelf: 'center',
        marginBottom: theme.spacing.xl,
        paddingVertical: theme.spacing.sm,
    },

    signupText: {
        fontSize: theme.typography.fontSize.sm,
        fontFamily: theme.typography.fontFamily.primary,
        color: theme.colors.primary[600],
        textAlign: 'center',
    },

    forgotPasswordLink: {
        alignSelf: 'center',
        marginTop: theme.spacing.lg,
        paddingVertical: theme.spacing.sm,
    },

    forgotPasswordText: {
        fontSize: theme.typography.fontSize.sm,
        fontFamily: theme.typography.fontFamily.primary,
        color: theme.colors.gray[500],
        textAlign: 'center',
    },

    // زر تسجيل الدخول
    loginButton: {
        marginTop: theme.spacing.md,
    },

    // مساحة إضافية
    bottomSpacing: {
        height: theme.spacing.xl,
    },
});

export default Login;
