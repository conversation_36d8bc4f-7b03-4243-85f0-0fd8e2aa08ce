import React, {useEffect, useState} from 'react';
import {
    ScrollView,
    Image,
    Text,
    View,
    Dimensions,
    TouchableOpacity,
    SafeAreaView,
    StyleSheet,
    StatusBar
} from 'react-native';
import Carousel, {Pagination} from 'react-native-snap-carousel-v4';

import {FontAwesomeIcon} from '@fortawesome/react-native-fontawesome';
import {faHeart} from '@fortawesome/free-regular-svg-icons';
import {
    faHeart as faHeartFilled,
    faCartShopping,
    faArrowLeft,
    faShare,
    faStar,
    faStarHalfAlt
} from '@fortawesome/free-solid-svg-icons';

import {ProductStore} from '../store/product';
import {observer} from 'mobx-react';
import { Button, Card, Badge, Divider, ToastManager } from '../components/ui';
import { theme } from '../styles/theme';

const {width: screenWidth, height: screenHeight} = Dimensions.get('window');

export const Product = observer(({ navigation }) => {
    const {
        state: {product, wishlist},
        addToCart,
        addToWishlist,
    } = ProductStore;

    const [activeSlide, setActiveSlide] = useState(0);
    const [liked, setLiked] = useState(false);
    const [quantity, setQuantity] = useState(1);
    const [selectedVariant, setSelectedVariant] = useState(null);

    useEffect(() => {
        setLiked(wishlist.find(x => x.id === product.id));
    }, [wishlist]);

    const handleAddToCart = () => {
        try {
            addToCart({
                ...product,
                quantity,
                selectedVariant
            });
            ToastManager.success('تم إضافة المنتج إلى السلة');
        } catch (error) {
            ToastManager.error('حدث خطأ أثناء إضافة المنتج');
        }
    };

    const handleToggleWishlist = () => {
        try {
            addToWishlist(product);
            ToastManager.success(
                liked ? 'تم إزالة المنتج من المفضلة' : 'تم إضافة المنتج إلى المفضلة'
            );
        } catch (error) {
            ToastManager.error('حدث خطأ أثناء تحديث المفضلة');
        }
    };

    const renderImageItem = ({item}) => {
        return (
            <View style={productStyles.imageContainer}>
                <Image
                    style={productStyles.productImage}
                    source={{ uri: item }}
                    resizeMode="cover"
                />
            </View>
        );
    };

    const renderStars = (rating) => {
        const stars = [];
        const fullStars = Math.floor(rating);
        const hasHalfStar = rating % 1 !== 0;

        for (let i = 0; i < fullStars; i++) {
            stars.push(
                <FontAwesomeIcon
                    key={i}
                    icon={faStar}
                    size={16}
                    color={theme.colors.warning[500]}
                />
            );
        }

        if (hasHalfStar) {
            stars.push(
                <FontAwesomeIcon
                    key="half"
                    icon={faStarHalfAlt}
                    size={16}
                    color={theme.colors.warning[500]}
                />
            );
        }

        const emptyStars = 5 - Math.ceil(rating);
        for (let i = 0; i < emptyStars; i++) {
            stars.push(
                <FontAwesomeIcon
                    key={`empty-${i}`}
                    icon={faStar}
                    size={16}
                    color={theme.colors.gray[300]}
                />
            );
        }

        return stars;
    };

    const PaginationView = () => {
        return (
            <Pagination
                dotsLength={product.imgs?.length || 0}
                activeDotIndex={activeSlide}
                containerStyle={productStyles.paginationContainer}
                dotStyle={productStyles.paginationDot}
                inactiveDotStyle={productStyles.paginationInactiveDot}
                inactiveDotOpacity={0.4}
                inactiveDotScale={0.6}
            />
        );
    };

    return (
        <SafeAreaView style={productStyles.container}>
            <StatusBar barStyle="dark-content" backgroundColor={theme.colors.white} />

            {/* Header */}
            <View style={productStyles.header}>
                <TouchableOpacity
                    style={productStyles.headerButton}
                    onPress={() => navigation.goBack()}
                    activeOpacity={0.7}
                >
                    <FontAwesomeIcon
                        icon={faArrowLeft}
                        size={20}
                        color={theme.colors.gray[700]}
                    />
                </TouchableOpacity>

                <View style={productStyles.headerActions}>
                    <TouchableOpacity
                        style={productStyles.headerButton}
                        onPress={() => {/* TODO: Share functionality */}}
                        activeOpacity={0.7}
                    >
                        <FontAwesomeIcon
                            icon={faShare}
                            size={18}
                            color={theme.colors.gray[700]}
                        />
                    </TouchableOpacity>

                    <TouchableOpacity
                        style={[
                            productStyles.headerButton,
                            liked && productStyles.likedButton
                        ]}
                        onPress={handleToggleWishlist}
                        activeOpacity={0.7}
                    >
                        <FontAwesomeIcon
                            icon={liked ? faHeartFilled : faHeart}
                            size={18}
                            color={liked ? theme.colors.error[500] : theme.colors.gray[700]}
                        />
                    </TouchableOpacity>
                </View>
            </View>

            <ScrollView style={productStyles.scrollView} showsVerticalScrollIndicator={false}>
                {/* صور المنتج */}
                <View style={productStyles.imageSection}>
                    <Carousel
                        data={product.imgs || []}
                        renderItem={renderImageItem}
                        sliderWidth={screenWidth}
                        itemWidth={screenWidth}
                        layout="default"
                        onSnapToItem={index => setActiveSlide(index)}
                    />
                    {product.imgs?.length > 1 && <PaginationView />}
                </View>

                {/* معلومات المنتج */}
                <Card style={productStyles.productInfo}>
                    {/* اسم المنتج والسعر */}
                    <View style={productStyles.titleSection}>
                        <Text style={productStyles.productName}>{product.name}</Text>
                        <View style={productStyles.priceSection}>
                            <Text style={productStyles.currentPrice}>
                                {product.price} ر.س
                            </Text>
                            {product.originalPrice && (
                                <Text style={productStyles.originalPrice}>
                                    {product.originalPrice} ر.س
                                </Text>
                            )}
                            {product.discount && (
                                <Badge variant="error" size="small">
                                    -{product.discount}%
                                </Badge>
                            )}
                        </View>
                    </View>

                    {/* التقييم */}
                    {product.rating && (
                        <View style={productStyles.ratingSection}>
                            <View style={productStyles.starsContainer}>
                                {renderStars(product.rating)}
                            </View>
                            <Text style={productStyles.ratingText}>
                                {product.rating} ({product.reviewCount || 0} تقييم)
                            </Text>
                        </View>
                    )}

                    <Divider spacing={theme.spacing.md} />

                    {/* الوصف */}
                    <View style={productStyles.descriptionSection}>
                        <Text style={productStyles.sectionTitle}>الوصف</Text>
                        <Text style={productStyles.description}>
                            {product.description}
                        </Text>
                    </View>

                    {/* المواصفات */}
                    {product.specifications && (
                        <>
                            <Divider spacing={theme.spacing.md} />
                            <View style={productStyles.specificationsSection}>
                                <Text style={productStyles.sectionTitle}>المواصفات</Text>
                                {Object.entries(product.specifications).map(([key, value]) => (
                                    <View key={key} style={productStyles.specificationRow}>
                                        <Text style={productStyles.specificationKey}>{key}</Text>
                                        <Text style={productStyles.specificationValue}>{value}</Text>
                                    </View>
                                ))}
                            </View>
                        </>
                    )}

                    {/* الكمية */}
                    <Divider spacing={theme.spacing.md} />
                    <View style={productStyles.quantitySection}>
                        <Text style={productStyles.sectionTitle}>الكمية</Text>
                        <View style={productStyles.quantityControls}>
                            <TouchableOpacity
                                style={productStyles.quantityButton}
                                onPress={() => setQuantity(Math.max(1, quantity - 1))}
                                activeOpacity={0.7}
                            >
                                <Text style={productStyles.quantityButtonText}>-</Text>
                            </TouchableOpacity>
                            <Text style={productStyles.quantityText}>{quantity}</Text>
                            <TouchableOpacity
                                style={productStyles.quantityButton}
                                onPress={() => setQuantity(quantity + 1)}
                                activeOpacity={0.7}
                            >
                                <Text style={productStyles.quantityButtonText}>+</Text>
                            </TouchableOpacity>
                        </View>
                    </View>

                    {/* مساحة إضافية للزر */}
                    <View style={productStyles.bottomSpacing} />
                </Card>
            </ScrollView>

            {/* زر إضافة إلى السلة */}
            <View style={productStyles.bottomBar}>
                <Button
                    variant="primary"
                    size="large"
                    fullWidth
                    onPress={handleAddToCart}
                    icon={
                        <FontAwesomeIcon
                            icon={faCartShopping}
                            size={18}
                            color={theme.colors.white}
                        />
                    }
                >
                    إضافة إلى السلة
                </Button>
            </View>
        </SafeAreaView>
    );
});

// أنماط شاشة Product المحسنة
const productStyles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: theme.colors.gray[50],
    },

    // Header
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: theme.spacing.md,
        paddingVertical: theme.spacing.sm,
        backgroundColor: theme.colors.white,
        borderBottomWidth: 1,
        borderBottomColor: theme.colors.gray[100],
        ...theme.shadows.sm,
    },

    headerButton: {
        width: 40,
        height: 40,
        borderRadius: theme.borderRadius.md,
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: theme.colors.gray[50],
    },

    headerActions: {
        flexDirection: 'row',
        gap: theme.spacing.sm,
    },

    likedButton: {
        backgroundColor: theme.colors.error[50],
    },

    scrollView: {
        flex: 1,
    },

    // قسم الصور
    imageSection: {
        backgroundColor: theme.colors.white,
        marginBottom: theme.spacing.md,
    },

    imageContainer: {
        width: screenWidth,
        height: screenWidth * 0.8,
        alignItems: 'center',
        justifyContent: 'center',
    },

    productImage: {
        width: '100%',
        height: '100%',
        borderBottomLeftRadius: theme.borderRadius.xl,
        borderBottomRightRadius: theme.borderRadius.xl,
    },

    // Pagination
    paginationContainer: {
        paddingVertical: theme.spacing.md,
    },

    paginationDot: {
        width: 30,
        height: 8,
        borderRadius: 4,
        backgroundColor: theme.colors.primary[600],
    },

    paginationInactiveDot: {
        width: 8,
        height: 8,
        borderRadius: 4,
        backgroundColor: theme.colors.gray[300],
    },

    // معلومات المنتج
    productInfo: {
        margin: theme.spacing.md,
        marginTop: 0,
    },

    titleSection: {
        marginBottom: theme.spacing.md,
    },

    productName: {
        fontSize: theme.typography.fontSize['2xl'],
        fontFamily: theme.typography.fontFamily.primaryBold,
        color: theme.colors.gray[900],
        marginBottom: theme.spacing.sm,
        textAlign: 'right',
    },

    priceSection: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'flex-end',
        gap: theme.spacing.sm,
    },

    currentPrice: {
        fontSize: theme.typography.fontSize.xl,
        fontFamily: theme.typography.fontFamily.primaryBold,
        color: theme.colors.primary[600],
    },

    originalPrice: {
        fontSize: theme.typography.fontSize.md,
        fontFamily: theme.typography.fontFamily.primary,
        color: theme.colors.gray[500],
        textDecorationLine: 'line-through',
    },

    // التقييم
    ratingSection: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'flex-end',
        gap: theme.spacing.sm,
        marginBottom: theme.spacing.md,
    },

    starsContainer: {
        flexDirection: 'row',
        gap: 2,
    },

    ratingText: {
        fontSize: theme.typography.fontSize.sm,
        fontFamily: theme.typography.fontFamily.primary,
        color: theme.colors.gray[600],
    },

    // الأقسام
    sectionTitle: {
        fontSize: theme.typography.fontSize.lg,
        fontFamily: theme.typography.fontFamily.primaryBold,
        color: theme.colors.gray[900],
        marginBottom: theme.spacing.sm,
        textAlign: 'right',
    },

    descriptionSection: {
        marginBottom: theme.spacing.lg,
    },

    description: {
        fontSize: theme.typography.fontSize.md,
        fontFamily: theme.typography.fontFamily.primary,
        color: theme.colors.gray[700],
        lineHeight: 24,
        textAlign: 'right',
    },

    // المواصفات
    specificationsSection: {
        marginBottom: theme.spacing.lg,
    },

    specificationRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingVertical: theme.spacing.sm,
        borderBottomWidth: 1,
        borderBottomColor: theme.colors.gray[100],
    },

    specificationKey: {
        fontSize: theme.typography.fontSize.md,
        fontFamily: theme.typography.fontFamily.primarySemiBold,
        color: theme.colors.gray[900],
    },

    specificationValue: {
        fontSize: theme.typography.fontSize.md,
        fontFamily: theme.typography.fontFamily.primary,
        color: theme.colors.gray[600],
    },

    // الكمية
    quantitySection: {
        marginBottom: theme.spacing.lg,
    },

    quantityControls: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        gap: theme.spacing.lg,
    },

    quantityButton: {
        width: 40,
        height: 40,
        borderRadius: theme.borderRadius.md,
        backgroundColor: theme.colors.primary[50],
        alignItems: 'center',
        justifyContent: 'center',
        borderWidth: 1,
        borderColor: theme.colors.primary[200],
    },

    quantityButtonText: {
        fontSize: theme.typography.fontSize.lg,
        fontFamily: theme.typography.fontFamily.primaryBold,
        color: theme.colors.primary[600],
    },

    quantityText: {
        fontSize: theme.typography.fontSize.xl,
        fontFamily: theme.typography.fontFamily.primaryBold,
        color: theme.colors.gray[900],
        minWidth: 40,
        textAlign: 'center',
    },

    // الزر السفلي
    bottomBar: {
        backgroundColor: theme.colors.white,
        paddingHorizontal: theme.spacing.lg,
        paddingVertical: theme.spacing.md,
        borderTopWidth: 1,
        borderTopColor: theme.colors.gray[100],
        ...theme.shadows.lg,
    },

    bottomSpacing: {
        height: theme.spacing.xl,
    },
});
