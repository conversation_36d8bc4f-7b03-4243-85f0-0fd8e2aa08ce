import React, {useState, useEffect} from 'react';
import {
    Image,
    Text,
    Pressable,
    TouchableOpacity,
    View,
    StyleSheet,
    ScrollView,
    Switch,
    Alert,
} from 'react-native';
import Input from '../components/Input';

import {PrimaryColor, SecondaryColor} from '../styles/theme';
import styles from '../styles';
import {faUser, faLock, faEnvelope, faPhone, faMapMarkerAlt, faCity, faGlobe} from '@fortawesome/free-solid-svg-icons';
import Svg, {Path} from 'react-native-svg';

import {AuthStore} from '../store/auth';

const Signup = ({navigation}) => {
    const [name, setName] = useState('');
    const [phone, setPhone] = useState('');
    const [password, setPassword] = useState('');
    const [confirmPassword, setConfirmPassword] = useState('');
    const [isWholesaleCustomer, setIsWholesaleCustomer] = useState(false);
    const [agreeTerms, setAgreeTerms] = useState(false);

    // Address fields for wholesale customers
    const [address, setAddress] = useState('');
    const [city, setCity] = useState('');
    const [state, setState] = useState('');
    const [postalCode, setPostalCode] = useState('');

    const onSubmit = async () => {
        // Validation
        if (!name || !phone || !password || !confirmPassword) {
            Alert.alert('خطأ', 'يرجى ملء جميع الحقول المطلوبة');
            return;
        }

        if (password !== confirmPassword) {
            Alert.alert('خطأ', 'كلمة المرور وتأكيد كلمة المرور غير متطابقتين');
            return;
        }

        if (!agreeTerms) {
            Alert.alert('خطأ', 'يجب الموافقة على الشروط والأحكام');
            return;
        }

        // Validate wholesale customer address
        if (isWholesaleCustomer && (!address || !city || !state)) {
            Alert.alert('خطأ', 'يرجى ملء معلومات العنوان لعملاء الجملة');
            return;
        }

        const registrationData = {
            name: name,
            phone: phone,
            password: password,
            password_confirmation: confirmPassword,
            is_wholesale_customer: isWholesaleCustomer,
            agree_terms_and_policy: agreeTerms,
        };

        // Add address data for wholesale customers
        if (isWholesaleCustomer) {
            registrationData.address = address;
            registrationData.city = city;
            registrationData.state = state;
            registrationData.postal_code = postalCode;
        }

        try {
            await AuthStore.register(registrationData);
        } catch (error) {
            Alert.alert('خطأ', 'حدث خطأ أثناء التسجيل. يرجى المحاولة مرة أخرى.');
        }
    };

    return (
        <View style={styles.container}>
            <View style={{marginVertical: 30, alignItems: 'center'}}>
                <Svg height="60" width="60" viewBox="0 0 512 512">
                    <Path
                        fill="#9551E8"
                        d="M135.2 117.4L109.1 192H402.9l-26.1-74.6C372.3 104.6 360.2 96 346.6 96H165.4c-13.6 0-25.7 8.6-30.2 21.4zM39.6 196.8L74.8 96.3C88.3 57.8 124.6 32 165.4 32H346.6c40.8 0 77.1 25.8 90.6 64.3l35.2 100.5c23.2 9.6 39.6 32.5 39.6 59.2V304c0 26.5-21.5 48-48 48H416c-26.5 0-48-21.5-48-48V288H144v16c0 26.5-21.5 48-48 48H48c-26.5 0-48-21.5-48-48V256c0-26.7 16.4-49.6 39.6-59.2z"
                    />
                </Svg>
                <Text style={{
                    fontSize: 24,
                    fontFamily: 'Poppins-Bold',
                    color: '#9551E8',
                    marginTop: 10,
                    textAlign: 'center'
                }}>
                    دليلك
                </Text>
            </View>
            <ScrollView style={{...styles.form}}>
                <Text
                    style={{
                        textAlign: 'center',
                        fontSize: 30,
                        fontFamily: 'Poppins-SemiBold',
                        color: '#000',
                        marginBottom: 20,
                    }}>
                    تسجيل حساب
                </Text>

                <Input
                    text={name}
                    setText={setName}
                    placeholder="الاسم الكامل"
                    icon={faUser}
                />
                <Input
                    text={phone}
                    setText={setPhone}
                    placeholder="رقم الهاتف"
                    icon={faPhone}
                    keyboardType="phone-pad"
                />
                <Input
                    password={true}
                    text={password}
                    setText={setPassword}
                    placeholder="كلمة المرور"
                    icon={faLock}
                />
                <Input
                    password={true}
                    text={confirmPassword}
                    setText={setConfirmPassword}
                    placeholder="تأكيد كلمة المرور"
                    icon={faLock}
                />

                {/* Wholesale Customer Toggle */}
                <View style={signupStyles.switchContainer}>
                    <Text style={signupStyles.switchLabel}>التسجيل كعميل جملة</Text>
                    <Switch
                        value={isWholesaleCustomer}
                        onValueChange={setIsWholesaleCustomer}
                        trackColor={{ false: '#767577', true: PrimaryColor }}
                        thumbColor={isWholesaleCustomer ? '#fff' : '#f4f3f4'}
                    />
                </View>

                {isWholesaleCustomer && (
                    <Text style={signupStyles.wholesaleNote}>
                        سيتم مراجعة الطلب والموافقة عليه من قبل الإدارة
                    </Text>
                )}

                {/* Address Section for Wholesale Customers */}
                {isWholesaleCustomer && (
                    <View style={signupStyles.addressSection}>
                        <Text style={signupStyles.sectionTitle}>معلومات العنوان (مطلوبة لعملاء الجملة)</Text>

                        <Input
                            text={address}
                            setText={setAddress}
                            placeholder="العنوان التفصيلي"
                            icon={faMapMarkerAlt}
                        />
                        <Input
                            text={city}
                            setText={setCity}
                            placeholder="المدينة"
                            icon={faCity}
                        />
                        <Input
                            text={state}
                            setText={setState}
                            placeholder="المنطقة/المحافظة"
                            icon={faGlobe}
                        />
                        <Input
                            text={postalCode}
                            setText={setPostalCode}
                            placeholder="الرمز البريدي (اختياري)"
                            icon={faEnvelope}
                            keyboardType="numeric"
                        />
                    </View>
                )}

                {/* Terms and Conditions */}
                <View style={signupStyles.termsContainer}>
                    <TouchableOpacity
                        style={signupStyles.checkbox}
                        onPress={() => setAgreeTerms(!agreeTerms)}>
                        <Text style={signupStyles.checkboxText}>
                            {agreeTerms ? '✓' : ''}
                        </Text>
                    </TouchableOpacity>
                    <Text style={signupStyles.termsText}>
                        أوافق على الشروط والأحكام
                    </Text>
                </View>

                <TouchableOpacity
                    style={[styles.primaryBtn, {marginTop: 20}]}
                    onPress={onSubmit}>
                    <Text style={{color: '#fff', fontSize: 16, fontWeight: 'bold'}}>
                        تسجيل
                    </Text>
                </TouchableOpacity>

                <Pressable
                    style={{alignSelf: 'center', marginVertical: 15}}
                    onPress={() => navigation.navigate('Login')}>
                    <Text
                        style={{
                            color: 'gray',
                            textDecorationColor: 'gray',
                            textDecorationLine: 'underline',
                            fontSize: 14,
                        }}>
                        هل لديك حساب؟ تسجيل الدخول
                    </Text>
                </Pressable>
            </ScrollView>
        </View>
    );
};

// Additional styles for the new components
const signupStyles = StyleSheet.create({
    switchContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginVertical: 15,
        paddingHorizontal: 5,
    },
    switchLabel: {
        fontSize: 16,
        color: '#333',
        fontFamily: 'Poppins-Medium',
    },
    wholesaleNote: {
        fontSize: 12,
        color: '#666',
        fontStyle: 'italic',
        marginBottom: 15,
        textAlign: 'center',
    },
    addressSection: {
        marginTop: 10,
        paddingTop: 15,
        borderTopWidth: 1,
        borderTopColor: '#eee',
    },
    sectionTitle: {
        fontSize: 16,
        fontWeight: 'bold',
        color: '#333',
        marginBottom: 15,
        textAlign: 'center',
    },
    termsContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        marginVertical: 15,
    },
    checkbox: {
        width: 20,
        height: 20,
        borderWidth: 1,
        borderColor: '#ccc',
        marginRight: 10,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#fff',
    },
    checkboxText: {
        color: PrimaryColor,
        fontWeight: 'bold',
    },
    termsText: {
        flex: 1,
        fontSize: 14,
        color: '#333',
    },
});

export default Signup;
