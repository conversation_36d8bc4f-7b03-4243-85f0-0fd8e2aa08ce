import React, {useEffect, useState, useCallback} from 'react';
import {
    View,
    ScrollView,
    Text,
    TextInput,
    TouchableOpacity,
    BackHandler,
    SafeAreaView,
    RefreshControl,
    StyleSheet,
    Animated
} from 'react-native';

import {Header} from '../components/Header';
import {Categories} from '../components/Categories';
import {ProductCarousel} from '../components/ProductCarousel';
import {ProductSlider} from '../components/ProductSlider';
import {ProductGrid} from '../components/ProductGrid';
import { Button, Card, Input } from '../components/ui';

import {FontAwesomeIcon} from '@fortawesome/react-native-fontawesome';
import {faSearch, faTimes, faFilter} from '@fortawesome/free-solid-svg-icons';

import styles from '../styles';
import { theme } from '../styles/theme';

const Dashboard = ({navigation}) => {
    // حالات البحث والتحديث
    const [searching, setSearching] = useState(false);
    const [searchText, setSearchText] = useState('');
    const [searched, setSearched] = useState(false);
    const [refreshing, setRefreshing] = useState(false);
    const [showFilters, setShowFilters] = useState(false);

    // Animation للبحث
    const searchAnimation = new Animated.Value(0);

    // معالج زر الرجوع
    const handleBackButtonClick = useCallback(() => {
        if (searched) {
            setSearching(false);
            setSearchText('');
            setSearched(false);
            return true;
        }
        return false;
    }, [searched]);

    // معالج التحديث
    const onRefresh = useCallback(() => {
        setRefreshing(true);
        // TODO: إضافة منطق تحديث البيانات
        setTimeout(() => {
            setRefreshing(false);
        }, 2000);
    }, []);

    // معالج البحث
    const handleSearch = useCallback(() => {
        if (searchText.trim().length > 0) {
            setSearched(true);
            setSearching(false);
        }
    }, [searchText]);

    // معالج مسح البحث
    const clearSearch = useCallback(() => {
        setSearchText('');
        setSearched(false);
        setSearching(false);
    }, []);

    // معالج فتح البحث
    const handleSearchFocus = useCallback(() => {
        setSearching(true);
        Animated.timing(searchAnimation, {
            toValue: 1,
            duration: 300,
            useNativeDriver: false,
        }).start();
    }, []);

    // معالج إغلاق البحث
    const handleSearchBlur = useCallback(() => {
        if (!searchText) {
            setSearching(false);
            Animated.timing(searchAnimation, {
                toValue: 0,
                duration: 300,
                useNativeDriver: false,
            }).start();
        }
    }, [searchText]);

    useEffect(() => {
        BackHandler.addEventListener('hardwareBackPress', handleBackButtonClick);
        return () => {
            BackHandler.removeEventListener('hardwareBackPress', handleBackButtonClick);
        };
    }, [handleBackButtonClick]);

    return (
        <SafeAreaView style={dashboardStyles.container}>
            {/* شريط البحث المحسن */}
            <View style={dashboardStyles.searchContainer}>
                <Card variant="flat" padding="small" style={dashboardStyles.searchCard}>
                    <View style={dashboardStyles.searchInputContainer}>
                        <TouchableOpacity
                            onPress={handleSearch}
                            style={dashboardStyles.searchButton}
                        >
                            <FontAwesomeIcon
                                icon={faSearch}
                                size={18}
                                color={theme.colors.gray[500]}
                            />
                        </TouchableOpacity>

                        <TextInput
                            value={searchText}
                            onChangeText={setSearchText}
                            style={dashboardStyles.searchInput}
                            placeholder="ابحث عن قطع الغيار..."
                            placeholderTextColor={theme.colors.gray[400]}
                            onFocus={handleSearchFocus}
                            onBlur={handleSearchBlur}
                            onSubmitEditing={handleSearch}
                            returnKeyType="search"
                            textAlign="right"
                        />

                        {searchText.length > 0 && (
                            <TouchableOpacity
                                onPress={clearSearch}
                                style={dashboardStyles.clearButton}
                            >
                                <FontAwesomeIcon
                                    icon={faTimes}
                                    size={16}
                                    color={theme.colors.gray[500]}
                                />
                            </TouchableOpacity>
                        )}

                        <TouchableOpacity
                            onPress={() => setShowFilters(!showFilters)}
                            style={dashboardStyles.filterButton}
                        >
                            <FontAwesomeIcon
                                icon={faFilter}
                                size={16}
                                color={theme.colors.primary[600]}
                            />
                        </TouchableOpacity>
                    </View>
                </Card>
            </View>

            {/* المحتوى الرئيسي */}
            <ScrollView
                style={dashboardStyles.content}
                showsVerticalScrollIndicator={false}
                refreshControl={
                    <RefreshControl
                        refreshing={refreshing}
                        onRefresh={onRefresh}
                        colors={[theme.colors.primary[600]]}
                        tintColor={theme.colors.primary[600]}
                    />
                }
            >
                {searched ? (
                    <View style={dashboardStyles.searchResults}>
                        <View style={dashboardStyles.searchHeader}>
                            <Text style={dashboardStyles.searchResultsTitle}>
                                نتائج البحث عن "{searchText}"
                            </Text>
                            <Button
                                variant="ghost"
                                size="small"
                                onPress={clearSearch}
                            >
                                مسح البحث
                            </Button>
                        </View>
                        <ProductGrid
                            navigation={navigation}
                            searchText={searchText}
                        />
                    </View>
                ) : (
                    <View style={dashboardStyles.homeContent}>
                        {/* قسم الاستكشاف */}
                        <View style={dashboardStyles.section}>
                            <Text style={dashboardStyles.sectionTitle}>استكشف</Text>
                            <ProductCarousel navigation={navigation} />
                        </View>

                        {/* قسم المنتجات الشائعة */}
                        <View style={dashboardStyles.section}>
                            <View style={dashboardStyles.sectionHeader}>
                                <Text style={dashboardStyles.sectionTitle}>المنتجات الشائعة</Text>
                                <TouchableOpacity onPress={() => navigation.navigate('AllProducts')}>
                                    <Text style={dashboardStyles.seeAllText}>عرض الكل</Text>
                                </TouchableOpacity>
                            </View>
                            <ProductSlider navigation={navigation} />
                        </View>

                        {/* قسم اختياراتنا لك */}
                        <View style={dashboardStyles.section}>
                            <View style={dashboardStyles.sectionHeader}>
                                <Text style={dashboardStyles.sectionTitle}>اختياراتنا لك</Text>
                                <TouchableOpacity onPress={() => navigation.navigate('Recommendations')}>
                                    <Text style={dashboardStyles.seeAllText}>عرض الكل</Text>
                                </TouchableOpacity>
                            </View>
                            <ProductSlider navigation={navigation} />
                        </View>

                        {/* مساحة إضافية في الأسفل */}
                        <View style={dashboardStyles.bottomSpacing} />
                    </View>
                )}
            </ScrollView>
        </SafeAreaView>
    );
};

// أنماط Dashboard محسنة
const dashboardStyles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: theme.colors.gray[50]
    },

    searchContainer: {
        paddingHorizontal: theme.spacing.md,
        paddingVertical: theme.spacing.sm,
        backgroundColor: theme.colors.white,
        borderBottomWidth: 1,
        borderBottomColor: theme.colors.gray[100]
    },

    searchCard: {
        backgroundColor: theme.colors.gray[50],
        borderRadius: theme.borderRadius.xl
    },

    searchInputContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: theme.spacing.sm
    },

    searchInput: {
        flex: 1,
        fontSize: theme.typography.fontSize.md,
        fontFamily: theme.typography.fontFamily.primary,
        color: theme.colors.gray[900],
        paddingVertical: theme.spacing.sm,
        textAlign: 'right'
    },

    searchButton: {
        padding: theme.spacing.xs
    },

    clearButton: {
        padding: theme.spacing.xs
    },

    filterButton: {
        padding: theme.spacing.xs,
        backgroundColor: theme.colors.primary[50],
        borderRadius: theme.borderRadius.sm
    },

    content: {
        flex: 1
    },

    searchResults: {
        padding: theme.spacing.md
    },

    searchHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: theme.spacing.lg
    },

    searchResultsTitle: {
        fontSize: theme.typography.fontSize.lg,
        fontFamily: theme.typography.fontFamily.primaryBold,
        color: theme.colors.gray[900]
    },

    homeContent: {
        paddingBottom: theme.spacing.xl
    },

    section: {
        marginBottom: theme.spacing.xl
    },

    sectionHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingHorizontal: theme.spacing.md,
        marginBottom: theme.spacing.md
    },

    sectionTitle: {
        fontSize: theme.typography.fontSize.xl,
        fontFamily: theme.typography.fontFamily.primaryBold,
        color: theme.colors.gray[900]
    },

    seeAllText: {
        fontSize: theme.typography.fontSize.sm,
        fontFamily: theme.typography.fontFamily.primarySemiBold,
        color: theme.colors.primary[600]
    },

    bottomSpacing: {
        height: 100
    }
});

export default Dashboard;
