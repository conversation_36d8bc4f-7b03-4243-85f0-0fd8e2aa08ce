import React from 'react';
import {
  View,
  Text,
  ScrollView,
  SafeAreaView,
  TouchableOpacity,
  StyleSheet
} from 'react-native';
import { FontAwesomeIcon } from '@fortawesome/react-native-fontawesome';
import {
  faArrowLeft,
  faPalette,
  faBell,
  faLanguage,
  faShield,
  faQuestionCircle,
  faSignOutAlt,
  faChevronRight
} from '@fortawesome/free-solid-svg-icons';
import {
  Card,
  Divider,
  ThemeSelector,
  ThemeToggle,
  Modal,
  Button
} from '../components/ui';
import { useTheme } from '../contexts/ThemeContext';

/**
 * شاشة الإعدادات مع دعم إدارة الثيمات
 */
const Settings = ({ navigation }) => {
  const { theme, isDark } = useTheme();
  const [showThemeModal, setShowThemeModal] = React.useState(false);

  const settingsGroups = [
    {
      title: 'المظهر والعرض',
      items: [
        {
          id: 'theme',
          title: 'المظهر',
          subtitle: isDark ? 'الوضع الليلي' : 'الوضع النهاري',
          icon: faPalette,
          onPress: () => setShowThemeModal(true),
          rightComponent: <ThemeToggle />
        },
        {
          id: 'language',
          title: 'اللغة',
          subtitle: 'العربية',
          icon: faLanguage,
          onPress: () => {/* TODO: Language settings */}
        }
      ]
    },
    {
      title: 'الإشعارات',
      items: [
        {
          id: 'notifications',
          title: 'الإشعارات',
          subtitle: 'إدارة إشعارات التطبيق',
          icon: faBell,
          onPress: () => {/* TODO: Notification settings */}
        }
      ]
    },
    {
      title: 'الأمان والخصوصية',
      items: [
        {
          id: 'privacy',
          title: 'الخصوصية والأمان',
          subtitle: 'إدارة بياناتك الشخصية',
          icon: faShield,
          onPress: () => {/* TODO: Privacy settings */}
        }
      ]
    },
    {
      title: 'المساعدة والدعم',
      items: [
        {
          id: 'help',
          title: 'المساعدة والدعم',
          subtitle: 'الأسئلة الشائعة والدعم',
          icon: faQuestionCircle,
          onPress: () => {/* TODO: Help screen */}
        }
      ]
    },
    {
      title: 'الحساب',
      items: [
        {
          id: 'logout',
          title: 'تسجيل الخروج',
          subtitle: 'الخروج من الحساب الحالي',
          icon: faSignOutAlt,
          onPress: () => {/* TODO: Logout */},
          isDestructive: true
        }
      ]
    }
  ];

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: theme.colors.surface, borderBottomColor: theme.colors.border }]}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
          activeOpacity={0.7}
        >
          <FontAwesomeIcon
            icon={faArrowLeft}
            size={20}
            color={theme.colors.text}
          />
        </TouchableOpacity>

        <Text style={[styles.headerTitle, { color: theme.colors.text }]}>
          الإعدادات
        </Text>

        <View style={styles.headerSpacer} />
      </View>

      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
      >
        {settingsGroups.map((group, groupIndex) => (
          <View key={group.title} style={styles.settingsGroup}>
            <Text style={[styles.groupTitle, { color: theme.colors.textSecondary }]}>
              {group.title}
            </Text>

            <Card style={styles.groupCard}>
              {group.items.map((item, itemIndex) => (
                <React.Fragment key={item.id}>
                  <SettingsItem
                    item={item}
                    theme={theme}
                  />
                  {itemIndex < group.items.length - 1 && (
                    <Divider spacing={0} />
                  )}
                </React.Fragment>
              ))}
            </Card>
          </View>
        ))}

        <View style={styles.bottomSpacing} />
      </ScrollView>

      {/* Theme Selection Modal */}
      <Modal
        visible={showThemeModal}
        onClose={() => setShowThemeModal(false)}
        title="اختيار المظهر"
        size="medium"
      >
        <ThemeSelector
          onThemeChange={() => setShowThemeModal(false)}
        />
      </Modal>
    </SafeAreaView>
  );
};

/**
 * مكون عنصر الإعدادات الفردي
 */
const SettingsItem = ({ item, theme }) => {
  return (
    <TouchableOpacity
      style={styles.settingsItem}
      onPress={item.onPress}
      activeOpacity={0.7}
    >
      <View style={styles.itemContent}>
        <View style={[
          styles.itemIcon,
          { backgroundColor: item.isDestructive ? theme.colors.error[50] : theme.colors.primary[50] }
        ]}>
          <FontAwesomeIcon
            icon={item.icon}
            size={18}
            color={item.isDestructive ? theme.colors.error[500] : theme.colors.primary[500]}
          />
        </View>

        <View style={styles.itemText}>
          <Text style={[
            styles.itemTitle,
            {
              color: item.isDestructive ? theme.colors.error[500] : theme.colors.text
            }
          ]}>
            {item.title}
          </Text>
          {item.subtitle && (
            <Text style={[styles.itemSubtitle, { color: theme.colors.textSecondary }]}>
              {item.subtitle}
            </Text>
          )}
        </View>

        {item.rightComponent ? (
          item.rightComponent
        ) : (
          <FontAwesomeIcon
            icon={faChevronRight}
            size={16}
            color={theme.colors.textTertiary}
          />
        )}
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },

  // Header
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },

  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },

  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    flex: 1,
    textAlign: 'center',
  },

  headerSpacer: {
    width: 40,
  },

  scrollView: {
    flex: 1,
    paddingHorizontal: 16,
  },

  // Settings Groups
  settingsGroup: {
    marginTop: 24,
  },

  groupTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
    textAlign: 'right',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },

  groupCard: {
    padding: 0,
  },

  // Settings Items
  settingsItem: {
    paddingHorizontal: 16,
    paddingVertical: 12,
  },

  itemContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },

  itemIcon: {
    width: 36,
    height: 36,
    borderRadius: 18,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },

  itemText: {
    flex: 1,
  },

  itemTitle: {
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'right',
  },

  itemSubtitle: {
    fontSize: 14,
    marginTop: 2,
    textAlign: 'right',
  },

  bottomSpacing: {
    height: 32,
  },
});

export default Settings;