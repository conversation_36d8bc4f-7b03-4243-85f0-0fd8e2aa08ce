import React from 'react';
import {
  TouchableOpacity,
  Text,
  StyleSheet,
  ActivityIndicator,
  View
} from 'react-native';
import { theme } from '../../styles/theme';

/**
 * مكون Button محسن وقابل للتخصيص
 * يدعم أنواع مختلفة من الأزرار والأحجام
 */
const Button = ({
  children,
  variant = 'primary',
  size = 'medium',
  disabled = false,
  loading = false,
  onPress,
  style,
  textStyle,
  icon,
  iconPosition = 'left',
  fullWidth = false,
  ...props
}) => {
  const buttonStyles = getButtonStyles(variant, size, disabled, fullWidth);
  const textStyles = getTextStyles(variant, size, disabled);

  const handlePress = () => {
    if (!disabled && !loading && onPress) {
      onPress();
    }
  };

  const renderContent = () => {
    if (loading) {
      return (
        <View style={styles.loadingContainer}>
          <ActivityIndicator
            size="small"
            color={getLoadingColor(variant)}
          />
          {typeof children === 'string' && (
            <Text style={[textStyles, styles.loadingText]}>
              {children}
            </Text>
          )}
        </View>
      );
    }

    if (icon && typeof children === 'string') {
      return (
        <View style={[
          styles.contentContainer,
          iconPosition === 'right' && styles.contentReverse
        ]}>
          {icon}
          <Text style={[textStyles, textStyle]}>
            {children}
          </Text>
        </View>
      );
    }

    if (typeof children === 'string') {
      return (
        <Text style={[textStyles, textStyle]}>
          {children}
        </Text>
      );
    }

    return children;
  };

  return (
    <TouchableOpacity
      style={[buttonStyles, style]}
      onPress={handlePress}
      disabled={disabled || loading}
      activeOpacity={0.8}
      {...props}
    >
      {renderContent()}
    </TouchableOpacity>
  );
};

// دالة للحصول على أنماط الزر
const getButtonStyles = (variant, size, disabled, fullWidth) => {
  const baseStyle = [styles.button];

  // إضافة نمط النوع
  baseStyle.push(styles[variant]);

  // إضافة نمط الحجم
  baseStyle.push(styles[size]);

  // إضافة نمط العرض الكامل
  if (fullWidth) {
    baseStyle.push(styles.fullWidth);
  }

  // إضافة نمط التعطيل
  if (disabled) {
    baseStyle.push(styles.disabled);
  }

  return baseStyle;
};

// دالة للحصول على أنماط النص
const getTextStyles = (variant, size, disabled) => {
  const baseStyle = [styles.text];

  // إضافة نمط النص حسب النوع
  baseStyle.push(styles[`${variant}Text`]);

  // إضافة نمط النص حسب الحجم
  baseStyle.push(styles[`${size}Text`]);

  // إضافة نمط النص المعطل
  if (disabled) {
    baseStyle.push(styles.disabledText);
  }

  return baseStyle;
};

// دالة للحصول على لون التحميل
const getLoadingColor = (variant) => {
  switch (variant) {
    case 'primary':
    case 'success':
    case 'warning':
    case 'error':
      return theme.colors.white;
    case 'secondary':
    case 'outline':
    case 'ghost':
      return theme.colors.primary[600];
    default:
      return theme.colors.white;
  }
};

const styles = StyleSheet.create({
  button: {
    borderRadius: theme.borderRadius.md,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    ...theme.shadows.sm
  },

  // أنواع الأزرار
  primary: {
    backgroundColor: theme.colors.primary[600],
    borderWidth: 0
  },

  secondary: {
    backgroundColor: theme.colors.secondary[100],
    borderWidth: 0
  },

  outline: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: theme.colors.primary[600]
  },

  ghost: {
    backgroundColor: 'transparent',
    borderWidth: 0
  },

  success: {
    backgroundColor: theme.colors.success[500],
    borderWidth: 0
  },

  warning: {
    backgroundColor: theme.colors.warning[500],
    borderWidth: 0
  },

  error: {
    backgroundColor: theme.colors.error[500],
    borderWidth: 0
  },

  // أحجام الأزرار
  small: {
    paddingVertical: theme.spacing.sm,
    paddingHorizontal: theme.spacing.md,
    minHeight: 36
  },

  medium: {
    paddingVertical: theme.spacing.md,
    paddingHorizontal: theme.spacing.lg,
    minHeight: 44
  },

  large: {
    paddingVertical: theme.spacing.lg,
    paddingHorizontal: theme.spacing.xl,
    minHeight: 52
  },

  // العرض الكامل
  fullWidth: {
    width: '100%'
  },

  // حالة التعطيل
  disabled: {
    opacity: 0.5
  },

  // أنماط النص
  text: {
    fontFamily: theme.typography.fontFamily.primarySemiBold,
    textAlign: 'center'
  },

  // ألوان النص حسب النوع
  primaryText: {
    color: theme.colors.white
  },

  secondaryText: {
    color: theme.colors.secondary[700]
  },

  outlineText: {
    color: theme.colors.primary[600]
  },

  ghostText: {
    color: theme.colors.primary[600]
  },

  successText: {
    color: theme.colors.white
  },

  warningText: {
    color: theme.colors.white
  },

  errorText: {
    color: theme.colors.white
  },

  // أحجام النص
  smallText: {
    fontSize: theme.typography.fontSize.sm,
    lineHeight: theme.typography.lineHeight.sm
  },

  mediumText: {
    fontSize: theme.typography.fontSize.md,
    lineHeight: theme.typography.lineHeight.md
  },

  largeText: {
    fontSize: theme.typography.fontSize.lg,
    lineHeight: theme.typography.lineHeight.lg
  },

  // نص معطل
  disabledText: {
    opacity: 0.7
  },

  // حاوي المحتوى
  contentContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: theme.spacing.sm
  },

  contentReverse: {
    flexDirection: 'row-reverse'
  },

  // حاوي التحميل
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: theme.spacing.sm
  },

  loadingText: {
    marginLeft: theme.spacing.sm
  }
});

export default Button;