import React, { useRef, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Animated,
  StyleSheet
} from 'react-native';
import { FontAwesomeIcon } from '@fortawesome/react-native-fontawesome';
import { faSun, faMoon } from '@fortawesome/free-solid-svg-icons';
import { theme } from '../../styles/theme';

/**
 * مكون Switch متقدم للتبديل بين الخيارات
 * يدعم أنواع مختلفة والـ animations
 */
const Switch = ({
  value = false,
  onValueChange,
  disabled = false,
  size = 'medium',
  variant = 'default',
  trackColor,
  thumbColor,
  activeTrackColor,
  activeThumbColor,
  style,
  ...props
}) => {
  const animatedValue = useRef(new Animated.Value(value ? 1 : 0)).current;

  useEffect(() => {
    Animated.timing(animatedValue, {
      toValue: value ? 1 : 0,
      duration: 200,
      useNativeDriver: false,
    }).start();
  }, [value]);

  const handlePress = () => {
    if (!disabled && onValueChange) {
      onValueChange(!value);
    }
  };

  const getSwitchStyles = () => {
    const baseStyle = [styles.switch];

    // إضافة نمط الحجم
    baseStyle.push(styles[`${size}Switch`]);

    // إضافة نمط النوع
    baseStyle.push(styles[variant]);

    if (disabled) {
      baseStyle.push(styles.disabled);
    }

    return baseStyle;
  };

  const getTrackStyles = () => {
    const baseStyle = [styles.track];

    // إضافة نمط الحجم للمسار
    baseStyle.push(styles[`${size}Track`]);

    // ألوان المسار
    const backgroundColor = value
      ? (activeTrackColor || theme.colors.primary[500])
      : (trackColor || theme.colors.gray[300]);

    baseStyle.push({ backgroundColor });

    return baseStyle;
  };

  const getThumbStyles = () => {
    const baseStyle = [styles.thumb];

    // إضافة نمط الحجم للإبهام
    baseStyle.push(styles[`${size}Thumb`]);

    // ألوان الإبهام
    const backgroundColor = value
      ? (activeThumbColor || theme.colors.white)
      : (thumbColor || theme.colors.white);

    baseStyle.push({ backgroundColor });

    // موقع الإبهام
    const translateX = animatedValue.interpolate({
      inputRange: [0, 1],
      outputRange: [2, getSwitchWidth() - getThumbSize() - 2],
    });

    baseStyle.push({
      transform: [{ translateX }],
    });

    return baseStyle;
  };

  const getSwitchWidth = () => {
    switch (size) {
      case 'small': return 40;
      case 'medium': return 50;
      case 'large': return 60;
      default: return 50;
    }
  };

  const getThumbSize = () => {
    switch (size) {
      case 'small': return 16;
      case 'medium': return 20;
      case 'large': return 24;
      default: return 20;
    }
  };

  return (
    <TouchableOpacity
      style={[getSwitchStyles(), style]}
      onPress={handlePress}
      disabled={disabled}
      activeOpacity={0.8}
      {...props}
    >
      <View style={getTrackStyles()}>
        <Animated.View style={getThumbStyles()} />
      </View>
    </TouchableOpacity>
  );
};

/**
 * مكون ThemeSwitch خاص للتبديل بين الثيمات
 */
export const ThemeSwitch = ({
  isDark = false,
  onToggle,
  size = 'medium',
  showIcons = true,
  style,
  ...props
}) => {
  return (
    <View style={[styles.themeSwitchContainer, style]}>
      {showIcons && (
        <FontAwesomeIcon
          icon={faSun}
          size={size === 'small' ? 14 : size === 'large' ? 20 : 16}
          color={!isDark ? theme.colors.warning[500] : theme.colors.gray[400]}
          style={styles.themeIcon}
        />
      )}

      <Switch
        value={isDark}
        onValueChange={onToggle}
        size={size}
        variant="theme"
        activeTrackColor={theme.colors.secondary[700]}
        trackColor={theme.colors.warning[200]}
        {...props}
      />

      {showIcons && (
        <FontAwesomeIcon
          icon={faMoon}
          size={size === 'small' ? 14 : size === 'large' ? 20 : 16}
          color={isDark ? theme.colors.secondary[300] : theme.colors.gray[400]}
          style={styles.themeIcon}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  switch: {
    justifyContent: 'center',
    alignItems: 'center',
  },

  // أحجام Switch
  smallSwitch: {
    width: 40,
    height: 20,
  },

  mediumSwitch: {
    width: 50,
    height: 24,
  },

  largeSwitch: {
    width: 60,
    height: 28,
  },

  // أنواع Switch
  default: {
    // النمط الافتراضي
  },

  theme: {
    // نمط خاص للثيمات
  },

  disabled: {
    opacity: 0.5,
  },

  // المسار
  track: {
    borderRadius: theme.borderRadius.full,
    position: 'relative',
    width: '100%',
    height: '100%',
  },

  smallTrack: {
    // يتم تحديد الحجم من الـ switch
  },

  mediumTrack: {
    // يتم تحديد الحجم من الـ switch
  },

  largeTrack: {
    // يتم تحديد الحجم من الـ switch
  },

  // الإبهام
  thumb: {
    position: 'absolute',
    borderRadius: theme.borderRadius.full,
    ...theme.shadows.sm,
  },

  smallThumb: {
    width: 16,
    height: 16,
    top: 2,
  },

  mediumThumb: {
    width: 20,
    height: 20,
    top: 2,
  },

  largeThumb: {
    width: 24,
    height: 24,
    top: 2,
  },

  // Theme Switch
  themeSwitchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: theme.spacing.sm,
  },

  themeIcon: {
    opacity: 0.8,
  },
});

export default Switch;