import React, { useEffect, useRef } from 'react';
import {
  Modal as RNModal,
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Animated,
  Dimensions,
  TouchableWithoutFeedback,
  ScrollView
} from 'react-native';
import { FontAwesomeIcon } from '@fortawesome/react-native-fontawesome';
import { faTimes } from '@fortawesome/free-solid-svg-icons';
import { theme } from '../../styles/theme';
import Button from './Button';

const { width, height } = Dimensions.get('window');

/**
 * مكون Modal متقدم وقابل للتخصيص
 * يدعم أنواع مختلفة من النوافذ المنبثقة والتفاعلات
 */
const Modal = ({
  visible = false,
  onClose,
  title,
  children,
  variant = 'default',
  size = 'medium',
  showCloseButton = true,
  closeOnBackdrop = true,
  animationType = 'slide',
  position = 'center',
  style,
  contentStyle,
  backdropStyle,
  ...props
}) => {
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(height)).current;
  const scaleAnim = useRef(new Animated.Value(0.8)).current;

  useEffect(() => {
    if (visible) {
      showModal();
    } else {
      hideModal();
    }
  }, [visible]);

  const showModal = () => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const hideModal = () => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 250,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: height,
        duration: 250,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 0.8,
        duration: 250,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const handleBackdropPress = () => {
    if (closeOnBackdrop && onClose) {
      onClose();
    }
  };

  const getModalStyle = () => {
    const baseStyle = [styles.modal];

    // إضافة نمط الحجم
    baseStyle.push(styles[`${size}Modal`]);

    // إضافة نمط الموقع
    baseStyle.push(styles[`${position}Position`]);

    // إضافة نمط النوع
    baseStyle.push(styles[variant]);

    return baseStyle;
  };

  const getAnimationStyle = () => {
    switch (animationType) {
      case 'fade':
        return {
          opacity: fadeAnim,
          transform: [{ scale: scaleAnim }],
        };
      case 'slide':
        return {
          opacity: fadeAnim,
          transform: [{ translateY: slideAnim }],
        };
      case 'scale':
        return {
          opacity: fadeAnim,
          transform: [{ scale: scaleAnim }],
        };
      default:
        return {
          opacity: fadeAnim,
          transform: [{ translateY: slideAnim }],
        };
    }
  };

  return (
    <RNModal
      transparent
      visible={visible}
      animationType="none"
      onRequestClose={onClose}
      {...props}
    >
      <TouchableWithoutFeedback onPress={handleBackdropPress}>
        <Animated.View style={[styles.backdrop, backdropStyle, { opacity: fadeAnim }]}>
          <TouchableWithoutFeedback>
            <Animated.View
              style={[
                getModalStyle(),
                getAnimationStyle(),
                style,
              ]}
            >
              {/* Header */}
              {(title || showCloseButton) && (
                <View style={styles.header}>
                  {title && (
                    <Text style={styles.title} numberOfLines={1}>
                      {title}
                    </Text>
                  )}
                  {showCloseButton && (
                    <TouchableOpacity
                      style={styles.closeButton}
                      onPress={onClose}
                      hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
                    >
                      <FontAwesomeIcon
                        icon={faTimes}
                        size={20}
                        color={theme.colors.gray[500]}
                      />
                    </TouchableOpacity>
                  )}
                </View>
              )}

              {/* Content */}
              <ScrollView
                style={[styles.content, contentStyle]}
                showsVerticalScrollIndicator={false}
                bounces={false}
              >
                {children}
              </ScrollView>
            </Animated.View>
          </TouchableWithoutFeedback>
        </Animated.View>
      </TouchableWithoutFeedback>
    </RNModal>
  );
};

/**
 * مكون Alert Modal للتنبيهات
 */
export const AlertModal = ({
  visible,
  onClose,
  title,
  message,
  confirmText = 'موافق',
  cancelText = 'إلغاء',
  onConfirm,
  onCancel,
  type = 'info',
  showCancel = false,
}) => {
  const handleConfirm = () => {
    if (onConfirm) onConfirm();
    if (onClose) onClose();
  };

  const handleCancel = () => {
    if (onCancel) onCancel();
    if (onClose) onClose();
  };

  const getIconColor = () => {
    switch (type) {
      case 'success': return theme.colors.success[500];
      case 'warning': return theme.colors.warning[500];
      case 'error': return theme.colors.error[500];
      default: return theme.colors.info[500];
    }
  };

  return (
    <Modal
      visible={visible}
      onClose={onClose}
      size="small"
      variant="alert"
      animationType="scale"
    >
      <View style={styles.alertContent}>
        {title && (
          <Text style={[styles.alertTitle, { color: getIconColor() }]}>
            {title}
          </Text>
        )}
        {message && (
          <Text style={styles.alertMessage}>
            {message}
          </Text>
        )}

        <View style={styles.alertButtons}>
          {showCancel && (
            <Button
              variant="outline"
              size="medium"
              onPress={handleCancel}
              style={styles.alertButton}
            >
              {cancelText}
            </Button>
          )}
          <Button
            variant={type === 'error' ? 'error' : 'primary'}
            size="medium"
            onPress={handleConfirm}
            style={styles.alertButton}
          >
            {confirmText}
          </Button>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  backdrop: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },

  modal: {
    backgroundColor: theme.colors.white,
    borderRadius: theme.borderRadius.xl,
    maxHeight: height * 0.9,
    ...theme.shadows.lg,
  },

  // أحجام Modal
  smallModal: {
    width: width * 0.8,
    maxWidth: 320,
  },

  mediumModal: {
    width: width * 0.9,
    maxWidth: 480,
  },

  largeModal: {
    width: width * 0.95,
    maxWidth: 640,
  },

  fullModal: {
    width: width,
    height: height,
    borderRadius: 0,
  },

  // مواقع Modal
  centerPosition: {
    alignSelf: 'center',
  },

  topPosition: {
    alignSelf: 'center',
    marginTop: 50,
  },

  bottomPosition: {
    alignSelf: 'center',
    marginBottom: 50,
  },

  // أنواع Modal
  default: {
    // النمط الافتراضي
  },

  alert: {
    padding: 0,
  },

  sheet: {
    borderTopLeftRadius: theme.borderRadius.xl,
    borderTopRightRadius: theme.borderRadius.xl,
    borderBottomLeftRadius: 0,
    borderBottomRightRadius: 0,
    marginTop: 'auto',
    marginBottom: 0,
  },

  // Header
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: theme.spacing.lg,
    paddingVertical: theme.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.gray[100],
  },

  title: {
    flex: 1,
    fontSize: theme.typography.fontSize.lg,
    fontFamily: theme.typography.fontFamily.primaryBold,
    color: theme.colors.gray[900],
    textAlign: 'right',
  },

  closeButton: {
    width: 32,
    height: 32,
    borderRadius: theme.borderRadius.md,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: theme.colors.gray[50],
  },

  // Content
  content: {
    flex: 1,
    paddingHorizontal: theme.spacing.lg,
    paddingVertical: theme.spacing.md,
  },

  // Alert styles
  alertContent: {
    padding: theme.spacing.xl,
    alignItems: 'center',
  },

  alertTitle: {
    fontSize: theme.typography.fontSize.xl,
    fontFamily: theme.typography.fontFamily.primaryBold,
    textAlign: 'center',
    marginBottom: theme.spacing.md,
  },

  alertMessage: {
    fontSize: theme.typography.fontSize.md,
    fontFamily: theme.typography.fontFamily.primary,
    color: theme.colors.gray[700],
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: theme.spacing.xl,
  },

  alertButtons: {
    flexDirection: 'row',
    gap: theme.spacing.md,
    width: '100%',
  },

  alertButton: {
    flex: 1,
  },
});

export default Modal;