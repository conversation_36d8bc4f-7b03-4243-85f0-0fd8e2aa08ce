import React, { useEffect, useRef } from 'react';
import {
  View,
  Animated,
  StyleSheet,
  Dimensions
} from 'react-native';
import { theme } from '../../styles/theme';

const { width } = Dimensions.get('window');

/**
 * مكون Loading محسن مع أنواع مختلفة من الـ loaders
 */
const LoadingSpinner = ({
  type = 'spinner',
  size = 'medium',
  color = theme.colors.primary[600],
  style
}) => {
  const animatedValue = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const animation = Animated.loop(
      Animated.timing(animatedValue, {
        toValue: 1,
        duration: type === 'pulse' ? 1500 : 1000,
        useNativeDriver: true,
      })
    );
    animation.start();

    return () => animation.stop();
  }, [animatedValue, type]);

  const getSizeValue = () => {
    switch (size) {
      case 'small': return 20;
      case 'medium': return 40;
      case 'large': return 60;
      default: return 40;
    }
  };

  const renderSpinner = () => {
    const rotation = animatedValue.interpolate({
      inputRange: [0, 1],
      outputRange: ['0deg', '360deg'],
    });

    return (
      <Animated.View
        style={[
          styles.spinner,
          {
            width: getSizeValue(),
            height: getSizeValue(),
            borderColor: `${color}20`,
            borderTopColor: color,
            transform: [{ rotate: rotation }],
          },
        ]}
      />
    );
  };

  const renderDots = () => {
    const dots = [0, 1, 2].map((index) => {
      const opacity = animatedValue.interpolate({
        inputRange: [0, 0.5, 1],
        outputRange: index === 0 ? [1, 0.3, 1] : index === 1 ? [0.3, 1, 0.3] : [1, 0.3, 1],
      });

      return (
        <Animated.View
          key={index}
          style={[
            styles.dot,
            {
              backgroundColor: color,
              opacity,
              width: getSizeValue() / 4,
              height: getSizeValue() / 4,
            },
          ]}
        />
      );
    });

    return <View style={styles.dotsContainer}>{dots}</View>;
  };

  const renderPulse = () => {
    const scale = animatedValue.interpolate({
      inputRange: [0, 0.5, 1],
      outputRange: [1, 1.2, 1],
    });

    const opacity = animatedValue.interpolate({
      inputRange: [0, 0.5, 1],
      outputRange: [0.7, 1, 0.7],
    });

    return (
      <Animated.View
        style={[
          styles.pulse,
          {
            width: getSizeValue(),
            height: getSizeValue(),
            backgroundColor: color,
            transform: [{ scale }],
            opacity,
          },
        ]}
      />
    );
  };

  const renderWave = () => {
    const bars = [0, 1, 2, 3, 4].map((index) => {
      const scaleY = animatedValue.interpolate({
        inputRange: [0, 0.5, 1],
        outputRange: [0.4, 1, 0.4],
        extrapolate: 'clamp',
      });

      return (
        <Animated.View
          key={index}
          style={[
            styles.waveBar,
            {
              backgroundColor: color,
              width: getSizeValue() / 8,
              height: getSizeValue(),
              transform: [{ scaleY }],
              marginHorizontal: 1,
            },
          ]}
        />
      );
    });

    return <View style={styles.waveContainer}>{bars}</View>;
  };

  const renderLoader = () => {
    switch (type) {
      case 'spinner':
        return renderSpinner();
      case 'dots':
        return renderDots();
      case 'pulse':
        return renderPulse();
      case 'wave':
        return renderWave();
      default:
        return renderSpinner();
    }
  };

  return (
    <View style={[styles.container, style]}>
      {renderLoader()}
    </View>
  );
};

/**
 * مكون Skeleton Loader للمحتوى
 */
export const SkeletonLoader = ({
  width = '100%',
  height = 20,
  borderRadius = 4,
  style
}) => {
  const animatedValue = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const animation = Animated.loop(
      Animated.sequence([
        Animated.timing(animatedValue, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(animatedValue, {
          toValue: 0,
          duration: 1000,
          useNativeDriver: true,
        }),
      ])
    );
    animation.start();

    return () => animation.stop();
  }, [animatedValue]);

  const opacity = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [0.3, 0.7],
  });

  return (
    <Animated.View
      style={[
        {
          width,
          height,
          borderRadius,
          backgroundColor: theme.colors.gray[200],
          opacity,
        },
        style,
      ]}
    />
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
  },

  spinner: {
    borderWidth: 3,
    borderRadius: 50,
  },

  dotsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },

  dot: {
    borderRadius: 50,
  },

  pulse: {
    borderRadius: 50,
  },

  waveContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },

  waveBar: {
    borderRadius: 2,
  },
});

export default LoadingSpinner;