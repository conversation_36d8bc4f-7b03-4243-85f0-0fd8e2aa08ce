import React, { useState } from 'react';
import {
  View,
  TextInput,
  Text,
  TouchableOpacity,
  StyleSheet
} from 'react-native';
import { theme } from '../../styles/theme';

/**
 * مكون Input محسن وقابل للتخصيص
 * يدعم التحقق من صحة البيانات والأيقونات
 */
const Input = ({
  label,
  placeholder,
  value,
  onChangeText,
  error,
  helperText,
  leftIcon,
  rightIcon,
  onRightIconPress,
  secureTextEntry = false,
  multiline = false,
  numberOfLines = 1,
  editable = true,
  style,
  inputStyle,
  labelStyle,
  errorStyle,
  helperStyle,
  ...props
}) => {
  const [isFocused, setIsFocused] = useState(false);

  const handleFocus = () => {
    setIsFocused(true);
  };

  const handleBlur = () => {
    setIsFocused(false);
  };

  const getInputContainerStyle = () => {
    const baseStyle = [styles.inputContainer];

    if (isFocused) {
      baseStyle.push(styles.focused);
    }

    if (error) {
      baseStyle.push(styles.error);
    }

    if (!editable) {
      baseStyle.push(styles.disabled);
    }

    return baseStyle;
  };

  return (
    <View style={[styles.container, style]}>
      {label && (
        <Text style={[styles.label, labelStyle]}>
          {label}
        </Text>
      )}

      <View style={getInputContainerStyle()}>
        {leftIcon && (
          <View style={styles.leftIconContainer}>
            {leftIcon}
          </View>
        )}

        <TextInput
          style={[
            styles.input,
            multiline && styles.multilineInput,
            inputStyle
          ]}
          placeholder={placeholder}
          placeholderTextColor={theme.colors.gray[400]}
          value={value}
          onChangeText={onChangeText}
          onFocus={handleFocus}
          onBlur={handleBlur}
          secureTextEntry={secureTextEntry}
          multiline={multiline}
          numberOfLines={numberOfLines}
          editable={editable}
          {...props}
        />

        {rightIcon && (
          <TouchableOpacity
            style={styles.rightIconContainer}
            onPress={onRightIconPress}
            disabled={!onRightIconPress}
          >
            {rightIcon}
          </TouchableOpacity>
        )}
      </View>

      {error && (
        <Text style={[styles.errorText, errorStyle]}>
          {error}
        </Text>
      )}

      {helperText && !error && (
        <Text style={[styles.helperText, helperStyle]}>
          {helperText}
        </Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: theme.spacing.md
  },

  label: {
    fontSize: theme.typography.fontSize.sm,
    fontFamily: theme.typography.fontFamily.primarySemiBold,
    color: theme.colors.gray[700],
    marginBottom: theme.spacing.xs
  },

  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: theme.colors.gray[300],
    borderRadius: theme.borderRadius.md,
    backgroundColor: theme.colors.white,
    minHeight: 44
  },

  focused: {
    borderColor: theme.colors.primary[600],
    ...theme.shadows.sm
  },

  error: {
    borderColor: theme.colors.error[500]
  },

  disabled: {
    backgroundColor: theme.colors.gray[50],
    opacity: 0.7
  },

  input: {
    flex: 1,
    fontSize: theme.typography.fontSize.md,
    fontFamily: theme.typography.fontFamily.primary,
    color: theme.colors.gray[900],
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    textAlign: 'right' // للنصوص العربية
  },

  multilineInput: {
    textAlignVertical: 'top',
    minHeight: 80
  },

  leftIconContainer: {
    paddingLeft: theme.spacing.md,
    justifyContent: 'center',
    alignItems: 'center'
  },

  rightIconContainer: {
    paddingRight: theme.spacing.md,
    justifyContent: 'center',
    alignItems: 'center'
  },

  errorText: {
    fontSize: theme.typography.fontSize.xs,
    fontFamily: theme.typography.fontFamily.primary,
    color: theme.colors.error[500],
    marginTop: theme.spacing.xs
  },

  helperText: {
    fontSize: theme.typography.fontSize.xs,
    fontFamily: theme.typography.fontFamily.primary,
    color: theme.colors.gray[500],
    marginTop: theme.spacing.xs
  }
});

export default Input;