import React from 'react';
import {
  View,
  Text,
  StyleSheet
} from 'react-native';
import { theme } from '../../styles/theme';

/**
 * مكون Divider للفصل بين العناصر
 * يدعم النص والأنماط المختلفة
 */
const Divider = ({
  children,
  orientation = 'horizontal',
  variant = 'solid',
  color = theme.colors.gray[200],
  thickness = 1,
  spacing = theme.spacing.md,
  textStyle,
  style,
  ...props
}) => {
  const getDividerStyles = () => {
    const baseStyle = [styles.divider];

    if (orientation === 'horizontal') {
      baseStyle.push({
        height: thickness,
        marginVertical: spacing,
      });
    } else {
      baseStyle.push({
        width: thickness,
        marginHorizontal: spacing,
      });
    }

    // إضافة نمط الخط
    switch (variant) {
      case 'dashed':
        baseStyle.push({
          borderStyle: 'dashed',
          backgroundColor: 'transparent',
          borderWidth: thickness,
          borderColor: color,
        });
        break;
      case 'dotted':
        baseStyle.push({
          borderStyle: 'dotted',
          backgroundColor: 'transparent',
          borderWidth: thickness,
          borderColor: color,
        });
        break;
      default:
        baseStyle.push({
          backgroundColor: color,
        });
    }

    return baseStyle;
  };

  // إذا كان هناك نص
  if (children) {
    return (
      <View style={[styles.container, styles[orientation], style]} {...props}>
        <View style={getDividerStyles()} />
        <Text style={[styles.text, textStyle]}>
          {children}
        </Text>
        <View style={getDividerStyles()} />
      </View>
    );
  }

  // خط فاصل بسيط
  return (
    <View
      style={[getDividerStyles(), style]}
      {...props}
    />
  );
};

/**
 * مكون SectionDivider للفصل بين الأقسام مع عنوان
 */
export const SectionDivider = ({
  title,
  subtitle,
  titleStyle,
  subtitleStyle,
  containerStyle,
  showLine = true,
  ...props
}) => {
  return (
    <View style={[styles.sectionContainer, containerStyle]}>
      {title && (
        <Text style={[styles.sectionTitle, titleStyle]}>
          {title}
        </Text>
      )}
      {subtitle && (
        <Text style={[styles.sectionSubtitle, subtitleStyle]}>
          {subtitle}
        </Text>
      )}
      {showLine && (
        <Divider
          spacing={theme.spacing.sm}
          {...props}
        />
      )}
    </View>
  );
};

/**
 * مكون SpaceDivider للمساحات الفارغة
 */
export const SpaceDivider = ({
  height = theme.spacing.md,
  width,
  style,
}) => {
  return (
    <View
      style={[
        {
          height: width ? undefined : height,
          width: width || undefined,
        },
        style,
      ]}
    />
  );
};

const styles = StyleSheet.create({
  divider: {
    flex: 1,
  },

  container: {
    alignItems: 'center',
  },

  horizontal: {
    flexDirection: 'row',
    alignItems: 'center',
  },

  vertical: {
    flexDirection: 'column',
    justifyContent: 'center',
    height: '100%',
  },

  text: {
    fontSize: theme.typography.fontSize.sm,
    fontFamily: theme.typography.fontFamily.primary,
    color: theme.colors.gray[500],
    paddingHorizontal: theme.spacing.md,
    backgroundColor: theme.colors.white,
    textAlign: 'center',
  },

  // Section Divider
  sectionContainer: {
    marginVertical: theme.spacing.lg,
  },

  sectionTitle: {
    fontSize: theme.typography.fontSize.xl,
    fontFamily: theme.typography.fontFamily.primaryBold,
    color: theme.colors.gray[900],
    textAlign: 'center',
    marginBottom: theme.spacing.xs,
  },

  sectionSubtitle: {
    fontSize: theme.typography.fontSize.md,
    fontFamily: theme.typography.fontFamily.primary,
    color: theme.colors.gray[600],
    textAlign: 'center',
    marginBottom: theme.spacing.sm,
  },
});

export default Divider;