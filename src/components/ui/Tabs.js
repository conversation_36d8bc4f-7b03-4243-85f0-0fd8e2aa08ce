import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Animated,
  StyleSheet,
  Dimensions
} from 'react-native';
import { FontAwesomeIcon } from '@fortawesome/react-native-fontawesome';
import { theme } from '../../styles/theme';

const { width: screenWidth } = Dimensions.get('window');

/**
 * مكون Tabs متقدم مع animations وأنواع مختلفة
 * يدعم التمرير الأفقي والعمودي
 */
const Tabs = ({
  tabs = [],
  activeTab = 0,
  onTabChange,
  variant = 'default',
  size = 'medium',
  scrollable = false,
  showIndicator = true,
  indicatorColor,
  tabBarStyle,
  contentStyle,
  style,
  ...props
}) => {
  const [currentTab, setCurrentTab] = useState(activeTab);
  const [tabWidths, setTabWidths] = useState([]);
  const indicatorAnim = useRef(new Animated.Value(0)).current;
  const scrollViewRef = useRef(null);

  useEffect(() => {
    setCurrentTab(activeTab);
    animateIndicator(activeTab);
  }, [activeTab]);

  const handleTabPress = (index) => {
    setCurrentTab(index);
    animateIndicator(index);

    if (onTabChange) {
      onTabChange(index);
    }

    // التمرير للتاب المحدد إذا كان scrollable
    if (scrollable && scrollViewRef.current) {
      scrollToTab(index);
    }
  };

  const animateIndicator = (index) => {
    if (!showIndicator || tabWidths.length === 0) return;

    const targetPosition = tabWidths.slice(0, index).reduce((sum, width) => sum + width, 0);

    Animated.spring(indicatorAnim, {
      toValue: targetPosition,
      useNativeDriver: false,
      tension: 100,
      friction: 8,
    }).start();
  };

  const scrollToTab = (index) => {
    if (!scrollViewRef.current || tabWidths.length === 0) return;

    const tabPosition = tabWidths.slice(0, index).reduce((sum, width) => sum + width, 0);
    const tabWidth = tabWidths[index] || 0;
    const centerPosition = tabPosition + (tabWidth / 2) - (screenWidth / 2);

    scrollViewRef.current.scrollTo({
      x: Math.max(0, centerPosition),
      animated: true,
    });
  };

  const onTabLayout = (event, index) => {
    const { width } = event.nativeEvent.layout;
    setTabWidths(prev => {
      const newWidths = [...prev];
      newWidths[index] = width;
      return newWidths;
    });
  };

  const getTabStyles = (index) => {
    const baseStyle = [styles.tab];

    // إضافة نمط الحجم
    baseStyle.push(styles[`${size}Tab`]);

    // إضافة نمط النوع
    baseStyle.push(styles[variant]);

    // إضافة نمط التاب النشط
    if (index === currentTab) {
      baseStyle.push(styles.activeTab);
      baseStyle.push(styles[`${variant}Active`]);
    }

    return baseStyle;
  };

  const getTabTextStyles = (index) => {
    const baseStyle = [styles.tabText];

    // إضافة نمط النص حسب الحجم
    baseStyle.push(styles[`${size}Text`]);

    // إضافة نمط النص النشط
    if (index === currentTab) {
      baseStyle.push(styles.activeTabText);
      baseStyle.push(styles[`${variant}ActiveText`]);
    } else {
      baseStyle.push(styles.inactiveTabText);
    }

    return baseStyle;
  };

  const renderTabBar = () => {
    const TabContainer = scrollable ? ScrollView : View;
    const containerProps = scrollable ? {
      ref: scrollViewRef,
      horizontal: true,
      showsHorizontalScrollIndicator: false,
      contentContainerStyle: styles.scrollableTabBar,
    } : {
      style: styles.fixedTabBar,
    };

    return (
      <View style={[styles.tabBarContainer, tabBarStyle]}>
        <TabContainer {...containerProps}>
          {tabs.map((tab, index) => (
            <TouchableOpacity
              key={index}
              style={getTabStyles(index)}
              onPress={() => handleTabPress(index)}
              onLayout={(event) => onTabLayout(event, index)}
              activeOpacity={0.7}
            >
              {tab.icon && (
                <FontAwesomeIcon
                  icon={tab.icon}
                  size={size === 'small' ? 14 : size === 'large' ? 18 : 16}
                  color={index === currentTab ? theme.colors.primary[600] : theme.colors.gray[500]}
                  style={styles.tabIcon}
                />
              )}

              <Text style={getTabTextStyles(index)}>
                {tab.title}
              </Text>

              {tab.badge && (
                <View style={styles.tabBadge}>
                  <Text style={styles.tabBadgeText}>{tab.badge}</Text>
                </View>
              )}
            </TouchableOpacity>
          ))}
        </TabContainer>

        {/* مؤشر التاب النشط */}
        {showIndicator && tabWidths.length > 0 && (
          <Animated.View
            style={[
              styles.indicator,
              {
                backgroundColor: indicatorColor || theme.colors.primary[600],
                width: tabWidths[currentTab] || 0,
                transform: [{ translateX: indicatorAnim }],
              },
            ]}
          />
        )}
      </View>
    );
  };

  const renderContent = () => {
    const activeTabData = tabs[currentTab];
    if (!activeTabData || !activeTabData.content) return null;

    return (
      <View style={[styles.content, contentStyle]}>
        {activeTabData.content}
      </View>
    );
  };

  return (
    <View style={[styles.container, style]} {...props}>
      {renderTabBar()}
      {renderContent()}
    </View>
  );
};

/**
 * مكون TabView للتبديل بين المحتويات مع animations
 */
export const TabView = ({
  tabs = [],
  activeTab = 0,
  onTabChange,
  animationType = 'slide',
  style,
  ...props
}) => {
  const [currentTab, setCurrentTab] = useState(activeTab);
  const animatedValue = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    setCurrentTab(activeTab);
    animateToTab(activeTab);
  }, [activeTab]);

  const animateToTab = (index) => {
    Animated.timing(animatedValue, {
      toValue: index,
      duration: 300,
      useNativeDriver: true,
    }).start();
  };

  const handleTabChange = (index) => {
    setCurrentTab(index);
    animateToTab(index);

    if (onTabChange) {
      onTabChange(index);
    }
  };

  return (
    <View style={[styles.tabViewContainer, style]}>
      <Tabs
        tabs={tabs.map(tab => ({ title: tab.title, icon: tab.icon, badge: tab.badge }))}
        activeTab={currentTab}
        onTabChange={handleTabChange}
        {...props}
      />

      <View style={styles.tabViewContent}>
        {tabs.map((tab, index) => {
          const translateX = animatedValue.interpolate({
            inputRange: [index - 1, index, index + 1],
            outputRange: [screenWidth, 0, -screenWidth],
            extrapolate: 'clamp',
          });

          const opacity = animatedValue.interpolate({
            inputRange: [index - 1, index, index + 1],
            outputRange: [0, 1, 0],
            extrapolate: 'clamp',
          });

          return (
            <Animated.View
              key={index}
              style={[
                styles.tabViewPanel,
                {
                  transform: animationType === 'slide' ? [{ translateX }] : [],
                  opacity: animationType === 'fade' ? opacity : 1,
                },
              ]}
            >
              {tab.content}
            </Animated.View>
          );
        })}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },

  // Tab Bar
  tabBarContainer: {
    position: 'relative',
    backgroundColor: theme.colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },

  fixedTabBar: {
    flexDirection: 'row',
  },

  scrollableTabBar: {
    flexDirection: 'row',
    paddingHorizontal: theme.spacing.md,
  },

  // Tab Styles
  tab: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    minWidth: 80,
  },

  // أحجام Tab
  smallTab: {
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: theme.spacing.xs,
    minWidth: 60,
  },

  mediumTab: {
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    minWidth: 80,
  },

  largeTab: {
    paddingHorizontal: theme.spacing.lg,
    paddingVertical: theme.spacing.md,
    minWidth: 100,
  },

  // أنواع Tab
  default: {
    backgroundColor: 'transparent',
  },

  pills: {
    backgroundColor: theme.colors.gray[100],
    borderRadius: theme.borderRadius.full,
    marginHorizontal: theme.spacing.xs,
  },

  cards: {
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.md,
    marginHorizontal: theme.spacing.xs,
    ...theme.shadows.sm,
  },

  // Tab النشط
  activeTab: {
    // سيتم تحديد الأنماط حسب النوع
  },

  defaultActive: {
    backgroundColor: 'transparent',
  },

  pillsActive: {
    backgroundColor: theme.colors.primary[500],
  },

  cardsActive: {
    backgroundColor: theme.colors.primary[50],
    borderWidth: 1,
    borderColor: theme.colors.primary[200],
  },

  // نصوص Tab
  tabText: {
    fontFamily: theme.typography.fontFamily.primarySemiBold,
    textAlign: 'center',
  },

  // أحجام النص
  smallText: {
    fontSize: theme.typography.fontSize.sm,
  },

  mediumText: {
    fontSize: theme.typography.fontSize.md,
  },

  largeText: {
    fontSize: theme.typography.fontSize.lg,
  },

  // ألوان النص
  activeTabText: {
    color: theme.colors.primary[600],
  },

  inactiveTabText: {
    color: theme.colors.gray[600],
  },

  defaultActiveText: {
    color: theme.colors.primary[600],
  },

  pillsActiveText: {
    color: theme.colors.white,
  },

  cardsActiveText: {
    color: theme.colors.primary[700],
  },

  // أيقونة Tab
  tabIcon: {
    marginRight: theme.spacing.xs,
  },

  // شارة Tab
  tabBadge: {
    backgroundColor: theme.colors.error[500],
    borderRadius: theme.borderRadius.full,
    paddingHorizontal: 6,
    paddingVertical: 2,
    marginLeft: theme.spacing.xs,
    minWidth: 18,
    alignItems: 'center',
    justifyContent: 'center',
  },

  tabBadgeText: {
    color: theme.colors.white,
    fontSize: 10,
    fontFamily: theme.typography.fontFamily.primaryBold,
  },

  // مؤشر Tab النشط
  indicator: {
    position: 'absolute',
    bottom: 0,
    height: 3,
    borderRadius: 1.5,
  },

  // المحتوى
  content: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },

  // TabView
  tabViewContainer: {
    flex: 1,
  },

  tabViewContent: {
    flex: 1,
    position: 'relative',
  },

  tabViewPanel: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    width: screenWidth,
  },
});

export default Tabs;