import React, { useState } from 'react';
import {
  View,
  Text,
  Image,
  TouchableOpacity,
  StyleSheet
} from 'react-native';
import { FontAwesomeIcon } from '@fortawesome/react-native-fontawesome';
import { faUser } from '@fortawesome/free-solid-svg-icons';
import { theme } from '../../styles/theme';

/**
 * مكون Avatar لعرض صور المستخدمين
 * يدعم الصور والأحرف والأيقونات
 */
const Avatar = ({
  source,
  name,
  size = 'medium',
  variant = 'circle',
  backgroundColor,
  textColor,
  onPress,
  style,
  textStyle,
  showBorder = false,
  borderColor = theme.colors.gray[200],
  borderWidth = 2,
  ...props
}) => {
  const [imageError, setImageError] = useState(false);

  // استخراج الأحرف الأولى من الاسم
  const getInitials = (name) => {
    if (!name) return '';
    const words = name.trim().split(' ');
    if (words.length === 1) {
      return words[0].charAt(0).toUpperCase();
    }
    return (words[0].charAt(0) + words[words.length - 1].charAt(0)).toUpperCase();
  };

  // الحصول على لون خلفية عشوائي بناءً على الاسم
  const getBackgroundColor = () => {
    if (backgroundColor) return backgroundColor;

    const colors = [
      theme.colors.primary[500],
      theme.colors.success[500],
      theme.colors.warning[500],
      theme.colors.error[500],
      theme.colors.info[500],
      theme.colors.secondary[500],
    ];

    if (!name) return colors[0];

    const charCode = name.charCodeAt(0);
    return colors[charCode % colors.length];
  };

  const getAvatarStyles = () => {
    const baseStyle = [styles.avatar];

    // إضافة نمط الحجم
    baseStyle.push(styles[`${size}Size`]);

    // إضافة نمط الشكل
    baseStyle.push(styles[variant]);

    // إضافة الحدود
    if (showBorder) {
      baseStyle.push({
        borderWidth,
        borderColor,
      });
    }

    return baseStyle;
  };

  const getTextStyles = () => {
    const baseStyle = [styles.text];

    // إضافة نمط النص حسب الحجم
    baseStyle.push(styles[`${size}Text`]);

    // إضافة لون النص
    baseStyle.push({
      color: textColor || theme.colors.white,
    });

    return baseStyle;
  };

  const renderContent = () => {
    // عرض الصورة إذا كانت متوفرة ولم تفشل
    if (source && !imageError) {
      return (
        <Image
          source={source}
          style={[getAvatarStyles(), styles.image]}
          onError={() => setImageError(true)}
          {...props}
        />
      );
    }

    // عرض الأحرف الأولى إذا كان الاسم متوفر
    if (name) {
      return (
        <View
          style={[
            getAvatarStyles(),
            { backgroundColor: getBackgroundColor() },
            style,
          ]}
        >
          <Text style={[getTextStyles(), textStyle]}>
            {getInitials(name)}
          </Text>
        </View>
      );
    }

    // عرض أيقونة افتراضية
    return (
      <View
        style={[
          getAvatarStyles(),
          { backgroundColor: getBackgroundColor() },
          style,
        ]}
      >
        <FontAwesomeIcon
          icon={faUser}
          size={getSizeValue() * 0.5}
          color={textColor || theme.colors.white}
        />
      </View>
    );
  };

  const getSizeValue = () => {
    switch (size) {
      case 'small': return 32;
      case 'medium': return 48;
      case 'large': return 64;
      case 'xlarge': return 80;
      default: return 48;
    }
  };

  if (onPress) {
    return (
      <TouchableOpacity onPress={onPress} activeOpacity={0.8}>
        {renderContent()}
      </TouchableOpacity>
    );
  }

  return renderContent();
};

/**
 * مكون AvatarGroup لعرض مجموعة من الـ Avatars
 */
export const AvatarGroup = ({
  avatars = [],
  max = 3,
  size = 'medium',
  spacing = -8,
  style,
  onMorePress,
}) => {
  const visibleAvatars = avatars.slice(0, max);
  const remainingCount = avatars.length - max;

  return (
    <View style={[styles.group, style]}>
      {visibleAvatars.map((avatar, index) => (
        <View
          key={index}
          style={[
            styles.groupItem,
            { marginLeft: index > 0 ? spacing : 0 },
          ]}
        >
          <Avatar
            {...avatar}
            size={size}
            showBorder={true}
            borderColor={theme.colors.white}
          />
        </View>
      ))}

      {remainingCount > 0 && (
        <TouchableOpacity
          style={[
            styles.groupItem,
            { marginLeft: spacing },
          ]}
          onPress={onMorePress}
          activeOpacity={0.8}
        >
          <Avatar
            name={`+${remainingCount}`}
            size={size}
            backgroundColor={theme.colors.gray[400]}
            showBorder={true}
            borderColor={theme.colors.white}
          />
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  avatar: {
    alignItems: 'center',
    justifyContent: 'center',
    overflow: 'hidden',
  },

  // أحجام Avatar
  smallSize: {
    width: 32,
    height: 32,
  },

  mediumSize: {
    width: 48,
    height: 48,
  },

  largeSize: {
    width: 64,
    height: 64,
  },

  xlargeSize: {
    width: 80,
    height: 80,
  },

  // أشكال Avatar
  circle: {
    borderRadius: 50,
  },

  square: {
    borderRadius: theme.borderRadius.md,
  },

  rounded: {
    borderRadius: theme.borderRadius.lg,
  },

  // الصورة
  image: {
    resizeMode: 'cover',
  },

  // النص
  text: {
    fontFamily: theme.typography.fontFamily.primaryBold,
    textAlign: 'center',
  },

  // أحجام النص
  smallText: {
    fontSize: 12,
  },

  mediumText: {
    fontSize: 16,
  },

  largeText: {
    fontSize: 20,
  },

  xlargeText: {
    fontSize: 24,
  },

  // مجموعة Avatars
  group: {
    flexDirection: 'row',
    alignItems: 'center',
  },

  groupItem: {
    position: 'relative',
  },
});

export default Avatar;