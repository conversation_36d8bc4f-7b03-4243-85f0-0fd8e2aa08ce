import React, { useEffect, useRef, useState } from 'react';
import {
  View,
  Text,
  Animated,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  PanGestureHandler,
  State
} from 'react-native';
import { FontAwesomeIcon } from '@fortawesome/react-native-fontawesome';
import {
  faCheckCircle,
  faExclamationTriangle,
  faTimesCircle,
  faInfoCircle,
  faTimes
} from '@fortawesome/free-solid-svg-icons';
import { theme } from '../../styles/theme';

const { width } = Dimensions.get('window');

/**
 * مكون Toast للإشعارات المؤقتة
 * يدعم أنواع مختلفة من الإشعارات مع animations
 */
const Toast = ({
  visible = false,
  message,
  type = 'info',
  duration = 3000,
  position = 'top',
  onHide,
  showCloseButton = true,
  style,
  ...props
}) => {
  const translateY = useRef(new Animated.Value(-100)).current;
  const opacity = useRef(new Animated.Value(0)).current;
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    if (visible) {
      showToast();
    } else {
      hideToast();
    }
  }, [visible]);

  const showToast = () => {
    setIsVisible(true);

    Animated.parallel([
      Animated.timing(translateY, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(opacity, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start();

    // إخفاء تلقائي بعد المدة المحددة
    if (duration > 0) {
      setTimeout(() => {
        hideToast();
      }, duration);
    }
  };

  const hideToast = () => {
    Animated.parallel([
      Animated.timing(translateY, {
        toValue: position === 'top' ? -100 : 100,
        duration: 250,
        useNativeDriver: true,
      }),
      Animated.timing(opacity, {
        toValue: 0,
        duration: 250,
        useNativeDriver: true,
      }),
    ]).start(() => {
      setIsVisible(false);
      if (onHide) onHide();
    });
  };

  const getToastConfig = () => {
    switch (type) {
      case 'success':
        return {
          icon: faCheckCircle,
          backgroundColor: theme.colors.success[500],
          textColor: theme.colors.white,
        };
      case 'warning':
        return {
          icon: faExclamationTriangle,
          backgroundColor: theme.colors.warning[500],
          textColor: theme.colors.white,
        };
      case 'error':
        return {
          icon: faTimesCircle,
          backgroundColor: theme.colors.error[500],
          textColor: theme.colors.white,
        };
      case 'info':
      default:
        return {
          icon: faInfoCircle,
          backgroundColor: theme.colors.info[500],
          textColor: theme.colors.white,
        };
    }
  };

  const config = getToastConfig();

  if (!isVisible && !visible) {
    return null;
  }

  return (
    <Animated.View
      style={[
        styles.container,
        styles[`${position}Position`],
        {
          backgroundColor: config.backgroundColor,
          transform: [{ translateY }],
          opacity,
        },
        style,
      ]}
      {...props}
    >
      <View style={styles.content}>
        <FontAwesomeIcon
          icon={config.icon}
          size={20}
          color={config.textColor}
          style={styles.icon}
        />

        <Text style={[styles.message, { color: config.textColor }]} numberOfLines={3}>
          {message}
        </Text>

        {showCloseButton && (
          <TouchableOpacity
            style={styles.closeButton}
            onPress={hideToast}
            hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
          >
            <FontAwesomeIcon
              icon={faTimes}
              size={16}
              color={config.textColor}
            />
          </TouchableOpacity>
        )}
      </View>
    </Animated.View>
  );
};

/**
 * Toast Manager للتحكم في عرض الإشعارات
 */
class ToastManager {
  static toastRef = null;

  static setToastRef(ref) {
    this.toastRef = ref;
  }

  static show(message, type = 'info', duration = 3000) {
    if (this.toastRef) {
      this.toastRef.show(message, type, duration);
    }
  }

  static success(message, duration = 3000) {
    this.show(message, 'success', duration);
  }

  static warning(message, duration = 3000) {
    this.show(message, 'warning', duration);
  }

  static error(message, duration = 3000) {
    this.show(message, 'error', duration);
  }

  static info(message, duration = 3000) {
    this.show(message, 'info', duration);
  }
}

/**
 * مكون ToastProvider لإدارة الإشعارات على مستوى التطبيق
 */
export const ToastProvider = ({ children }) => {
  const [toasts, setToasts] = useState([]);

  const show = (message, type, duration) => {
    const id = Date.now().toString();
    const newToast = { id, message, type, duration, visible: true };

    setToasts(prev => [...prev, newToast]);

    // إزالة التوست بعد المدة المحددة
    setTimeout(() => {
      setToasts(prev => prev.filter(toast => toast.id !== id));
    }, duration + 300); // إضافة وقت للـ animation
  };

  const hide = (id) => {
    setToasts(prev =>
      prev.map(toast =>
        toast.id === id ? { ...toast, visible: false } : toast
      )
    );
  };

  // تسجيل المرجع في ToastManager
  useEffect(() => {
    ToastManager.setToastRef({ show });
  }, []);

  return (
    <View style={{ flex: 1 }}>
      {children}

      {/* عرض الإشعارات */}
      <View style={styles.toastContainer}>
        {toasts.map((toast, index) => (
          <Toast
            key={toast.id}
            visible={toast.visible}
            message={toast.message}
            type={toast.type}
            duration={0} // نتحكم في المدة من هنا
            onHide={() => hide(toast.id)}
            style={{
              zIndex: 1000 + index,
              top: 50 + (index * 80) // ترتيب الإشعارات
            }}
          />
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    left: theme.spacing.md,
    right: theme.spacing.md,
    borderRadius: theme.borderRadius.lg,
    ...theme.shadows.md,
    zIndex: 1000,
  },

  topPosition: {
    top: 50,
  },

  bottomPosition: {
    bottom: 50,
  },

  content: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    minHeight: 56,
  },

  icon: {
    marginRight: theme.spacing.sm,
  },

  message: {
    flex: 1,
    fontSize: theme.typography.fontSize.md,
    fontFamily: theme.typography.fontFamily.primary,
    lineHeight: 20,
    textAlign: 'right',
  },

  closeButton: {
    marginLeft: theme.spacing.sm,
    padding: theme.spacing.xs,
  },

  toastContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    pointerEvents: 'box-none',
  },
});

export { ToastManager };
export default Toast;