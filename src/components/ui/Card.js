import React from 'react';
import {
  View,
  TouchableOpacity,
  StyleSheet
} from 'react-native';
import { theme } from '../../styles/theme';

/**
 * مكون Card محسن وقابل للتخصيص
 * يدعم أنواع مختلفة من البطاقات والتفاعلات
 */
const Card = ({
  children,
  variant = 'default',
  padding = 'medium',
  onPress,
  style,
  disabled = false,
  ...props
}) => {
  const cardStyles = getCardStyles(variant, padding, disabled);

  if (onPress) {
    return (
      <TouchableOpacity
        style={[cardStyles, style]}
        onPress={onPress}
        disabled={disabled}
        activeOpacity={0.9}
        {...props}
      >
        {children}
      </TouchableOpacity>
    );
  }

  return (
    <View style={[cardStyles, style]} {...props}>
      {children}
    </View>
  );
};

// دالة للحصول على أنماط البطاقة
const getCardStyles = (variant, padding, disabled) => {
  const baseStyle = [styles.card];

  // إضافة نمط النوع
  baseStyle.push(styles[variant]);

  // إضافة نمط الحشو
  baseStyle.push(styles[`${padding}Padding`]);

  // إضافة نمط التعطيل
  if (disabled) {
    baseStyle.push(styles.disabled);
  }

  return baseStyle;
};

const styles = StyleSheet.create({
  card: {
    backgroundColor: theme.colors.white,
    borderRadius: theme.borderRadius.lg,
    overflow: 'hidden'
  },

  // أنواع البطاقات
  default: {
    ...theme.shadows.md,
    borderWidth: 0
  },

  elevated: {
    ...theme.shadows.lg,
    borderWidth: 0
  },

  outlined: {
    ...theme.shadows.sm,
    borderWidth: 1,
    borderColor: theme.colors.gray[200]
  },

  flat: {
    shadowOpacity: 0,
    elevation: 0,
    borderWidth: 0
  },

  // أحجام الحشو
  nonePadding: {
    padding: 0
  },

  smallPadding: {
    padding: theme.spacing.sm
  },

  mediumPadding: {
    padding: theme.spacing.md
  },

  largePadding: {
    padding: theme.spacing.lg
  },

  // حالة التعطيل
  disabled: {
    opacity: 0.6
  }
});

export default Card;