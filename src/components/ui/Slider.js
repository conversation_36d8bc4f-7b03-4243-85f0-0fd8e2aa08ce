import React, { useState, useRef, useCallback } from 'react';
import {
  View,
  Text,
  PanGestureHandler,
  State,
  Animated,
  StyleSheet,
  Dimensions
} from 'react-native';
import { theme } from '../../styles/theme';

const { width: screenWidth } = Dimensions.get('window');

/**
 * مكون Slider متقدم للقيم الرقمية
 * يدعم النطاقات والخطوات والتنسيقات المختلفة
 */
const Slider = ({
  value = 0,
  minimumValue = 0,
  maximumValue = 100,
  step = 1,
  onValueChange,
  onSlidingStart,
  onSlidingComplete,
  disabled = false,
  trackStyle,
  thumbStyle,
  minimumTrackTintColor,
  maximumTrackTintColor,
  thumbTintColor,
  showValue = false,
  valueFormatter,
  label,
  style,
  ...props
}) => {
  const [sliderWidth, setSliderWidth] = useState(200);
  const [isDragging, setIsDragging] = useState(false);
  const animatedValue = useRef(new Animated.Value(value)).current;
  const panGesture = useRef();

  // تحويل القيمة إلى موقع على الشريط
  const valueToPosition = useCallback((val) => {
    const range = maximumValue - minimumValue;
    const percentage = (val - minimumValue) / range;
    return percentage * sliderWidth;
  }, [minimumValue, maximumValue, sliderWidth]);

  // تحويل الموقع إلى قيمة
  const positionToValue = useCallback((position) => {
    const percentage = position / sliderWidth;
    const range = maximumValue - minimumValue;
    let newValue = minimumValue + (percentage * range);

    // تطبيق الخطوة
    if (step > 0) {
      newValue = Math.round(newValue / step) * step;
    }

    // التأكد من أن القيمة ضمن النطاق
    return Math.max(minimumValue, Math.min(maximumValue, newValue));
  }, [minimumValue, maximumValue, sliderWidth, step]);

  const onGestureEvent = Animated.event(
    [{ nativeEvent: { translationX: animatedValue } }],
    { useNativeDriver: false }
  );

  const onHandlerStateChange = (event) => {
    const { state, translationX } = event.nativeEvent;

    switch (state) {
      case State.BEGAN:
        setIsDragging(true);
        if (onSlidingStart) {
          onSlidingStart(value);
        }
        break;

      case State.ACTIVE:
        const currentPosition = valueToPosition(value) + translationX;
        const newValue = positionToValue(currentPosition);

        if (onValueChange && newValue !== value) {
          onValueChange(newValue);
        }
        break;

      case State.END:
      case State.CANCELLED:
        setIsDragging(false);
        animatedValue.setValue(0);

        if (onSlidingComplete) {
          onSlidingComplete(value);
        }
        break;
    }
  };

  const onTrackLayout = (event) => {
    const { width } = event.nativeEvent.layout;
    setSliderWidth(width - 20); // 20 هو عرض الـ thumb
  };

  const formatValue = (val) => {
    if (valueFormatter) {
      return valueFormatter(val);
    }
    return val.toString();
  };

  const thumbPosition = valueToPosition(value);

  return (
    <View style={[styles.container, style]} {...props}>
      {label && (
        <Text style={styles.label}>{label}</Text>
      )}

      <View style={styles.sliderContainer}>
        {/* عرض القيمة الدنيا */}
        <Text style={styles.valueText}>
          {formatValue(minimumValue)}
        </Text>

        <View style={styles.trackContainer} onLayout={onTrackLayout}>
          {/* المسار الخلفي */}
          <View
            style={[
              styles.track,
              styles.maximumTrack,
              trackStyle,
              { backgroundColor: maximumTrackTintColor || theme.colors.gray[200] },
            ]}
          />

          {/* المسار النشط */}
          <View
            style={[
              styles.track,
              styles.minimumTrack,
              {
                width: thumbPosition,
                backgroundColor: minimumTrackTintColor || theme.colors.primary[500],
              },
            ]}
          />

          {/* الـ Thumb */}
          <PanGestureHandler
            ref={panGesture}
            onGestureEvent={onGestureEvent}
            onHandlerStateChange={onHandlerStateChange}
            enabled={!disabled}
          >
            <Animated.View
              style={[
                styles.thumb,
                thumbStyle,
                {
                  backgroundColor: thumbTintColor || theme.colors.white,
                  left: thumbPosition - 10, // نصف عرض الـ thumb
                  transform: [
                    { translateX: animatedValue },
                    { scale: isDragging ? 1.2 : 1 },
                  ],
                },
                disabled && styles.disabledThumb,
              ]}
            >
              {showValue && (
                <View style={styles.valueContainer}>
                  <Text style={styles.thumbValue}>
                    {formatValue(value)}
                  </Text>
                </View>
              )}
            </Animated.View>
          </PanGestureHandler>
        </View>

        {/* عرض القيمة العليا */}
        <Text style={styles.valueText}>
          {formatValue(maximumValue)}
        </Text>
      </View>

      {showValue && !isDragging && (
        <Text style={styles.currentValue}>
          القيمة الحالية: {formatValue(value)}
        </Text>
      )}
    </View>
  );
};

/**
 * مكون RangeSlider للنطاقات
 */
export const RangeSlider = ({
  lowValue = 0,
  highValue = 100,
  minimumValue = 0,
  maximumValue = 100,
  step = 1,
  onValuesChange,
  disabled = false,
  style,
  ...props
}) => {
  const [sliderWidth, setSliderWidth] = useState(200);
  const [activeThumbs, setActiveThumbs] = useState({ low: false, high: false });

  const valueToPosition = useCallback((val) => {
    const range = maximumValue - minimumValue;
    const percentage = (val - minimumValue) / range;
    return percentage * sliderWidth;
  }, [minimumValue, maximumValue, sliderWidth]);

  const positionToValue = useCallback((position) => {
    const percentage = position / sliderWidth;
    const range = maximumValue - minimumValue;
    let newValue = minimumValue + (percentage * range);

    if (step > 0) {
      newValue = Math.round(newValue / step) * step;
    }

    return Math.max(minimumValue, Math.min(maximumValue, newValue));
  }, [minimumValue, maximumValue, sliderWidth, step]);

  const onTrackLayout = (event) => {
    const { width } = event.nativeEvent.layout;
    setSliderWidth(width - 20);
  };

  const lowPosition = valueToPosition(lowValue);
  const highPosition = valueToPosition(highValue);

  return (
    <View style={[styles.container, style]} {...props}>
      <View style={styles.sliderContainer}>
        <Text style={styles.valueText}>{minimumValue}</Text>

        <View style={styles.trackContainer} onLayout={onTrackLayout}>
          {/* المسار الخلفي */}
          <View style={[styles.track, styles.maximumTrack]} />

          {/* المسار النشط بين القيمتين */}
          <View
            style={[
              styles.track,
              styles.rangeTrack,
              {
                left: lowPosition,
                width: highPosition - lowPosition,
                backgroundColor: theme.colors.primary[500],
              },
            ]}
          />

          {/* Thumb الأدنى */}
          <Animated.View
            style={[
              styles.thumb,
              {
                left: lowPosition - 10,
                transform: [{ scale: activeThumbs.low ? 1.2 : 1 }],
              },
            ]}
          />

          {/* Thumb الأعلى */}
          <Animated.View
            style={[
              styles.thumb,
              {
                left: highPosition - 10,
                transform: [{ scale: activeThumbs.high ? 1.2 : 1 }],
              },
            ]}
          />
        </View>

        <Text style={styles.valueText}>{maximumValue}</Text>
      </View>

      <View style={styles.rangeValues}>
        <Text style={styles.rangeValue}>من: {lowValue}</Text>
        <Text style={styles.rangeValue}>إلى: {highValue}</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingVertical: theme.spacing.md,
  },

  label: {
    fontSize: theme.typography.fontSize.md,
    fontFamily: theme.typography.fontFamily.primarySemiBold,
    color: theme.colors.text,
    marginBottom: theme.spacing.sm,
    textAlign: 'right',
  },

  sliderContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: theme.spacing.md,
  },

  trackContainer: {
    flex: 1,
    height: 40,
    justifyContent: 'center',
    position: 'relative',
  },

  track: {
    height: 4,
    borderRadius: 2,
    position: 'absolute',
  },

  maximumTrack: {
    width: '100%',
    backgroundColor: theme.colors.gray[200],
  },

  minimumTrack: {
    backgroundColor: theme.colors.primary[500],
  },

  rangeTrack: {
    backgroundColor: theme.colors.primary[500],
  },

  thumb: {
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: theme.colors.white,
    position: 'absolute',
    top: 10,
    ...theme.shadows.md,
    borderWidth: 2,
    borderColor: theme.colors.primary[500],
  },

  disabledThumb: {
    backgroundColor: theme.colors.gray[300],
    borderColor: theme.colors.gray[400],
  },

  valueContainer: {
    position: 'absolute',
    top: -30,
    left: -15,
    width: 50,
    alignItems: 'center',
    backgroundColor: theme.colors.gray[800],
    borderRadius: theme.borderRadius.sm,
    paddingVertical: 4,
    paddingHorizontal: 8,
  },

  thumbValue: {
    color: theme.colors.white,
    fontSize: theme.typography.fontSize.xs,
    fontFamily: theme.typography.fontFamily.primaryBold,
  },

  valueText: {
    fontSize: theme.typography.fontSize.sm,
    fontFamily: theme.typography.fontFamily.primary,
    color: theme.colors.textSecondary,
    minWidth: 30,
    textAlign: 'center',
  },

  currentValue: {
    fontSize: theme.typography.fontSize.sm,
    fontFamily: theme.typography.fontFamily.primary,
    color: theme.colors.text,
    textAlign: 'center',
    marginTop: theme.spacing.sm,
  },

  // Range Slider
  rangeValues: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: theme.spacing.sm,
  },

  rangeValue: {
    fontSize: theme.typography.fontSize.sm,
    fontFamily: theme.typography.fontFamily.primarySemiBold,
    color: theme.colors.primary[600],
  },
});

export default Slider;