import React, { useRef, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Animated,
  StyleSheet
} from 'react-native';
import { FontAwesomeIcon } from '@fortawesome/react-native-fontawesome';
import { faCheck, faMinus } from '@fortawesome/free-solid-svg-icons';
import { theme } from '../../styles/theme';

/**
 * مكون Checkbox متقدم مع animations
 * يدعم الحالات المختلفة والأنماط المتنوعة
 */
const Checkbox = ({
  checked = false,
  indeterminate = false,
  onPress,
  disabled = false,
  size = 'medium',
  variant = 'default',
  label,
  description,
  error,
  color,
  style,
  labelStyle,
  checkboxStyle,
  ...props
}) => {
  const scaleAnim = useRef(new Animated.Value(checked ? 1 : 0)).current;
  const opacityAnim = useRef(new Animated.Value(checked ? 1 : 0)).current;

  useEffect(() => {
    Animated.parallel([
      Animated.spring(scaleAnim, {
        toValue: checked || indeterminate ? 1 : 0,
        useNativeDriver: true,
        tension: 150,
        friction: 7,
      }),
      Animated.timing(opacityAnim, {
        toValue: checked || indeterminate ? 1 : 0,
        duration: 150,
        useNativeDriver: true,
      }),
    ]).start();
  }, [checked, indeterminate]);

  const handlePress = () => {
    if (!disabled && onPress) {
      onPress(!checked);
    }
  };

  const getCheckboxStyles = () => {
    const baseStyle = [styles.checkbox];

    // إضافة نمط الحجم
    baseStyle.push(styles[`${size}Checkbox`]);

    // إضافة نمط النوع
    baseStyle.push(styles[variant]);

    // إضافة نمط الحالة
    if (checked || indeterminate) {
      baseStyle.push(styles.checkedCheckbox);
      baseStyle.push(styles[`${variant}Checked`]);
    }

    if (disabled) {
      baseStyle.push(styles.disabledCheckbox);
    }

    if (error) {
      baseStyle.push(styles.errorCheckbox);
    }

    // إضافة لون مخصص
    if (color && (checked || indeterminate)) {
      baseStyle.push({ backgroundColor: color, borderColor: color });
    }

    return baseStyle;
  };

  const getIconSize = () => {
    switch (size) {
      case 'small': return 12;
      case 'medium': return 14;
      case 'large': return 16;
      default: return 14;
    }
  };

  const renderIcon = () => {
    if (indeterminate) {
      return (
        <FontAwesomeIcon
          icon={faMinus}
          size={getIconSize()}
          color={theme.colors.white}
        />
      );
    }

    if (checked) {
      return (
        <FontAwesomeIcon
          icon={faCheck}
          size={getIconSize()}
          color={theme.colors.white}
        />
      );
    }

    return null;
  };

  return (
    <TouchableOpacity
      style={[styles.container, style]}
      onPress={handlePress}
      disabled={disabled}
      activeOpacity={0.7}
      {...props}
    >
      <View style={[getCheckboxStyles(), checkboxStyle]}>
        <Animated.View
          style={[
            styles.iconContainer,
            {
              transform: [{ scale: scaleAnim }],
              opacity: opacityAnim,
            },
          ]}
        >
          {renderIcon()}
        </Animated.View>
      </View>

      {(label || description) && (
        <View style={styles.textContainer}>
          {label && (
            <Text
              style={[
                styles.label,
                styles[`${size}Label`],
                disabled && styles.disabledLabel,
                error && styles.errorLabel,
                labelStyle,
              ]}
            >
              {label}
            </Text>
          )}

          {description && (
            <Text
              style={[
                styles.description,
                disabled && styles.disabledDescription,
              ]}
            >
              {description}
            </Text>
          )}

          {error && (
            <Text style={styles.errorText}>
              {error}
            </Text>
          )}
        </View>
      )}
    </TouchableOpacity>
  );
};

/**
 * مكون CheckboxGroup لإدارة مجموعة من الـ checkboxes
 */
export const CheckboxGroup = ({
  options = [],
  value = [],
  onChange,
  disabled = false,
  size = 'medium',
  variant = 'default',
  direction = 'vertical',
  style,
  ...props
}) => {
  const handleOptionChange = (optionValue, isChecked) => {
    if (!onChange) return;

    let newValue;
    if (isChecked) {
      newValue = [...value, optionValue];
    } else {
      newValue = value.filter(v => v !== optionValue);
    }

    onChange(newValue);
  };

  return (
    <View
      style={[
        styles.group,
        direction === 'horizontal' && styles.horizontalGroup,
        style,
      ]}
      {...props}
    >
      {options.map((option, index) => {
        const optionValue = typeof option === 'string' ? option : option.value;
        const optionLabel = typeof option === 'string' ? option : option.label;
        const optionDescription = typeof option === 'object' ? option.description : null;
        const optionDisabled = typeof option === 'object' ? option.disabled : false;

        return (
          <Checkbox
            key={optionValue || index}
            checked={value.includes(optionValue)}
            onPress={(isChecked) => handleOptionChange(optionValue, isChecked)}
            disabled={disabled || optionDisabled}
            size={size}
            variant={variant}
            label={optionLabel}
            description={optionDescription}
            style={styles.groupItem}
          />
        );
      })}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },

  // Checkbox
  checkbox: {
    borderWidth: 2,
    borderRadius: theme.borderRadius.sm,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: theme.colors.white,
    borderColor: theme.colors.gray[300],
  },

  // أحجام Checkbox
  smallCheckbox: {
    width: 16,
    height: 16,
  },

  mediumCheckbox: {
    width: 20,
    height: 20,
  },

  largeCheckbox: {
    width: 24,
    height: 24,
  },

  // أنواع Checkbox
  default: {
    // النمط الافتراضي
  },

  rounded: {
    borderRadius: theme.borderRadius.md,
  },

  // حالات Checkbox
  checkedCheckbox: {
    backgroundColor: theme.colors.primary[500],
    borderColor: theme.colors.primary[500],
  },

  defaultChecked: {
    backgroundColor: theme.colors.primary[500],
    borderColor: theme.colors.primary[500],
  },

  roundedChecked: {
    backgroundColor: theme.colors.primary[500],
    borderColor: theme.colors.primary[500],
  },

  disabledCheckbox: {
    backgroundColor: theme.colors.gray[100],
    borderColor: theme.colors.gray[200],
    opacity: 0.6,
  },

  errorCheckbox: {
    borderColor: theme.colors.error[500],
  },

  // أيقونة
  iconContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },

  // النصوص
  textContainer: {
    flex: 1,
    marginLeft: theme.spacing.sm,
  },

  label: {
    fontFamily: theme.typography.fontFamily.primary,
    color: theme.colors.text,
    textAlign: 'right',
  },

  // أحجام النص
  smallLabel: {
    fontSize: theme.typography.fontSize.sm,
  },

  mediumLabel: {
    fontSize: theme.typography.fontSize.md,
  },

  largeLabel: {
    fontSize: theme.typography.fontSize.lg,
  },

  description: {
    fontSize: theme.typography.fontSize.sm,
    fontFamily: theme.typography.fontFamily.primary,
    color: theme.colors.textSecondary,
    marginTop: theme.spacing.xs,
    textAlign: 'right',
  },

  disabledLabel: {
    color: theme.colors.gray[400],
  },

  disabledDescription: {
    color: theme.colors.gray[300],
  },

  errorLabel: {
    color: theme.colors.error[600],
  },

  errorText: {
    fontSize: theme.typography.fontSize.sm,
    fontFamily: theme.typography.fontFamily.primary,
    color: theme.colors.error[500],
    marginTop: theme.spacing.xs,
    textAlign: 'right',
  },

  // Group
  group: {
    gap: theme.spacing.md,
  },

  horizontalGroup: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },

  groupItem: {
    // يتم تحديد المسافات من الـ group
  },
});

export default Checkbox;