import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet
} from 'react-native';
import { FontAwesomeIcon } from '@fortawesome/react-native-fontawesome';
import {
  faSun,
  faMoon,
  faDesktop,
  faCheck
} from '@fortawesome/free-solid-svg-icons';
import { useTheme } from '../../contexts/ThemeContext';
import { Card } from './index';

/**
 * مكون ThemeSelector لاختيار الثيم
 * يدعم Light Mode, Dark Mode, وSystem Theme
 */
const ThemeSelector = ({ style, onThemeChange }) => {
  const {
    currentThemeName,
    isSystemTheme,
    setTheme,
    useSystemTheme
  } = useTheme();

  const themeOptions = [
    {
      id: 'system',
      name: 'تلقائي (النظام)',
      description: 'يتبع إعدادات النظام',
      icon: faDesktop,
      isActive: isSystemTheme,
      onPress: useSystemTheme
    },
    {
      id: 'light',
      name: 'الوضع النهاري',
      description: 'خلفية فاتحة ونص داكن',
      icon: faSun,
      isActive: !isSystemTheme && currentThemeName === 'light',
      onPress: () => setTheme('light')
    },
    {
      id: 'dark',
      name: 'الوضع الليلي',
      description: 'خلفية داكنة ونص فاتح',
      icon: faMoon,
      isActive: !isSystemTheme && currentThemeName === 'dark',
      onPress: () => setTheme('dark')
    }
  ];

  const handleThemeSelect = (option) => {
    option.onPress();
    if (onThemeChange) {
      onThemeChange(option.id);
    }
  };

  return (
    <Card style={[styles.container, style]}>
      <Text style={styles.title}>اختيار المظهر</Text>
      <Text style={styles.subtitle}>
        اختر المظهر المفضل لديك للتطبيق
      </Text>

      <View style={styles.optionsContainer}>
        {themeOptions.map((option) => (
          <ThemeOption
            key={option.id}
            option={option}
            onSelect={() => handleThemeSelect(option)}
          />
        ))}
      </View>
    </Card>
  );
};

/**
 * مكون خيار الثيم الفردي
 */
const ThemeOption = ({ option, onSelect }) => {
  const { theme } = useTheme();

  return (
    <TouchableOpacity
      style={[
        styles.option,
        option.isActive && styles.activeOption,
        { borderColor: option.isActive ? theme.colors.primary[500] : theme.colors.border }
      ]}
      onPress={onSelect}
      activeOpacity={0.7}
    >
      <View style={styles.optionContent}>
        <View style={styles.optionIcon}>
          <FontAwesomeIcon
            icon={option.icon}
            size={20}
            color={option.isActive ? theme.colors.primary[500] : theme.colors.gray[500]}
          />
        </View>

        <View style={styles.optionText}>
          <Text style={[
            styles.optionName,
            { color: theme.colors.text }
          ]}>
            {option.name}
          </Text>
          <Text style={[
            styles.optionDescription,
            { color: theme.colors.textSecondary }
          ]}>
            {option.description}
          </Text>
        </View>

        {option.isActive && (
          <View style={styles.checkIcon}>
            <FontAwesomeIcon
              icon={faCheck}
              size={16}
              color={theme.colors.primary[500]}
            />
          </View>
        )}
      </View>
    </TouchableOpacity>
  );
};

/**
 * مكون ThemeToggle بسيط للتبديل السريع
 */
export const ThemeToggle = ({ style }) => {
  const { isDark, toggleTheme, theme } = useTheme();

  return (
    <TouchableOpacity
      style={[styles.toggleButton, style]}
      onPress={toggleTheme}
      activeOpacity={0.7}
    >
      <FontAwesomeIcon
        icon={isDark ? faSun : faMoon}
        size={20}
        color={theme.colors.text}
      />
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 20,
  },

  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
    textAlign: 'right',
  },

  subtitle: {
    fontSize: 14,
    marginBottom: 20,
    textAlign: 'right',
    opacity: 0.7,
  },

  optionsContainer: {
    gap: 12,
  },

  option: {
    borderWidth: 2,
    borderRadius: 12,
    padding: 16,
    backgroundColor: 'transparent',
  },

  activeOption: {
    backgroundColor: 'rgba(102, 126, 234, 0.05)',
  },

  optionContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },

  optionIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(102, 126, 234, 0.1)',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },

  optionText: {
    flex: 1,
  },

  optionName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
    textAlign: 'right',
  },

  optionDescription: {
    fontSize: 14,
    textAlign: 'right',
  },

  checkIcon: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: 'rgba(102, 126, 234, 0.1)',
    alignItems: 'center',
    justifyContent: 'center',
  },

  // Toggle Button
  toggleButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
  },
});

export default ThemeSelector;