import React from 'react';
import {
  View,
  Text,
  StyleSheet
} from 'react-native';
import { theme } from '../../styles/theme';

/**
 * مكون Badge للعلامات والتسميات
 * يدعم أنواع وأحجام مختلفة
 */
const Badge = ({
  children,
  variant = 'default',
  size = 'medium',
  position = 'top-right',
  count,
  maxCount = 99,
  showZero = false,
  dot = false,
  style,
  textStyle,
  ...props
}) => {
  // تحديد النص المعروض
  const getDisplayText = () => {
    if (dot) return '';
    if (count !== undefined) {
      if (count === 0 && !showZero) return '';
      return count > maxCount ? `${maxCount}+` : count.toString();
    }
    return children;
  };

  const displayText = getDisplayText();

  // إخفاء Badge إذا لم يكن هناك محتوى
  if (!displayText && !dot && count === 0 && !showZero) {
    return null;
  }

  const getBadgeStyles = () => {
    const baseStyle = [styles.badge];

    // إضافة نمط النوع
    baseStyle.push(styles[variant]);

    // إضافة نمط الحجم
    baseStyle.push(styles[`${size}Size`]);

    // إضافة نمط النقطة
    if (dot) {
      baseStyle.push(styles.dot);
    }

    return baseStyle;
  };

  const getTextStyles = () => {
    const baseStyle = [styles.text];

    // إضافة نمط النص حسب النوع
    baseStyle.push(styles[`${variant}Text`]);

    // إضافة نمط النص حسب الحجم
    baseStyle.push(styles[`${size}Text`]);

    return baseStyle;
  };

  return (
    <View style={[getBadgeStyles(), style]} {...props}>
      {!dot && (
        <Text style={[getTextStyles(), textStyle]} numberOfLines={1}>
          {displayText}
        </Text>
      )}
    </View>
  );
};

/**
 * مكون BadgeWrapper لوضع Badge على مكون آخر
 */
export const BadgeWrapper = ({
  children,
  badge,
  position = 'top-right',
  offset = { x: 0, y: 0 },
  style,
}) => {
  const getPositionStyle = () => {
    const baseOffset = 8;

    switch (position) {
      case 'top-right':
        return {
          top: -baseOffset + offset.y,
          right: -baseOffset + offset.x,
        };
      case 'top-left':
        return {
          top: -baseOffset + offset.y,
          left: -baseOffset + offset.x,
        };
      case 'bottom-right':
        return {
          bottom: -baseOffset + offset.y,
          right: -baseOffset + offset.x,
        };
      case 'bottom-left':
        return {
          bottom: -baseOffset + offset.y,
          left: -baseOffset + offset.x,
        };
      default:
        return {
          top: -baseOffset + offset.y,
          right: -baseOffset + offset.x,
        };
    }
  };

  return (
    <View style={[styles.wrapper, style]}>
      {children}
      {badge && (
        <View style={[styles.badgePosition, getPositionStyle()]}>
          {badge}
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  badge: {
    borderRadius: theme.borderRadius.full,
    alignItems: 'center',
    justifyContent: 'center',
    minWidth: 20,
    minHeight: 20,
  },

  // أنواع Badge
  default: {
    backgroundColor: theme.colors.gray[500],
  },

  primary: {
    backgroundColor: theme.colors.primary[600],
  },

  secondary: {
    backgroundColor: theme.colors.secondary[600],
  },

  success: {
    backgroundColor: theme.colors.success[500],
  },

  warning: {
    backgroundColor: theme.colors.warning[500],
  },

  error: {
    backgroundColor: theme.colors.error[500],
  },

  info: {
    backgroundColor: theme.colors.info[500],
  },

  outline: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: theme.colors.gray[300],
  },

  // أحجام Badge
  smallSize: {
    minWidth: 16,
    minHeight: 16,
    paddingHorizontal: 4,
  },

  mediumSize: {
    minWidth: 20,
    minHeight: 20,
    paddingHorizontal: 6,
  },

  largeSize: {
    minWidth: 24,
    minHeight: 24,
    paddingHorizontal: 8,
  },

  // نمط النقطة
  dot: {
    minWidth: 8,
    minHeight: 8,
    borderRadius: 4,
    paddingHorizontal: 0,
  },

  // أنماط النص
  text: {
    fontFamily: theme.typography.fontFamily.primaryBold,
    textAlign: 'center',
  },

  // ألوان النص حسب النوع
  defaultText: {
    color: theme.colors.white,
  },

  primaryText: {
    color: theme.colors.white,
  },

  secondaryText: {
    color: theme.colors.white,
  },

  successText: {
    color: theme.colors.white,
  },

  warningText: {
    color: theme.colors.white,
  },

  errorText: {
    color: theme.colors.white,
  },

  infoText: {
    color: theme.colors.white,
  },

  outlineText: {
    color: theme.colors.gray[700],
  },

  // أحجام النص
  smallText: {
    fontSize: 10,
    lineHeight: 12,
  },

  mediumText: {
    fontSize: 12,
    lineHeight: 14,
  },

  largeText: {
    fontSize: 14,
    lineHeight: 16,
  },

  // Wrapper styles
  wrapper: {
    position: 'relative',
  },

  badgePosition: {
    position: 'absolute',
    zIndex: 1,
  },
});

export default Badge;