import React, {useState, useEffect} from 'react';
import {View, Text, Pressable, Image, StyleSheet, TouchableOpacity} from 'react-native';

import Svg, {Path} from 'react-native-svg';
import styles from '../styles';
import {FontAwesomeIcon} from '@fortawesome/react-native-fontawesome';
import {faBars, faBell, faSearch} from '@fortawesome/free-solid-svg-icons';

import {AuthStore} from '../store/auth';
import {ProductStore} from '../store/product';
import {observer} from 'mobx-react';
import { theme } from '../styles/theme';

export const Header = observer(({navigationRef, hideCart, setOpenMenu, showSearch = false, onSearchPress}) => {
    const {
        state: {cart},
    } = ProductStore;

    return (
        <View style={headerStyles.container}>
            {/* قسم اليسار - القائمة */}
            <TouchableOpacity
                style={headerStyles.iconButton}
                onPress={() => setOpenMenu(true)}
                activeOpacity={0.7}
            >
                <FontAwesomeIcon
                    icon={faBars}
                    size={20}
                    color={theme.colors.gray[700]}
                />
            </TouchableOpacity>

            {/* قسم الوسط - الشعار */}
            <View style={headerStyles.logoContainer}>
                <View style={headerStyles.logoWrapper}>
                    <Svg height="32" width="32" viewBox="0 0 512 512">
                        <Path
                            fill={theme.colors.primary[600]}
                            d="M135.2 117.4L109.1 192H402.9l-26.1-74.6C372.3 104.6 360.2 96 346.6 96H165.4c-13.6 0-25.7 8.6-30.2 21.4zM39.6 196.8L74.8 96.3C88.3 57.8 124.6 32 165.4 32H346.6c40.8 0 77.1 25.8 90.6 64.3l35.2 100.5c23.2 9.6 39.6 32.5 39.6 59.2V304c0 26.5-21.5 48-48 48H416c-26.5 0-48-21.5-48-48V288H144v16c0 26.5-21.5 48-48 48H48c-26.5 0-48-21.5-48-48V256c0-26.7 16.4-49.6 39.6-59.2z"
                        />
                    </Svg>
                    <Text style={headerStyles.logoText}>دليلك</Text>
                </View>
            </View>

            {/* قسم اليمين - الأيقونات */}
            <View style={headerStyles.rightSection}>
                {/* أيقونة البحث */}
                {showSearch && (
                    <TouchableOpacity
                        style={headerStyles.iconButton}
                        onPress={onSearchPress}
                        activeOpacity={0.7}
                    >
                        <FontAwesomeIcon
                            icon={faSearch}
                            size={18}
                            color={theme.colors.gray[700]}
                        />
                    </TouchableOpacity>
                )}

                {/* أيقونة الإشعارات */}
                <TouchableOpacity
                    style={headerStyles.iconButton}
                    onPress={() => {/* TODO: إضافة وظيفة الإشعارات */}}
                    activeOpacity={0.7}
                >
                    <FontAwesomeIcon
                        icon={faBell}
                        size={18}
                        color={theme.colors.gray[700]}
                    />
                    {/* نقطة الإشعارات الجديدة */}
                    <View style={headerStyles.notificationDot} />
                </TouchableOpacity>

                {/* أيقونة السلة */}
                {!hideCart && (
                    <TouchableOpacity
                        style={headerStyles.cartButton}
                        onPress={() => navigationRef.navigate('Cart')}
                        activeOpacity={0.7}
                    >
                        <Image
                            style={headerStyles.cartIcon}
                            source={require('../../assets/icons/bag.png')}
                        />
                        {cart.length > 0 && (
                            <View style={headerStyles.cartBadge}>
                                <Text style={headerStyles.cartBadgeText}>
                                    {cart.length > 99 ? '99+' : cart.length}
                                </Text>
                            </View>
                        )}
                    </TouchableOpacity>
                )}
            </View>
        </View>
    );
});

// أنماط Header محسنة
const headerStyles = StyleSheet.create({
    container: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: theme.spacing.md,
        paddingVertical: theme.spacing.sm,
        backgroundColor: theme.colors.white,
        borderBottomWidth: 1,
        borderBottomColor: theme.colors.gray[100],
        ...theme.shadows.sm,
        minHeight: 60
    },

    iconButton: {
        width: 40,
        height: 40,
        borderRadius: theme.borderRadius.md,
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: theme.colors.gray[50]
    },

    logoContainer: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center'
    },

    logoWrapper: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: theme.spacing.sm
    },

    logoText: {
        fontSize: theme.typography.fontSize.xl,
        fontFamily: theme.typography.fontFamily.primaryBold,
        color: theme.colors.gray[800],
        textAlign: 'center'
    },

    rightSection: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: theme.spacing.sm
    },

    notificationDot: {
        position: 'absolute',
        top: 8,
        right: 8,
        width: 8,
        height: 8,
        borderRadius: 4,
        backgroundColor: theme.colors.error[500]
    },

    cartButton: {
        width: 40,
        height: 40,
        alignItems: 'center',
        justifyContent: 'center',
        position: 'relative'
    },

    cartIcon: {
        width: 24,
        height: 24,
        tintColor: theme.colors.gray[700]
    },

    cartBadge: {
        position: 'absolute',
        top: -2,
        right: -2,
        backgroundColor: theme.colors.error[500],
        borderRadius: theme.borderRadius.full,
        minWidth: 18,
        height: 18,
        alignItems: 'center',
        justifyContent: 'center',
        paddingHorizontal: 4
    },

    cartBadgeText: {
        color: theme.colors.white,
        fontSize: theme.typography.fontSize.xs,
        fontFamily: theme.typography.fontFamily.primaryBold,
        textAlign: 'center'
    }
});
