import {View, Text, Pressable, TouchableOpacity} from 'react-native';
import React from 'react';
import styles from '../styles';

import {FontAwesomeIcon} from '@fortawesome/react-native-fontawesome';
import {
    faBoxes,
    faCartShopping,
    faCheck,
    faCheckCircle,
    faChevronRight,
    faHeart,
    faHome,
    faSearch,
    faUser,
    faSignOutAlt,
    faCog,
    faLanguage,
    faDollarSign,
} from '@fortawesome/free-solid-svg-icons';
import Svg, {Path} from 'react-native-svg';

const Menu = ({navigationRef}) => {
    console.log(navigationRef.current?.getCurrentRoute());

    return (
        <View style={styles.menu}>
            <View style={styles.menuTop}>
                <View
                    style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'center',
                    }}>
                    <Svg height="50" width="50" viewBox="0 0 448 512">
                        <Path
                            fill="#21282F"
                            d="M271.5 432l-68 32C88.5 453.7 0 392.5 0 318.2c0-71.5 82.5-131 191.7-144.3v43c-71.5 12.5-124 53-124 101.3 0 51 58.5 93.3 135.7 103v-340l68-33.2v384zM448 291l-131.3-28.5 36.8-20.7c-19.5-11.5-43.5-20-70-24.8v-43c46.2 5.5 87.7 19.5 120.3 39.3l35-19.8L448 291z"
                        />
                    </Svg>
                    <Text style={{...styles.headerText, fontSize: 24}}>
                        دليل
                    </Text>
                </View>
            </View>

            <View style={styles.menuOptions}>
                {/* قسم الحساب */}
                <TouchableOpacity
                    style={styles.menuOption}
                    onPress={() => navigationRef.navigate('Profile')}>
                    <FontAwesomeIcon icon={faUser} color="#9551E8" />
                    <Text style={styles.menuText}>حسابي</Text>
                    <FontAwesomeIcon color="#ccc" icon={faChevronRight} />
                </TouchableOpacity>

                <TouchableOpacity
                    style={styles.menuOption}
                    onPress={() => navigationRef.navigate('Wishlist')}>
                    <FontAwesomeIcon icon={faHeart} color="#9551E8" />
                    <Text style={styles.menuText}>قائمة الرغبات</Text>
                    <FontAwesomeIcon color="#ccc" icon={faChevronRight} />
                </TouchableOpacity>

                <TouchableOpacity
                    style={styles.menuOption}
                    onPress={() => navigationRef.navigate('Cart')}>
                    <FontAwesomeIcon icon={faCartShopping} color="#9551E8" />
                    <Text style={styles.menuText}>عربة التسوق</Text>
                    <FontAwesomeIcon color="#ccc" icon={faChevronRight} />
                </TouchableOpacity>

                {/* خط فاصل */}
                <View style={styles.menuDivider} />

                {/* قسم التصفح */}
                <TouchableOpacity
                    style={styles.menuOption}
                    onPress={() => navigationRef.navigate('Dashboard')}>
                    <FontAwesomeIcon icon={faHome} color="#666" />
                    <Text style={styles.menuText}>الرئيسية</Text>
                    <FontAwesomeIcon color="#ccc" icon={faChevronRight} />
                </TouchableOpacity>

                <TouchableOpacity
                    style={styles.menuOption}
                    onPress={() => navigationRef.navigate('Category')}>
                    <FontAwesomeIcon icon={faBoxes} color="#666" />
                    <Text style={styles.menuText}>جميع الفئات</Text>
                    <FontAwesomeIcon color="#ccc" icon={faChevronRight} />
                </TouchableOpacity>

                <TouchableOpacity
                    style={styles.menuOption}
                    onPress={() => navigationRef.navigate('Search')}>
                    <FontAwesomeIcon icon={faSearch} color="#666" />
                    <Text style={styles.menuText}>البحث</Text>
                    <FontAwesomeIcon color="#ccc" icon={faChevronRight} />
                </TouchableOpacity>

                <TouchableOpacity
                    style={styles.menuOption}
                    onPress={() => navigationRef.navigate('Orders')}>
                    <FontAwesomeIcon icon={faCheckCircle} color="#666" />
                    <Text style={styles.menuText}>طلباتي</Text>
                    <FontAwesomeIcon color="#ccc" icon={faChevronRight} />
                </TouchableOpacity>

                {/* خط فاصل */}
                <View style={styles.menuDivider} />

                {/* قسم الإعدادات */}
                <TouchableOpacity style={styles.menuOption}>
                    <FontAwesomeIcon icon={faLanguage} color="#666" />
                    <Text style={styles.menuText}>اللغة</Text>
                    <FontAwesomeIcon color="#ccc" icon={faChevronRight} />
                </TouchableOpacity>

                <TouchableOpacity style={styles.menuOption}>
                    <FontAwesomeIcon icon={faDollarSign} color="#666" />
                    <Text style={styles.menuText}>العملة</Text>
                    <FontAwesomeIcon color="#ccc" icon={faChevronRight} />
                </TouchableOpacity>

                <TouchableOpacity style={styles.menuOption}>
                    <FontAwesomeIcon icon={faCog} color="#666" />
                    <Text style={styles.menuText}>الإعدادات</Text>
                    <FontAwesomeIcon color="#ccc" icon={faChevronRight} />
                </TouchableOpacity>

                {/* خط فاصل */}
                <View style={styles.menuDivider} />

                {/* تسجيل الخروج */}
                <TouchableOpacity style={styles.menuOption}>
                    <FontAwesomeIcon icon={faSignOutAlt} color="#e74c3c" />
                    <Text style={[styles.menuText, {color: '#e74c3c'}]}>
                        تسجيل الخروج
                    </Text>
                    <FontAwesomeIcon color="#ccc" icon={faChevronRight} />
                </TouchableOpacity>
            </View>
        </View>
    );
};

export default Menu;
