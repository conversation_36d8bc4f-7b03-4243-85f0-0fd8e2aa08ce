import React, {useState} from 'react';
import {View, Text, Image} from 'react-native';
import Dashboard from '../screens/Dashboard';
import Login from '../screens/Login';
import Signup from '../screens/Signup';
import {Product} from '../screens/Product';
import {Cart} from '../screens/Cart';
import {Wishlist} from '../screens/Wishlist';
import Search from '../screens/Search';
import Tutorial from '../screens/Onboarding';
import {Header} from '../components/Header';
import Menu from '../components/Menu';
import SideMenu from '@chakrahq/react-native-side-menu';
import {Category} from '../screens/Category';

import {FontAwesomeIcon} from '@fortawesome/react-native-fontawesome';
import {
    faHome,
    faUser,
    faHeart,
    faSearch,
    faShoppingCart,
} from '@fortawesome/free-solid-svg-icons';

import {
    faUser as faUserEmpty,
    faHeart as faHeartEmpty,
    faSearch as faSearchEmpty,
    faShoppingCart as faShoppingCartEmpty,
} from '@fortawesome/free-regular-svg-icons';

import {AuthStore} from '../store/auth';
import {observer} from 'mobx-react';

import {createDrawerNavigator} from '@react-navigation/drawer';
import {NavigationContainer} from '@react-navigation/native';
import {createStackNavigator} from '@react-navigation/stack';
import {createBottomTabNavigator} from '@react-navigation/bottom-tabs';
import {createNavigationContainerRef} from '@react-navigation/native';
import Profile from '../screens/Profile';
import Orders from '../screens/Orders';
import Order from '../screens/Order';

const Drawer = createDrawerNavigator();
const Stack = createStackNavigator();
const HomeStack = createStackNavigator();
const CartStack = createStackNavigator();
const SearchStack = createStackNavigator();
const ProfileStack = createStackNavigator();
const Tab = createBottomTabNavigator();

const navigationRef = createNavigationContainerRef();

const menu = <Menu navigationRef={navigationRef} />;

const HomeTabs = () => {
    return (
        <HomeStack.Navigator
            screenOptions={{
                headerShown: false,
            }}
            initialRouteName="Dashboard">
            <Stack.Screen name="Dashboard" component={Dashboard} />
            <Stack.Screen name="Product" component={Product} />
            <Stack.Screen name="Cart" component={Cart} />
            <Stack.Screen name="Category" component={Category} />
        </HomeStack.Navigator>
    );
};

const SearchTabs = () => {
    return (
        <SearchStack.Navigator
            screenOptions={{
                headerShown: false,
            }}
            initialRouteName="Search">
            <Stack.Screen name="Search" component={Search} />
            <Stack.Screen name="Product" component={Product} />
            <Stack.Screen name="Category" component={Category} />
        </SearchStack.Navigator>
    );
};

const CartTabs = () => {
    return (
        <CartStack.Navigator
            screenOptions={{
                headerShown: false,
            }}
            initialRouteName="Cart">
            <Stack.Screen name="Cart" component={Cart} />
            <Stack.Screen name="Wishlist" component={Wishlist} />
            <Stack.Screen name="Product" component={Product} />
            <Stack.Screen name="Category" component={Category} />
        </CartStack.Navigator>
    );
};

const ProfileTabs = () => {
    return (
        <ProfileStack.Navigator
            screenOptions={{
                headerShown: false,
            }}
            initialRouteName="Profile">
            <Stack.Screen name="Profile" component={Profile} />
            <Stack.Screen name="Order" component={Order} />
            <Stack.Screen name="Orders" component={Orders} />
            <Stack.Screen name="Wishlist" component={Wishlist} />
        </ProfileStack.Navigator>
    );
};

const Tabs = ({navigation}) => {
    return (
        <>
            <Tab.Navigator
                screenOptions={{
                    headerShown: false,
                }}>
                <Tab.Screen
                    name="Home"
                    component={HomeTabs}
                    options={{
                        tabBarLabel: ({focused, color, size}) => (
                            <Text
                                style={{
                                    color: focused ? '#9551E8' : '#000',
                                    fontFamily: 'Poppins-Regular',
                                    fontSize: 10,
                                }}>
                                الرئيسية
                            </Text>
                        ),
                        tabBarIcon: ({focused}) => (
                            <FontAwesomeIcon
                                icon={faHome}
                                color={focused ? '#9551E8' : '#666'}
                                size={20}
                            />
                        ),
                    }}
                />
                <Tab.Screen
                    name="Search"
                    component={SearchTabs}
                    options={{
                        tabBarLabel: ({focused, color, size}) => (
                            <Text
                                style={{
                                    color: focused ? '#9551E8' : '#000',
                                    fontFamily: 'Poppins-Regular',
                                    fontSize: 10,
                                }}>
                                البحث
                            </Text>
                        ),
                        tabBarIcon: ({focused}) => (
                            <FontAwesomeIcon
                                icon={focused ? faSearch : faSearchEmpty}
                                color={focused ? '#9551E8' : '#666'}
                                size={20}
                            />
                        ),
                    }}
                />
                <Tab.Screen
                    name="Cart"
                    component={CartTabs}
                    options={{
                        tabBarLabel: ({focused, color, size}) => (
                            <Text
                                style={{
                                    color: focused ? '#9551E8' : '#000',
                                    fontFamily: 'Poppins-Regular',
                                    fontSize: 10,
                                }}>
                                السلة
                            </Text>
                        ),
                        tabBarIcon: ({focused}) => (
                            <FontAwesomeIcon
                                icon={
                                    focused
                                        ? faShoppingCart
                                        : faShoppingCartEmpty
                                }
                                color={focused ? '#9551E8' : '#666'}
                                size={20}
                            />
                        ),
                    }}
                />
                <Tab.Screen
                    name="Profile"
                    component={ProfileTabs}
                    options={{
                        tabBarLabel: ({focused, color, size}) => (
                            <Text
                                style={{
                                    color: focused ? '#9551E8' : '#000',
                                    fontFamily: 'Poppins-Regular',
                                    fontSize: 10,
                                }}>
                                الحساب
                            </Text>
                        ),
                        tabBarIcon: ({focused}) => (
                            <FontAwesomeIcon
                                icon={focused ? faUser : faUserEmpty}
                                color={focused ? '#9551E8' : '#666'}
                                size={20}
                            />
                        ),
                    }}
                />
            </Tab.Navigator>
        </>
    );
};

export const Navigator = observer(() => {
    const [openMenu, setOpenMenu] = useState(false);

    const {
        state: {isAuthenticated},
    } = AuthStore;

    return (
        <>
            {isAuthenticated ? (
                <>
                    <NavigationContainer ref={navigationRef}>
                        <SideMenu menu={menu} isOpen={openMenu} autoClosing={true}>
                            <View style={{flex: 1}}>
                                <Header
                                    setOpenMenu={setOpenMenu}
                                    navigationRef={navigationRef}
                                />
                                <Stack.Navigator
                                    screenOptions={{
                                        headerShown: false,
                                    }}
                                    initialRouteName="Main">
                                    <Stack.Screen name="Main" component={Tabs} />
                                </Stack.Navigator>
                            </View>
                        </SideMenu>
                    </NavigationContainer>
                </>
            ) : (
                <>
                    <NavigationContainer ref={navigationRef}>
                        <Stack.Navigator
                            screenOptions={{
                                headerShown: false,
                            }}
                            initialRouteName="Onboarding">
                            <Stack.Screen
                                name="Onboarding"
                                component={Tutorial}
                            />
                            <Stack.Screen name="signup" component={Signup} />
                            <Stack.Screen name="login" component={Login} />
                        </Stack.Navigator>
                    </NavigationContainer>
                </>
            )}
        </>
    );
});
