import {StyleSheet} from 'react-native';
import {PrimaryColor, SecondaryColor, btnTextPrimary} from './theme';

const styles = StyleSheet.create({
    container: {
        alignItems: 'center',
        justifyContent: 'center',
        flex: 1,
        backgroundColor: '#fff',
    },

    // منع الزوم والتمرير الأفقي
    noZoom: {
        overflow: 'hidden',
        touchAction: 'pan-y', // السماح بالتمرير العمودي فقط
    },

    heading: {
        marginVertical: 10,
        marginHorizontal: 20,
        fontFamily: 'Poppins-SemiBold',
        fontSize: 24,
        color: '#21282F',
    },

    form: {
        width: '100%',
        backgroundColor: '#eee',
        padding: 20,
        flex: 1,
    },

    label: {
        fontSize: 16,
        marginVertical: 5,
    },

    inputView: {
        flexDirection: 'row',
        alignItems: 'center',
        borderColor: '#fff',
        borderWidth: 1,
        marginBottom: 10,
        backgroundColor: '#fff',
        borderRadius: 5,
        paddingLeft: 10,
    },

    inputViewFocused: {
        flexDirection: 'row',
        alignItems: 'center',
        borderColor: PrimaryColor,
        borderWidth: 1,
        marginBottom: 10,
        backgroundColor: '#fff',
        borderRadius: 5,
        paddingLeft: 10,
    },

    input: {
        borderRadius: 5,
        padding: 10,
        flex: 1,
    },

    inputFocused: {
        borderRadius: 5,
        padding: 10,
        flex: 1,
    },

    btnPrimary: {
        backgroundColor: SecondaryColor,
        marginVertical: 10,
        padding: 15,
        borderRadius: 5,
        alignItems: 'center',
    },

    btnTextPrimary: {
        color: btnTextPrimary,
        fontSize: 16,
    },

    header: {
        backgroundColor: '#fff',
        padding: 15,
        paddingTop: 50, // إضافة مساحة للـ status bar
        flexDirection: 'row',
        alignItems: 'center',
        borderBottomWidth: 1,
        borderBottomColor: '#f0f0f0',
        elevation: 2,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 1,
        },
        shadowOpacity: 0.1,
        shadowRadius: 2,
    },

    headerText: {
        color: '#9551E8',
        fontSize: 22,
        fontFamily: 'Poppins-Bold',
        marginTop: 5,
        marginLeft: 8,
    },

    product: {
        height: 150,
        width: '100%',
        borderRadius: 10,
        justifyContent: 'flex-end',
        marginBottom: 10,
    },

    categoryList: {
        marginLeft: 20,
        marginBottom: 10,
    },

    category: {
        paddingVertical: 5,
        marginHorizontal: 3,
        borderRadius: 5,
        backgroundColor: '#eee',
    },

    categoryText: {
        fontSize: 16,
        color: '#A2A0A1',
        fontFamily: 'Poppins-Regular',
        paddingHorizontal: 15,
    },

    categoryTextSelected: {
        fontSize: 16,
        color: '#fff',
        fontFamily: 'Poppins-SemiBold',
        backgroundColor: PrimaryColor,
        paddingHorizontal: 20,
        borderRadius: 5,
    },

    productSlider: {
        flexDirection: 'row',
    },

    productGrid: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        justifyContent: 'space-between',
        margin: 20,
        marginLeft: 10,
    },

    sliderItem: {
        width: 150,
        marginLeft: 10,
    },

    sliderImg: {
        width: 150,
        height: 150,
        resizeMode: 'cover',
    },

    sliderText: {
        fontFamily: 'Poppins-SemiBold',
    },

    sliderPrice: {
        fontFamily: 'Poppins-Regular',
    },

    productBtn: {
        position: 'absolute',
        bottom: 0,
        backgroundColor: '#21282F',
        padding: 20,
        width: 200,
        alignSelf: 'flex-end',
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        borderTopLeftRadius: 100,
    },

    productText: {
        color: '#fff',
        fontFamily: 'Poppins-Regular',
        fontSize: 16,
    },

    searchBar: {
        flexDirection: 'row',
        paddingHorizontal: 20,
        paddingVertical: 10,
        backgroundColor: '#fff',
        borderBottomWidth: 1,
        borderBottomColor: '#f0f0f0',
    },

    searchText: {
        fontFamily: 'Poppins-Regular',
        margin: 20,
        marginBottom: 0,
        fontSize: 16,
    },

    searchInput: {
        borderWidth: 1,
        borderColor: '#ccc',
        flex: 1,
        backgroundColor: '#eee',
        borderRadius: 30,
        paddingHorizontal: 20,
    },

    searchInputFocused: {
        borderWidth: 1,
        borderColor: '#ccc',
        flex: 1,
        backgroundColor: '#fff',
        borderRadius: 30,
        paddingHorizontal: 20,
    },

    searchBtn: {
        position: 'absolute',
        right: 0,
        paddingVertical: 15,
        paddingHorizontal: 25,
    },

    // Search Screen Styles
    searchHeader: {
        backgroundColor: '#fff',
        paddingHorizontal: 15,
        paddingVertical: 10,
        borderBottomWidth: 1,
        borderBottomColor: '#eee',
    },

    searchBarContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: '#f5f5f5',
        borderRadius: 25,
        paddingHorizontal: 15,
    },

    searchInputFull: {
        flex: 1,
        paddingVertical: 12,
        fontSize: 16,
        fontFamily: 'Poppins-Regular',
        color: '#000',
        textAlign: 'right',
    },

    clearSearchBtn: {
        padding: 5,
        marginRight: 10,
    },

    searchBtnFull: {
        backgroundColor: PrimaryColor,
        paddingHorizontal: 15,
        paddingVertical: 8,
        borderRadius: 20,
        marginLeft: 10,
    },

    searchResultsTitle: {
        fontSize: 18,
        fontFamily: 'Poppins-SemiBold',
        color: '#21282F',
        marginBottom: 10,
    },

    sectionTitle: {
        fontSize: 18,
        fontFamily: 'Poppins-SemiBold',
        color: '#21282F',
        marginHorizontal: 15,
        marginBottom: 15,
    },

    recentSearchItem: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingVertical: 12,
        borderBottomWidth: 1,
        borderBottomColor: '#f0f0f0',
    },

    recentSearchText: {
        fontSize: 16,
        fontFamily: 'Poppins-Regular',
        color: '#333',
    },

    popularSearchContainer: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        paddingHorizontal: 15,
    },

    popularSearchTag: {
        backgroundColor: '#f0f0f0',
        paddingHorizontal: 15,
        paddingVertical: 8,
        borderRadius: 20,
        marginRight: 10,
        marginBottom: 10,
    },

    popularSearchText: {
        fontSize: 14,
        fontFamily: 'Poppins-Regular',
        color: '#666',
    },

    primaryBtn: {
        backgroundColor: PrimaryColor,
        padding: 15,
        width: 200,
        alignItems: 'center',
        alignSelf: 'center',
        borderRadius: 50,
        marginBottom: 20,
    },

    secondaryBtn: {
        borderColor: '#000',
        borderWidth: 2,
        padding: 10,
        width: 200,
        alignItems: 'center',
        alignSelf: 'center',
        marginVertical: 20,
    },

    cartItem: {
        borderColor: '#ccc',
        borderBottomWidth: 1,
        margin: 10,
        padding: 5,
        flexDirection: 'row',
        marginBottom: 0,
    },

    cartName: {
        marginLeft: 5,
        fontSize: 16,
        color: '#000',
        fontFamily: 'Poppins-SemiBold',
    },

    cartPrice: {
        marginLeft: 5,
        fontSize: 14,
        color: '#000',
        fontFamily: 'Poppins-Regular',
    },

    wishlistItem: {
        borderColor: '#ccc',
        borderBottomWidth: 1,
        margin: 10,
        padding: 5,
        flexDirection: 'row',
        marginBottom: 0,
    },

    wishlistName: {
        marginLeft: 5,
        fontSize: 16,
        color: '#000',
        fontFamily: 'Poppins-SemiBold',
        width: '80%',
    },

    wishlistPrice: {
        marginLeft: 5,
        fontSize: 14,
        color: '#000',
        fontFamily: 'Poppins-Regular',
    },

    cartQuantity: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: '#fff',
        paddingVertical: 5,
        paddingHorizontal: 10,
        borderRadius: 30,
        borderColor: '#eee',
        borderWidth: 1,
        alignSelf: 'flex-end',
    },

    menuTop: {
        height: 100,
        justifyContent: 'flex-end',
        alignItems: 'flex-start',
        padding: 10,
    },

    menuOptions: {
        height: '100%',
        backgroundColor: '#fff',
        paddingVertical: 20,
    },

    menuOption: {
        flexDirection: 'row',
        paddingVertical: 20,
        marginHorizontal: 20,
        borderBottomColor: '#eee',
        borderBottomWidth: 1,
        alignItems: 'center',
    },

    menuText: {
        fontFamily: 'Poppins-SemiBold',
        color: '#000',
        marginLeft: 10,
        flex: 1,
        textAlign: 'right',
    },

    menuDivider: {
        height: 1,
        backgroundColor: '#eee',
        marginVertical: 10,
        marginHorizontal: 20,
    },

    profile: {
        backgroundColor: '#fff',
        alignItems: 'center',
    },

    profileName: {
        fontFamily: 'Poppins-Regular',
        fontSize: 20,
        color: '#000',
    },

    switch: {
        flexDirection: 'row',
    },

    switchBtn: {
        flex: 1,
        alignItems: 'center',
        paddingVertical: 20,
        borderBottomColor: '#ccc',
        borderBottomWidth: 2,
    },

    switchBtnSelected: {
        flex: 1,
        alignItems: 'center',
        paddingVertical: 20,
        borderBottomColor: '#000',
        borderBottomWidth: 2,
    },

    switchText: {
        fontFamily: 'Poppins-Regular',
    },

    switchTextSelected: {
        fontFamily: 'Poppins-SemiBold',
        color: '#000',
    },

    orderItem: {
        padding: 10,
        backgroundColor: '#fff',
        marginBottom: 10,
        flexDirection: 'row',
        justifyContent: 'space-between',
    },

    orderNo: {
        fontFamily: 'Poppins-SemiBold',
        color: '#000',
        fontSize: 16,
    },

    orderItemCount: {
        fontFamily: 'Poppins-Regular',
    },

    orderDate: {
        fontFamily: 'Poppins-Regular',
        fontStyle: 'italic',
    },
});

export default styles;
