// نظام ثيمات متقدم مع دعم Dark Mode - المرحلة الثالثة
import { Appearance } from 'react-native';

// الألوان الأساسية المشتركة
const baseColors = {
  // الألوان الأساسية
  primary: {
    50: '#f0f4ff',
    100: '#e0e7ff',
    200: '#c7d2fe',
    300: '#a5b4fc',
    400: '#818cf8',
    500: '#667eea',
    600: '#5a6fd8',
    700: '#4f46e5',
    800: '#4338ca',
    900: '#312e81'
  },

  // الألوان الثانوية
  secondary: {
    50: '#f8fafc',
    100: '#f1f5f9',
    200: '#e2e8f0',
    300: '#cbd5e1',
    400: '#94a3b8',
    500: '#64748b',
    600: '#475569',
    700: '#334155',
    800: '#1e293b',
    900: '#0f172a'
  },

  // ألوان الحالة
  success: {
    50: '#f0fdf4',
    100: '#dcfce7',
    200: '#bbf7d0',
    300: '#86efac',
    400: '#4ade80',
    500: '#10b981',
    600: '#059669',
    700: '#047857',
    800: '#065f46',
    900: '#064e3b'
  },

  warning: {
    50: '#fffbeb',
    100: '#fef3c7',
    200: '#fde68a',
    300: '#fcd34d',
    400: '#fbbf24',
    500: '#f59e0b',
    600: '#d97706',
    700: '#b45309',
    800: '#92400e',
    900: '#78350f'
  },

  error: {
    50: '#fef2f2',
    100: '#fee2e2',
    200: '#fecaca',
    300: '#fca5a5',
    400: '#f87171',
    500: '#ef4444',
    600: '#dc2626',
    700: '#b91c1c',
    800: '#991b1b',
    900: '#7f1d1d'
  },

  info: {
    50: '#eff6ff',
    100: '#dbeafe',
    200: '#bfdbfe',
    300: '#93c5fd',
    400: '#60a5fa',
    500: '#3b82f6',
    600: '#2563eb',
    700: '#1d4ed8',
    800: '#1e40af',
    900: '#1e3a8a'
  },

  // ألوان محايدة
  gray: {
    50: '#f9fafb',
    100: '#f3f4f6',
    200: '#e5e7eb',
    300: '#d1d5db',
    400: '#9ca3af',
    500: '#6b7280',
    600: '#4b5563',
    700: '#374151',
    800: '#1f2937',
    900: '#111827'
  },

  // ألوان خاصة
  white: '#ffffff',
  black: '#000000',
  transparent: 'transparent'
};

// ثيم الوضع النهاري (Light Mode)
const lightTheme = {
  colors: {
    ...baseColors,

    // ألوان السطح والخلفية
    background: '#ffffff',
    surface: '#ffffff',
    surfaceVariant: '#f9fafb',

    // ألوان النص
    text: '#111827',
    textSecondary: '#6b7280',
    textTertiary: '#9ca3af',
    textInverse: '#ffffff',

    // ألوان الحدود والفواصل
    border: '#e5e7eb',
    borderLight: '#f3f4f6',
    divider: '#e5e7eb',

    // ألوان التفاعل
    overlay: 'rgba(0, 0, 0, 0.5)',
    backdrop: 'rgba(0, 0, 0, 0.25)',

    // ألوان الحالة للخلفيات
    backgroundSuccess: '#f0fdf4',
    backgroundWarning: '#fffbeb',
    backgroundError: '#fef2f2',
    backgroundInfo: '#eff6ff'
  },

  isDark: false
};

// ثيم الوضع الليلي (Dark Mode)
const darkTheme = {
  colors: {
    ...baseColors,

    // ألوان السطح والخلفية
    background: '#0f172a',
    surface: '#1e293b',
    surfaceVariant: '#334155',

    // ألوان النص
    text: '#f8fafc',
    textSecondary: '#cbd5e1',
    textTertiary: '#94a3b8',
    textInverse: '#111827',

    // ألوان الحدود والفواصل
    border: '#475569',
    borderLight: '#334155',
    divider: '#475569',

    // ألوان التفاعل
    overlay: 'rgba(0, 0, 0, 0.7)',
    backdrop: 'rgba(0, 0, 0, 0.5)',

    // ألوان الحالة للخلفيات
    backgroundSuccess: '#064e3b',
    backgroundWarning: '#78350f',
    backgroundError: '#7f1d1d',
    backgroundInfo: '#1e3a8a'
  },

  isDark: true
};

// الخصائص المشتركة بين جميع الثيمات
const commonThemeProperties = {
  // نظام المسافات
  spacing: {
    xs: 4,
    sm: 8,
    md: 16,
    lg: 24,
    xl: 32,
    '2xl': 40,
    '3xl': 48,
    '4xl': 64
  },

  // نظام الخطوط
  typography: {
    fontFamily: {
      primary: 'Cairo-Regular',
      primaryBold: 'Cairo-Bold',
      primarySemiBold: 'Cairo-SemiBold',
      secondary: 'Inter-Regular'
    },
    fontSize: {
      xs: 12,
      sm: 14,
      md: 16,
      lg: 18,
      xl: 20,
      '2xl': 24,
      '3xl': 30,
      '4xl': 36
    },
    lineHeight: {
      xs: 16,
      sm: 20,
      md: 24,
      lg: 28,
      xl: 32,
      '2xl': 36,
      '3xl': 42,
      '4xl': 48
    },
    fontWeight: {
      normal: '400',
      medium: '500',
      semibold: '600',
      bold: '700'
    }
  },

  // نظام الحدود والزوايا
  borderRadius: {
    none: 0,
    sm: 4,
    md: 8,
    lg: 12,
    xl: 16,
    '2xl': 20,
    full: 9999
  },

  // نظام الظلال
  shadows: {
    sm: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.05,
      shadowRadius: 2,
      elevation: 1
    },
    md: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 3
    },
    lg: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.15,
      shadowRadius: 8,
      elevation: 6
    },
    xl: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 8 },
      shadowOpacity: 0.2,
      shadowRadius: 16,
      elevation: 10
    }
  }
};

// دمج الخصائص المشتركة مع كل ثيم
const createTheme = (themeColors) => ({
  ...commonThemeProperties,
  ...themeColors
});

// تصدير الثيمات
export const themes = {
  light: createTheme(lightTheme),
  dark: createTheme(darkTheme)
};

// الثيم الافتراضي (للتوافق مع الكود الحالي)
export const theme = themes.light;

// متغير لتتبع الثيم الحالي
let currentTheme = 'light';

// دالة للحصول على الثيم الحالي
export const getCurrentTheme = () => themes[currentTheme];

// دالة لتغيير الثيم
export const setTheme = (themeName) => {
  if (themes[themeName]) {
    currentTheme = themeName;
    return themes[themeName];
  }
  return themes.light;
};

// دالة للحصول على الثيم بناءً على تفضيلات النظام
export const getSystemTheme = () => {
  const colorScheme = Appearance.getColorScheme();
  return colorScheme === 'dark' ? 'dark' : 'light';
};

// دالة لتبديل الثيم
export const toggleTheme = () => {
  const newTheme = currentTheme === 'light' ? 'dark' : 'light';
  return setTheme(newTheme);
};

// للتوافق مع الكود القديم - سيتم إزالتها تدريجياً
export const PrimaryColor = theme.colors.primary[600];
export const SecondaryColor = theme.colors.secondary[900];
export const TextColorPrimary = theme.colors.white;
export const TextColorSecondary = theme.colors.secondary[900];
export const btnTextPrimary = theme.colors.white;
export const LabelColor = theme.colors.gray[500];
export const InputColor = theme.colors.gray[600];
