import React, { createContext, useContext, useState, useEffect } from 'react';
import { Appearance, StatusBar } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { themes, getSystemTheme } from '../styles/theme';

/**
 * Theme Context للتحكم في الثيمات على مستوى التطبيق
 * يدعم Light Mode و Dark Mode مع حفظ التفضيلات
 */

const ThemeContext = createContext();

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

export const ThemeProvider = ({ children }) => {
  const [currentTheme, setCurrentTheme] = useState('light');
  const [isSystemTheme, setIsSystemTheme] = useState(true);
  const [isLoading, setIsLoading] = useState(true);

  // تحميل التفضيلات المحفوظة عند بدء التطبيق
  useEffect(() => {
    loadThemePreferences();
  }, []);

  // مراقبة تغييرات النظام
  useEffect(() => {
    const subscription = Appearance.addChangeListener(({ colorScheme }) => {
      if (isSystemTheme) {
        const systemTheme = colorScheme === 'dark' ? 'dark' : 'light';
        setCurrentTheme(systemTheme);
        updateStatusBar(systemTheme);
      }
    });

    return () => subscription?.remove();
  }, [isSystemTheme]);

  // تحديث شريط الحالة عند تغيير الثيم
  useEffect(() => {
    updateStatusBar(currentTheme);
  }, [currentTheme]);

  const loadThemePreferences = async () => {
    try {
      const savedTheme = await AsyncStorage.getItem('app_theme');
      const savedIsSystemTheme = await AsyncStorage.getItem('is_system_theme');

      if (savedIsSystemTheme === 'false') {
        // المستخدم اختار ثيم محدد
        setIsSystemTheme(false);
        setCurrentTheme(savedTheme || 'light');
      } else {
        // استخدام ثيم النظام
        setIsSystemTheme(true);
        const systemTheme = getSystemTheme();
        setCurrentTheme(systemTheme);
      }
    } catch (error) {
      console.warn('Error loading theme preferences:', error);
      // استخدام الثيم الافتراضي
      const systemTheme = getSystemTheme();
      setCurrentTheme(systemTheme);
    } finally {
      setIsLoading(false);
    }
  };

  const updateStatusBar = (themeName) => {
    const isDark = themeName === 'dark';
    StatusBar.setBarStyle(isDark ? 'light-content' : 'dark-content', true);
    if (Platform.OS === 'android') {
      StatusBar.setBackgroundColor(
        isDark ? themes.dark.colors.surface : themes.light.colors.background,
        true
      );
    }
  };

  const setTheme = async (themeName) => {
    try {
      setCurrentTheme(themeName);
      setIsSystemTheme(false);

      // حفظ التفضيلات
      await AsyncStorage.setItem('app_theme', themeName);
      await AsyncStorage.setItem('is_system_theme', 'false');
    } catch (error) {
      console.warn('Error saving theme preference:', error);
    }
  };

  const useSystemTheme = async () => {
    try {
      setIsSystemTheme(true);
      const systemTheme = getSystemTheme();
      setCurrentTheme(systemTheme);

      // حفظ التفضيلات
      await AsyncStorage.setItem('is_system_theme', 'true');
    } catch (error) {
      console.warn('Error saving system theme preference:', error);
    }
  };

  const toggleTheme = async () => {
    const newTheme = currentTheme === 'light' ? 'dark' : 'light';
    await setTheme(newTheme);
  };

  const value = {
    // الثيم الحالي
    theme: themes[currentTheme],
    currentThemeName: currentTheme,
    isDark: currentTheme === 'dark',
    isSystemTheme,
    isLoading,

    // دوال التحكم
    setTheme,
    toggleTheme,
    useSystemTheme,

    // جميع الثيمات المتاحة
    availableThemes: Object.keys(themes)
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};

export default ThemeContext;