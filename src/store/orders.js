// 📦 Orders Store - إدارة الطلبات
import {makeObservable, observable, action, runInAction} from 'mobx';
import {ToastAndroid} from 'react-native';
import {ecommerceService} from '../services/apiService';

class Orders {
    state = {
        orders: [],
        currentOrder: null,
        loading: false,
        error: null,
        pagination: {
            current_page: 1,
            total: 0,
            per_page: 10,
            last_page: 1,
        },
    };

    constructor() {
        makeObservable(this, {
            state: observable,
            getOrders: action,
            getOrderDetails: action,
            createOrder: action,
            cancelOrder: action,
            confirmDelivery: action,
            uploadProof: action,
            downloadInvoice: action,
            setLoading: action,
            setError: action,
            clearError: action,
        });
    }

    createToast = message => {
        ToastAndroid.showWithGravityAndOffset(
            message,
            ToastAndroid.LONG,
            ToastAndroid.BOTTOM,
            0,
            50,
        );
    };

    setLoading = loading => {
        this.state.loading = loading;
    };

    setError = error => {
        this.state.error = error;
    };

    clearError = () => {
        this.state.error = null;
    };

    getOrders = async (params = {}) => {
        try {
            this.setLoading(true);
            this.clearError();

            // Note: This requires authentication
            const response = await ecommerceService.getOrders(params);

            if (response.error === false && response.data) {
                runInAction(() => {
                    this.state.orders = response.data.data || response.data;
                    if (response.data.meta) {
                        this.state.pagination = response.data.meta;
                    }
                });
                return {success: true, data: response.data};
            }
        } catch (error) {
            console.error('Get orders error:', error);
            const errorMessage =
                error.response?.data?.message ||
                error.message ||
                'فشل في جلب الطلبات';
            this.setError(errorMessage);

            // If authentication error, show appropriate message
            if (error.response?.status === 401) {
                this.createToast('يجب تسجيل الدخول لعرض الطلبات');
            }

            return {success: false, error: errorMessage};
        } finally {
            this.setLoading(false);
        }
    };

    getOrderDetails = async orderId => {
        try {
            this.setLoading(true);
            this.clearError();

            const response = await ecommerceService.getOrderDetails(orderId);

            if (response.error === false && response.data) {
                runInAction(() => {
                    this.state.currentOrder = response.data;
                });
                return {success: true, data: response.data};
            }
        } catch (error) {
            console.error('Get order details error:', error);
            const errorMessage =
                error.response?.data?.message ||
                error.message ||
                'فشل في جلب تفاصيل الطلب';
            this.setError(errorMessage);
            return {success: false, error: errorMessage};
        } finally {
            this.setLoading(false);
        }
    };

    createOrder = async orderData => {
        try {
            this.setLoading(true);
            this.clearError();

            const response = await ecommerceService.createOrder(orderData);

            if (response.error === false && response.data) {
                runInAction(() => {
                    this.state.currentOrder = response.data;
                    // Add to orders list if it exists
                    if (this.state.orders.length > 0) {
                        this.state.orders.unshift(response.data);
                    }
                });

                this.createToast('تم إنشاء الطلب بنجاح');
                return {success: true, data: response.data};
            }
        } catch (error) {
            console.error('Create order error:', error);
            const errorMessage =
                error.response?.data?.message ||
                error.message ||
                'فشل في إنشاء الطلب';
            this.setError(errorMessage);
            this.createToast(errorMessage);
            return {success: false, error: errorMessage};
        } finally {
            this.setLoading(false);
        }
    };

    cancelOrder = async orderId => {
        try {
            this.setLoading(true);
            this.clearError();

            const response = await ecommerceService.cancelOrder(orderId);

            if (response.error === false) {
                // Update order status in local state
                runInAction(() => {
                    if (
                        this.state.currentOrder &&
                        this.state.currentOrder.id === orderId
                    ) {
                        this.state.currentOrder.status = 'cancelled';
                    }

                    this.state.orders = this.state.orders.map(order =>
                        order.id === orderId
                            ? {...order, status: 'cancelled'}
                            : order,
                    );
                });

                this.createToast('تم إلغاء الطلب');
                return {success: true, data: response.data};
            }
        } catch (error) {
            console.error('Cancel order error:', error);
            const errorMessage =
                error.response?.data?.message ||
                error.message ||
                'فشل في إلغاء الطلب';
            this.setError(errorMessage);
            this.createToast(errorMessage);
            return {success: false, error: errorMessage};
        } finally {
            this.setLoading(false);
        }
    };

    confirmDelivery = async orderId => {
        try {
            this.setLoading(true);
            this.clearError();

            const response = await ecommerceService.confirmDelivery(orderId);

            if (response.error === false) {
                // Update order status in local state
                runInAction(() => {
                    if (
                        this.state.currentOrder &&
                        this.state.currentOrder.id === orderId
                    ) {
                        this.state.currentOrder.status = 'delivered';
                    }

                    this.state.orders = this.state.orders.map(order =>
                        order.id === orderId
                            ? {...order, status: 'delivered'}
                            : order,
                    );
                });

                this.createToast('تم تأكيد استلام الطلب');
                return {success: true, data: response.data};
            }
        } catch (error) {
            console.error('Confirm delivery error:', error);
            const errorMessage =
                error.response?.data?.message ||
                error.message ||
                'فشل في تأكيد الاستلام';
            this.setError(errorMessage);
            this.createToast(errorMessage);
            return {success: false, error: errorMessage};
        } finally {
            this.setLoading(false);
        }
    };

    uploadProof = async (orderId, proofFile) => {
        try {
            this.setLoading(true);
            this.clearError();

            const formData = new FormData();
            formData.append('proof', proofFile);

            const response = await ecommerceService.uploadProof(
                orderId,
                formData,
            );

            if (response.error === false) {
                this.createToast('تم رفع إثبات الدفع');
                return {success: true, data: response.data};
            }
        } catch (error) {
            console.error('Upload proof error:', error);
            const errorMessage =
                error.response?.data?.message ||
                error.message ||
                'فشل في رفع إثبات الدفع';
            this.setError(errorMessage);
            this.createToast(errorMessage);
            return {success: false, error: errorMessage};
        } finally {
            this.setLoading(false);
        }
    };

    downloadInvoice = async orderId => {
        try {
            this.setLoading(true);
            this.clearError();

            const response = await ecommerceService.downloadInvoice(orderId);

            if (response.error === false) {
                this.createToast('تم تحميل الفاتورة');
                return {success: true, data: response.data};
            }
        } catch (error) {
            console.error('Download invoice error:', error);
            const errorMessage =
                error.response?.data?.message ||
                error.message ||
                'فشل في تحميل الفاتورة';
            this.setError(errorMessage);
            this.createToast(errorMessage);
            return {success: false, error: errorMessage};
        } finally {
            this.setLoading(false);
        }
    };

    getOrdersByStatus = status => {
        return this.state.orders.filter(order => order.status === status);
    };

    getPendingOrders = () => {
        return this.getOrdersByStatus('pending');
    };

    getCompletedOrders = () => {
        return this.getOrdersByStatus('completed');
    };

    getCancelledOrders = () => {
        return this.getOrdersByStatus('cancelled');
    };

    getOrdersCount = () => {
        return this.state.orders.length;
    };

    getTotalSpent = () => {
        return this.state.orders
            .filter(order => order.status === 'completed')
            .reduce((total, order) => total + (order.total || 0), 0);
    };

    // Helper method to format order status in Arabic
    getOrderStatusText = status => {
        const statusMap = {
            pending: 'قيد الانتظار',
            processing: 'قيد المعالجة',
            shipped: 'تم الشحن',
            delivered: 'تم التسليم',
            cancelled: 'ملغي',
            refunded: 'مسترد',
            completed: 'مكتمل',
        };

        return statusMap[status] || status;
    };

    // Helper method to get order status color
    getOrderStatusColor = status => {
        const colorMap = {
            pending: '#FFA500',
            processing: '#2196F3',
            shipped: '#9C27B0',
            delivered: '#4CAF50',
            cancelled: '#F44336',
            refunded: '#FF9800',
            completed: '#4CAF50',
        };

        return colorMap[status] || '#757575';
    };
}

export const OrdersStore = new Orders();
