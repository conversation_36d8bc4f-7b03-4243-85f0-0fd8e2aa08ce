import axios from 'axios';
import {makeObservable, observable, action, runInAction} from 'mobx';
import {BASE_URL} from './url';
import {authService} from '../services/apiService';

import {ToastAndroid} from 'react-native';

class Auth {
    state = {
        isAuthenticated: false,
        user: null,
        token: null,
        loading: false,
        error: null,
    };

    constructor() {
        makeObservable(this, {
            state: observable,
            login: action,
            register: action,
            logout: action,
            checkEmail: action,
            sendOtp: action,
            verifyOtp: action,
            forgotPassword: action,
            setLoading: action,
            setError: action,
            clearError: action,
        });
    }

    createToast = message => {
        ToastAndroid.showWithGravityAndOffset(
            message,
            ToastAndroid.LONG,
            ToastAndroid.BOTTOM,
            0,
            50,
        );
    };

    setLoading = loading => {
        this.state.loading = loading;
    };

    setError = error => {
        this.state.error = error;
    };

    clearError = () => {
        this.state.error = null;
    };

    login = async credentials => {
        try {
            this.setLoading(true);
            this.clearError();

            const response = await authService.login(credentials);

            if (response.error === false && response.data) {
                runInAction(() => {
                    this.state.isAuthenticated = true;
                    this.state.user = response.data.user;
                    this.state.token = response.data.token;
                });

                this.createToast('تم تسجيل الدخول بنجاح');
                return {success: true, data: response.data};
            } else {
                throw new Error(response.message || 'فشل في تسجيل الدخول');
            }
        } catch (error) {
            console.error('Login error:', error);
            const errorMessage =
                error.response?.data?.message ||
                error.message ||
                'فشل في تسجيل الدخول';

            runInAction(() => {
                this.state.isAuthenticated = false;
                this.state.user = null;
                this.state.token = null;
            });

            this.setError(errorMessage);
            this.createToast(errorMessage);
            return {success: false, error: errorMessage};
        } finally {
            this.setLoading(false);
        }
    };

    register = async userData => {
        try {
            this.setLoading(true);
            this.clearError();

            const response = await authService.register(userData);

            if (response.error === false && response.data) {
                runInAction(() => {
                    this.state.isAuthenticated = true;
                    this.state.user = response.data.user;
                    this.state.token = response.data.token;
                });

                this.createToast('تم إنشاء الحساب بنجاح');
                return {success: true, data: response.data};
            } else {
                throw new Error(response.message || 'فشل في إنشاء الحساب');
            }
        } catch (error) {
            console.error('Register error:', error);
            const errorMessage =
                error.response?.data?.message ||
                error.message ||
                'فشل في إنشاء الحساب';

            runInAction(() => {
                this.state.isAuthenticated = false;
                this.state.user = null;
                this.state.token = null;
            });

            this.setError(errorMessage);
            this.createToast(errorMessage);
            return {success: false, error: errorMessage};
        } finally {
            this.setLoading(false);
        }
    };

    logout = async () => {
        try {
            this.setLoading(true);

            if (this.state.token) {
                await authService.logout();
            }

            runInAction(() => {
                this.state.isAuthenticated = false;
                this.state.user = null;
                this.state.token = null;
            });

            this.createToast('تم تسجيل الخروج');
            return {success: true};
        } catch (error) {
            console.error('Logout error:', error);
            // حتى لو فشل الطلب، نقوم بتسجيل الخروج محلياً
            runInAction(() => {
                this.state.isAuthenticated = false;
                this.state.user = null;
                this.state.token = null;
            });

            this.createToast('تم تسجيل الخروج');
            return {success: true};
        } finally {
            this.setLoading(false);
        }
    };

    checkEmail = async email => {
        try {
            this.setLoading(true);
            this.clearError();

            const response = await authService.checkEmail(email);
            return {success: true, data: response};
        } catch (error) {
            console.error('Check email error:', error);
            const errorMessage =
                error.response?.data?.message ||
                error.message ||
                'فشل في فحص البريد الإلكتروني';
            this.setError(errorMessage);
            return {success: false, error: errorMessage};
        } finally {
            this.setLoading(false);
        }
    };

    sendOtp = async (phone, type = 'login') => {
        try {
            this.setLoading(true);
            this.clearError();

            const response = await authService.sendOtp(phone, type);

            if (response.success) {
                this.createToast('تم إرسال رمز التحقق');
                return {success: true, data: response};
            } else {
                throw new Error(response.message || 'فشل في إرسال رمز التحقق');
            }
        } catch (error) {
            console.error('Send OTP error:', error);
            const errorMessage =
                error.response?.data?.message ||
                error.message ||
                'فشل في إرسال رمز التحقق';
            this.setError(errorMessage);
            this.createToast(errorMessage);
            return {success: false, error: errorMessage};
        } finally {
            this.setLoading(false);
        }
    };

    verifyOtp = async (phone, code, type = 'login') => {
        try {
            this.setLoading(true);
            this.clearError();

            const response = await authService.verifyOtp(phone, code, type);

            if (response.success) {
                if (response.data?.user && response.data?.token) {
                    runInAction(() => {
                        this.state.isAuthenticated = true;
                        this.state.user = response.data.user;
                        this.state.token = response.data.token;
                    });
                }

                this.createToast('تم التحقق بنجاح');
                return {success: true, data: response.data};
            } else {
                throw new Error(response.message || 'رمز التحقق غير صحيح');
            }
        } catch (error) {
            console.error('Verify OTP error:', error);
            const errorMessage =
                error.response?.data?.message ||
                error.message ||
                'رمز التحقق غير صحيح';
            this.setError(errorMessage);
            this.createToast(errorMessage);
            return {success: false, error: errorMessage};
        } finally {
            this.setLoading(false);
        }
    };

    forgotPassword = async email => {
        try {
            this.setLoading(true);
            this.clearError();

            const response = await authService.forgotPassword(email);

            if (response.success || response.error === false) {
                this.createToast('تم إرسال رابط إعادة تعيين كلمة المرور');
                return {success: true, data: response};
            } else {
                throw new Error(
                    response.message || 'فشل في إرسال رابط إعادة التعيين',
                );
            }
        } catch (error) {
            console.error('Forgot password error:', error);
            const errorMessage =
                error.response?.data?.message ||
                error.message ||
                'فشل في إرسال رابط إعادة التعيين';
            this.setError(errorMessage);
            this.createToast(errorMessage);
            return {success: false, error: errorMessage};
        } finally {
            this.setLoading(false);
        }
    };
}

export const AuthStore = new Auth();
