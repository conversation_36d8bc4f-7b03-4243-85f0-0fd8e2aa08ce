import axios from 'axios';

import {ToastAndroid} from 'react-native';

import {makeObservable, observable, action, runInAction} from 'mobx';
import {BASE_URL} from './url';
import {ecommerceService, vehiclePartsService} from '../services/apiService';

import {categories} from '../data/categories';
import {products} from '../data/products';
import {cart} from '../data/cart';
import {wishlist} from '../data/wishlist';

class Product {
    state = {
        allProducts: products,
        searchedProducts: [],
        products: [],
        product: {},
        categories: [],
        brands: [],
        category: null,
        cart: cart,
        wishlist: wishlist,
        loading: false,
        error: null,
        // Vehicle Parts
        vehicleCategories: [],
        makes: [],
        models: [],
        selectedMake: null,
        selectedModel: null,
    };

    constructor() {
        makeObservable(this, {
            state: observable,
            getCategories: action,
            getProducts: action,
            getProductDetails: action,
            getProductsByCategories: action,
            getSearchedProducts: action,
            getRandomProducts: action,
            getBrands: action,
            addToCart: action,
            addToWishlist: action,
            removeFromWishlist: action,
            setCategory: action,
            setProduct: action,
            setLoading: action,
            setError: action,
            clearError: action,
            // Vehicle Parts actions
            getVehicleCategories: action,
            getVehicleMakes: action,
            getVehicleModels: action,
            searchVehicleParts: action,
        });
    }

    createToast = message => {
        ToastAndroid.showWithGravityAndOffset(
            message,
            ToastAndroid.LONG,
            ToastAndroid.BOTTOM,
            0,
            50,
        );
    };

    setLoading = loading => {
        this.state.loading = loading;
    };

    setError = error => {
        this.state.error = error;
    };

    clearError = () => {
        this.state.error = null;
    };

    shuffle = a => {
        var j, x, i;
        for (i = a.length - 1; i > 0; i--) {
            j = Math.floor(Math.random() * (i + 1));
            x = a[i];
            a[i] = a[j];
            a[j] = x;
        }
        return a;
    };

    getCategories = async (useApi = true) => {
        if (!useApi) {
            this.state.categories = categories;
            return;
        }

        try {
            this.setLoading(true);
            this.clearError();

            const response = await ecommerceService.getCategories();

            if (response.error === false && response.data) {
                runInAction(() => {
                    this.state.categories = response.data.data || response.data;
                });
                return {success: true, data: response.data};
            } else {
                // Fallback to mock data
                this.state.categories = categories;
                return {success: true, data: categories};
            }
        } catch (error) {
            console.error('Get categories error:', error);
            // Fallback to mock data
            runInAction(() => {
                this.state.categories = categories;
            });

            const errorMessage =
                error.response?.data?.message ||
                error.message ||
                'فشل في جلب الفئات';
            this.setError(errorMessage);
            return {success: false, error: errorMessage};
        } finally {
            this.setLoading(false);
        }
    };

    getBrands = async () => {
        try {
            this.setLoading(true);
            this.clearError();

            const response = await ecommerceService.getBrands();

            if (response.error === false && response.data) {
                runInAction(() => {
                    this.state.brands = response.data.data || response.data;
                });
                return {success: true, data: response.data};
            }
        } catch (error) {
            console.error('Get brands error:', error);
            const errorMessage =
                error.response?.data?.message ||
                error.message ||
                'فشل في جلب العلامات التجارية';
            this.setError(errorMessage);
            return {success: false, error: errorMessage};
        } finally {
            this.setLoading(false);
        }
    };

    getProducts = async (params = {}, useApi = true) => {
        if (!useApi) {
            this.state.products = this.shuffle(products);
            this.state.allProducts = this.shuffle(products);
            return;
        }

        try {
            this.setLoading(true);
            this.clearError();

            const response = await ecommerceService.getProducts(params);

            if (response.error === false && response.data) {
                const productsData = response.data.data || response.data;

                runInAction(() => {
                    this.state.products = productsData;
                    this.state.allProducts = productsData;
                });

                return {success: true, data: response.data};
            } else {
                // Fallback to mock data
                runInAction(() => {
                    this.state.products = this.shuffle(products);
                    this.state.allProducts = this.shuffle(products);
                });
                return {success: true, data: products};
            }
        } catch (error) {
            console.error('Get products error:', error);
            // Fallback to mock data
            runInAction(() => {
                this.state.products = this.shuffle(products);
                this.state.allProducts = this.shuffle(products);
            });

            const errorMessage =
                error.response?.data?.message ||
                error.message ||
                'فشل في جلب المنتجات';
            this.setError(errorMessage);
            return {success: false, error: errorMessage};
        } finally {
            this.setLoading(false);
        }
    };

    getProductDetails = async slug => {
        try {
            this.setLoading(true);
            this.clearError();

            const response = await ecommerceService.getProductDetails(slug);

            if (response.error === false && response.data) {
                runInAction(() => {
                    this.state.product = response.data;
                });
                return {success: true, data: response.data};
            }
        } catch (error) {
            console.error('Get product details error:', error);
            const errorMessage =
                error.response?.data?.message ||
                error.message ||
                'فشل في جلب تفاصيل المنتج';
            this.setError(errorMessage);
            return {success: false, error: errorMessage};
        } finally {
            this.setLoading(false);
        }
    };

    getProductsByCategories = async (id, useApi = true) => {
        if (!useApi) {
            if (id === null) {
                this.state.searchedProducts = this.shuffle(
                    this.state.allProducts,
                );
            } else {
                this.state.searchedProducts = this.shuffle(
                    this.state.allProducts.filter(x => x.category === id),
                );
            }
            return;
        }

        try {
            this.setLoading(true);
            this.clearError();

            if (id === null) {
                // Get all products
                const response = await ecommerceService.getProducts();
                if (response.error === false && response.data) {
                    runInAction(() => {
                        this.state.searchedProducts =
                            response.data.data || response.data;
                    });
                }
            } else {
                // Get products by category
                const response = await ecommerceService.getCategoryProducts(id);
                if (response.error === false && response.data) {
                    runInAction(() => {
                        this.state.searchedProducts =
                            response.data.data || response.data;
                    });
                }
            }

            return {success: true};
        } catch (error) {
            console.error('Get products by category error:', error);
            // Fallback to mock data
            if (id === null) {
                this.state.searchedProducts = this.shuffle(
                    this.state.allProducts,
                );
            } else {
                this.state.searchedProducts = this.shuffle(
                    this.state.allProducts.filter(x => x.category === id),
                );
            }

            const errorMessage =
                error.response?.data?.message ||
                error.message ||
                'فشل في جلب منتجات الفئة';
            this.setError(errorMessage);
            return {success: false, error: errorMessage};
        } finally {
            this.setLoading(false);
        }
    };

    getSearchedProducts = async (text, useApi = true) => {
        if (!useApi) {
            this.state.searchedProducts = this.shuffle(
                this.state.allProducts.filter(x =>
                    x.name.toLowerCase().includes(text.toLowerCase()),
                ),
            );
            return;
        }

        try {
            this.setLoading(true);
            this.clearError();

            const response = await ecommerceService.getProducts({search: text});

            if (response.error === false && response.data) {
                runInAction(() => {
                    this.state.searchedProducts =
                        response.data.data || response.data;
                });
                return {success: true, data: response.data};
            }
        } catch (error) {
            console.error('Search products error:', error);
            // Fallback to mock data search
            runInAction(() => {
                this.state.searchedProducts = this.shuffle(
                    this.state.allProducts.filter(x =>
                        x.name.toLowerCase().includes(text.toLowerCase()),
                    ),
                );
            });

            const errorMessage =
                error.response?.data?.message ||
                error.message ||
                'فشل في البحث';
            this.setError(errorMessage);
            return {success: false, error: errorMessage};
        } finally {
            this.setLoading(false);
        }
    };

    getRandomProducts = () => {
        return this.shuffle(this.state.allProducts.slice(0, 6));
    };

    // Vehicle Parts Functions
    getVehicleCategories = async () => {
        try {
            this.setLoading(true);
            this.clearError();

            const response = await vehiclePartsService.getRootCategories();

            if (response.error === false && response.data) {
                runInAction(() => {
                    this.state.vehicleCategories =
                        response.data.data || response.data;
                });
                return {success: true, data: response.data};
            }
        } catch (error) {
            console.error('Get vehicle categories error:', error);
            const errorMessage =
                error.response?.data?.message ||
                error.message ||
                'فشل في جلب فئات المركبات';
            this.setError(errorMessage);
            return {success: false, error: errorMessage};
        } finally {
            this.setLoading(false);
        }
    };

    getVehicleMakes = async (vehicleTypeId = null) => {
        try {
            this.setLoading(true);
            this.clearError();

            const params = vehicleTypeId
                ? {vehicle_type_id: vehicleTypeId}
                : {};
            const response = await vehiclePartsService.getMakes(params);

            if (response.error === false && response.data) {
                runInAction(() => {
                    this.state.makes = response.data.data || response.data;
                });
                return {success: true, data: response.data};
            }
        } catch (error) {
            console.error('Get vehicle makes error:', error);
            const errorMessage =
                error.response?.data?.message ||
                error.message ||
                'فشل في جلب ماركات المركبات';
            this.setError(errorMessage);
            return {success: false, error: errorMessage};
        } finally {
            this.setLoading(false);
        }
    };

    getVehicleModels = async makeId => {
        try {
            this.setLoading(true);
            this.clearError();

            const response = await vehiclePartsService.getModels(makeId);

            if (response.error === false && response.data) {
                runInAction(() => {
                    this.state.models = response.data.data || response.data;
                    this.state.selectedMake = makeId;
                });
                return {success: true, data: response.data};
            }
        } catch (error) {
            console.error('Get vehicle models error:', error);
            const errorMessage =
                error.response?.data?.message ||
                error.message ||
                'فشل في جلب موديلات المركبة';
            this.setError(errorMessage);
            return {success: false, error: errorMessage};
        } finally {
            this.setLoading(false);
        }
    };

    searchVehicleParts = async searchData => {
        try {
            this.setLoading(true);
            this.clearError();

            const response = await vehiclePartsService.searchParts(searchData);

            if (response.error === false && response.data) {
                runInAction(() => {
                    this.state.searchedProducts =
                        response.data.data || response.data;
                });
                return {success: true, data: response.data};
            }
        } catch (error) {
            console.error('Search vehicle parts error:', error);
            const errorMessage =
                error.response?.data?.message ||
                error.message ||
                'فشل في البحث عن قطع الغيار';
            this.setError(errorMessage);
            return {success: false, error: errorMessage};
        } finally {
            this.setLoading(false);
        }
    };

    setCategory = id => {
        this.state.category = id;
    };

    setProduct = data => {
        this.state.product = data;
    };

    addToCart = product => {
        if (this.state.cart.find(x => x.product.id === product.id)) {
            this.createToast('Already in cart');
        } else {
            this.state.cart = [
                ...this.state.cart,
                {product: product, quantity: 1},
            ];
            this.createToast('Added to cart');
        }
    };

    updateCartQuantity = (id, quantity) => {
        if (quantity === 0) {
            this.state.cart = this.state.cart.filter(x => x.product.id !== id);
        } else {
            this.state.cart = this.state.cart.map(x =>
                x.product.id === id ? {...x, quantity: quantity} : x,
            );
        }
    };

    addToWishlist = product => {
        if (this.state.wishlist.find(x => x.id === product.id)) {
            this.createToast('Already in wishlist');
        } else {
            this.state.wishlist = [...this.state.wishlist, product];
            this.createToast('Added to wishlist');
        }
    };

    removeFromWishlist = id => {
        this.state.wishlist = this.state.wishlist.filter(x => x.id !== id);
    };
}

export const ProductStore = new Product();
