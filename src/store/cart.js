// 🛒 Cart Store - إدارة سلة التسوق
import {makeObservable, observable, action, runInAction} from 'mobx';
import {ToastAndroid} from 'react-native';
import {ecommerceService} from '../services/apiService';
import {cart as mockCart} from '../data/cart';

class Cart {
    state = {
        items: mockCart,
        loading: false,
        error: null,
        total: 0,
        subtotal: 0,
        tax: 0,
        shipping: 0,
        discount: 0,
        coupon: null,
    };

    constructor() {
        makeObservable(this, {
            state: observable,
            getCart: action,
            addToCart: action,
            updateCartItem: action,
            removeFromCart: action,
            clearCart: action,
            applyCoupon: action,
            removeCoupon: action,
            calculateTotals: action,
            setLoading: action,
            setError: action,
            clearError: action,
        });

        this.calculateTotals();
    }

    createToast = message => {
        ToastAndroid.showWithGravityAndOffset(
            message,
            ToastAndroid.LONG,
            ToastAndroid.BOTTOM,
            0,
            50,
        );
    };

    setLoading = loading => {
        this.state.loading = loading;
    };

    setError = error => {
        this.state.error = error;
    };

    clearError = () => {
        this.state.error = null;
    };

    calculateTotals = () => {
        const subtotal = this.state.items.reduce((sum, item) => {
            return sum + item.product.price * item.quantity;
        }, 0);

        const tax = subtotal * 0.1; // 10% tax
        const shipping = subtotal > 100 ? 0 : 10; // Free shipping over $100
        const discount = this.state.coupon
            ? subtotal * (this.state.coupon.discount / 100)
            : 0;
        const total = subtotal + tax + shipping - discount;

        runInAction(() => {
            this.state.subtotal = subtotal;
            this.state.tax = tax;
            this.state.shipping = shipping;
            this.state.discount = discount;
            this.state.total = total;
        });
    };

    getCart = async (useApi = false) => {
        if (!useApi) {
            this.calculateTotals();
            return {success: true, data: this.state.items};
        }

        try {
            this.setLoading(true);
            this.clearError();

            const response = await ecommerceService.getCart();

            if (response.error === false && response.data) {
                runInAction(() => {
                    this.state.items = response.data.items || response.data;
                });
                this.calculateTotals();
                return {success: true, data: response.data};
            }
        } catch (error) {
            console.error('Get cart error:', error);
            const errorMessage =
                error.response?.data?.message ||
                error.message ||
                'فشل في جلب السلة';
            this.setError(errorMessage);
            return {success: false, error: errorMessage};
        } finally {
            this.setLoading(false);
        }
    };

    addToCart = async (product, quantity = 1, useApi = false) => {
        if (!useApi) {
            // Local cart management
            const existingItem = this.state.items.find(
                item => item.product.id === product.id,
            );

            if (existingItem) {
                this.updateCartItem(
                    existingItem.product.id,
                    existingItem.quantity + quantity,
                    false,
                );
                this.createToast('تم تحديث الكمية في السلة');
            } else {
                runInAction(() => {
                    this.state.items.push({product, quantity});
                });
                this.calculateTotals();
                this.createToast('تم إضافة المنتج للسلة');
            }

            return {success: true};
        }

        try {
            this.setLoading(true);
            this.clearError();

            const response = await ecommerceService.addToCart({
                product_id: product.id,
                quantity: quantity,
            });

            if (response.error === false) {
                // Refresh cart
                await this.getCart(true);
                this.createToast('تم إضافة المنتج للسلة');
                return {success: true, data: response.data};
            }
        } catch (error) {
            console.error('Add to cart error:', error);
            // Fallback to local cart
            this.addToCart(product, quantity, false);
            const errorMessage =
                error.response?.data?.message ||
                error.message ||
                'فشل في إضافة المنتج للسلة';
            this.setError(errorMessage);
            return {success: false, error: errorMessage};
        } finally {
            this.setLoading(false);
        }
    };

    updateCartItem = async (productId, quantity, useApi = false) => {
        if (!useApi) {
            if (quantity <= 0) {
                this.removeFromCart(productId, false);
                return;
            }

            runInAction(() => {
                this.state.items = this.state.items.map(item =>
                    item.product.id === productId ? {...item, quantity} : item,
                );
            });
            this.calculateTotals();
            return {success: true};
        }

        try {
            this.setLoading(true);
            this.clearError();

            const response = await ecommerceService.updateCart({
                product_id: productId,
                quantity: quantity,
            });

            if (response.error === false) {
                // Refresh cart
                await this.getCart(true);
                return {success: true, data: response.data};
            }
        } catch (error) {
            console.error('Update cart error:', error);
            // Fallback to local update
            this.updateCartItem(productId, quantity, false);
            const errorMessage =
                error.response?.data?.message ||
                error.message ||
                'فشل في تحديث السلة';
            this.setError(errorMessage);
            return {success: false, error: errorMessage};
        } finally {
            this.setLoading(false);
        }
    };

    removeFromCart = async (productId, useApi = false) => {
        if (!useApi) {
            runInAction(() => {
                this.state.items = this.state.items.filter(
                    item => item.product.id !== productId,
                );
            });
            this.calculateTotals();
            this.createToast('تم حذف المنتج من السلة');
            return {success: true};
        }

        try {
            this.setLoading(true);
            this.clearError();

            const response = await ecommerceService.removeFromCart(productId);

            if (response.error === false) {
                // Refresh cart
                await this.getCart(true);
                this.createToast('تم حذف المنتج من السلة');
                return {success: true, data: response.data};
            }
        } catch (error) {
            console.error('Remove from cart error:', error);
            // Fallback to local removal
            this.removeFromCart(productId, false);
            const errorMessage =
                error.response?.data?.message ||
                error.message ||
                'فشل في حذف المنتج';
            this.setError(errorMessage);
            return {success: false, error: errorMessage};
        } finally {
            this.setLoading(false);
        }
    };

    clearCart = async (useApi = false) => {
        if (!useApi) {
            runInAction(() => {
                this.state.items = [];
                this.state.coupon = null;
            });
            this.calculateTotals();
            this.createToast('تم إفراغ السلة');
            return {success: true};
        }

        try {
            this.setLoading(true);
            this.clearError();

            // Clear cart via API
            for (const item of this.state.items) {
                await ecommerceService.removeFromCart(item.product.id);
            }

            runInAction(() => {
                this.state.items = [];
                this.state.coupon = null;
            });
            this.calculateTotals();
            this.createToast('تم إفراغ السلة');
            return {success: true};
        } catch (error) {
            console.error('Clear cart error:', error);
            // Fallback to local clear
            this.clearCart(false);
            const errorMessage =
                error.response?.data?.message ||
                error.message ||
                'فشل في إفراغ السلة';
            this.setError(errorMessage);
            return {success: false, error: errorMessage};
        } finally {
            this.setLoading(false);
        }
    };

    applyCoupon = async (couponCode, useApi = false) => {
        if (!useApi) {
            // Mock coupon application
            const mockCoupon = {
                code: couponCode,
                discount: 10, // 10% discount
                type: 'percentage',
            };

            runInAction(() => {
                this.state.coupon = mockCoupon;
            });
            this.calculateTotals();
            this.createToast('تم تطبيق الكوبون');
            return {success: true, data: mockCoupon};
        }

        try {
            this.setLoading(true);
            this.clearError();

            const response = await ecommerceService.applyCoupon({
                coupon_code: couponCode,
                cart_id: 'current', // or actual cart ID
            });

            if (response.error === false && response.data) {
                runInAction(() => {
                    this.state.coupon = response.data.coupon;
                });
                this.calculateTotals();
                this.createToast('تم تطبيق الكوبون');
                return {success: true, data: response.data};
            }
        } catch (error) {
            console.error('Apply coupon error:', error);
            const errorMessage =
                error.response?.data?.message ||
                error.message ||
                'كوبون غير صالح';
            this.setError(errorMessage);
            this.createToast(errorMessage);
            return {success: false, error: errorMessage};
        } finally {
            this.setLoading(false);
        }
    };

    removeCoupon = async (useApi = false) => {
        if (!useApi) {
            runInAction(() => {
                this.state.coupon = null;
            });
            this.calculateTotals();
            this.createToast('تم إلغاء الكوبون');
            return {success: true};
        }

        try {
            this.setLoading(true);
            this.clearError();

            const response = await ecommerceService.removeCoupon({
                cart_id: 'current', // or actual cart ID
            });

            if (response.error === false) {
                runInAction(() => {
                    this.state.coupon = null;
                });
                this.calculateTotals();
                this.createToast('تم إلغاء الكوبون');
                return {success: true};
            }
        } catch (error) {
            console.error('Remove coupon error:', error);
            // Fallback to local removal
            this.removeCoupon(false);
            const errorMessage =
                error.response?.data?.message ||
                error.message ||
                'فشل في إلغاء الكوبون';
            this.setError(errorMessage);
            return {success: false, error: errorMessage};
        } finally {
            this.setLoading(false);
        }
    };

    getCartItemsCount = () => {
        return this.state.items.reduce(
            (count, item) => count + item.quantity,
            0,
        );
    };

    isProductInCart = productId => {
        return this.state.items.some(item => item.product.id === productId);
    };
}

export const CartStore = new Cart();
