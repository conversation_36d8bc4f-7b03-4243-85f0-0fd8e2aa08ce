// 🔧 API Service - خدمات الاتصال بالـ APIs
import axios from 'axios';
import {getBaseUrl} from '../store/url';
import {
    AUTH_ENDPOINTS,
    ECOMMERCE_ENDPOINTS,
    VEHICLE_PARTS_ENDPOINTS,
    LOCATION_ENDPOINTS,
    CONTACT_ENDPOINTS,
    replaceUrlPlaceholders,
    buildQueryString,
} from '../constants/apiEndpoints';

// إعداد Axios instance
const apiClient = axios.create({
    baseURL: getBaseUrl(),
    timeout: 10000,
    headers: {
        'Content-Type': 'application/json',
        Accept: 'application/json',
    },
});

// Interceptor للطلبات - إضافة token إذا كان متوفراً
apiClient.interceptors.request.use(
    config => {
        // يمكن إضافة token هنا لاحقاً
        // const token = getAuthToken();
        // if (token) {
        //   config.headers.Authorization = `Bearer ${token}`;
        // }
        console.log('API Request:', config.method?.toUpperCase(), config.url);
        return config;
    },
    error => {
        console.error('Request Error:', error);
        return Promise.reject(error);
    },
);

// Interceptor للاستجابات - معالجة الأخطاء
apiClient.interceptors.response.use(
    response => {
        console.log('API Response:', response.status, response.config.url);
        return response;
    },
    error => {
        console.error(
            'Response Error:',
            error.response?.status,
            error.response?.data,
        );

        // معالجة أخطاء المصادقة
        if (error.response?.status === 401) {
            // إعادة توجيه لصفحة تسجيل الدخول
            console.log('Authentication required');
        }

        return Promise.reject(error);
    },
);

// ================================
// 🔐 Authentication Services
// ================================
export const authService = {
    // تسجيل الدخول
    login: async credentials => {
        const response = await apiClient.post(
            AUTH_ENDPOINTS.LOGIN,
            credentials,
        );
        return response.data;
    },

    // تسجيل حساب جديد
    register: async userData => {
        const response = await apiClient.post(
            AUTH_ENDPOINTS.REGISTER,
            userData,
        );
        return response.data;
    },

    // تسجيل الخروج
    logout: async () => {
        const response = await apiClient.post(AUTH_ENDPOINTS.LOGOUT);
        return response.data;
    },

    // نسيان كلمة المرور
    forgotPassword: async email => {
        const response = await apiClient.post(AUTH_ENDPOINTS.FORGOT_PASSWORD, {
            email,
        });
        return response.data;
    },

    // فحص البريد الإلكتروني
    checkEmail: async email => {
        const response = await apiClient.post(AUTH_ENDPOINTS.EMAIL_CHECK, {
            email,
        });
        return response.data;
    },

    // OTP Services
    sendOtp: async (phone, type) => {
        const response = await apiClient.post(AUTH_ENDPOINTS.OTP_SEND, {
            phone,
            type,
        });
        return response.data;
    },

    verifyOtp: async (phone, code, type) => {
        const response = await apiClient.post(AUTH_ENDPOINTS.OTP_VERIFY, {
            phone,
            code,
            type,
        });
        return response.data;
    },
};

// ================================
// 🛒 Ecommerce Services
// ================================
export const ecommerceService = {
    // Products
    getProducts: async (params = {}) => {
        const queryString = buildQueryString(params);
        const url = `${ECOMMERCE_ENDPOINTS.PRODUCTS}${
            queryString ? `?${queryString}` : ''
        }`;
        const response = await apiClient.get(url);
        return response.data;
    },

    getProductDetails: async slug => {
        const response = await apiClient.get(
            `${ECOMMERCE_ENDPOINTS.PRODUCT_DETAILS}${slug}`,
        );
        return response.data;
    },

    getRelatedProducts: async slug => {
        const url = replaceUrlPlaceholders(
            ECOMMERCE_ENDPOINTS.PRODUCT_RELATED,
            {slug},
        );
        const response = await apiClient.get(url);
        return response.data;
    },

    // Categories
    getCategories: async (params = {}) => {
        const queryString = buildQueryString(params);
        const url = `${ECOMMERCE_ENDPOINTS.CATEGORIES}${
            queryString ? `?${queryString}` : ''
        }`;
        const response = await apiClient.get(url);
        return response.data;
    },

    getCategoryDetails: async slug => {
        const response = await apiClient.get(
            `${ECOMMERCE_ENDPOINTS.CATEGORY_DETAILS}${slug}`,
        );
        return response.data;
    },

    getCategoryProducts: async (id, params = {}) => {
        const url = replaceUrlPlaceholders(
            ECOMMERCE_ENDPOINTS.CATEGORY_PRODUCTS,
            {id},
        );
        const queryString = buildQueryString(params);
        const finalUrl = `${url}${queryString ? `?${queryString}` : ''}`;
        const response = await apiClient.get(finalUrl);
        return response.data;
    },

    // Brands
    getBrands: async (params = {}) => {
        const queryString = buildQueryString(params);
        const url = `${ECOMMERCE_ENDPOINTS.BRANDS}${
            queryString ? `?${queryString}` : ''
        }`;
        const response = await apiClient.get(url);
        return response.data;
    },

    // Cart
    getCart: async () => {
        const response = await apiClient.get(ECOMMERCE_ENDPOINTS.CART);
        return response.data;
    },

    addToCart: async productData => {
        const response = await apiClient.post(
            ECOMMERCE_ENDPOINTS.CART_ADD,
            productData,
        );
        return response.data;
    },

    updateCart: async cartData => {
        const response = await apiClient.put(
            ECOMMERCE_ENDPOINTS.CART_UPDATE,
            cartData,
        );
        return response.data;
    },

    removeFromCart: async itemId => {
        const response = await apiClient.delete(
            `${ECOMMERCE_ENDPOINTS.CART_REMOVE}/${itemId}`,
        );
        return response.data;
    },

    // Flash Sales
    getFlashSales: async (keys = []) => {
        const params = keys.length > 0 ? {keys: keys.join(',')} : {};
        const queryString = buildQueryString(params);
        const url = `${ECOMMERCE_ENDPOINTS.FLASH_SALES}${
            queryString ? `?${queryString}` : ''
        }`;
        const response = await apiClient.get(url);
        return response.data;
    },
};

// ================================
// 🚗 Vehicle Parts Services
// ================================
export const vehiclePartsService = {
    getRootCategories: async () => {
        const response = await apiClient.get(
            VEHICLE_PARTS_ENDPOINTS.ROOT_CATEGORIES,
        );
        return response.data;
    },

    getChildCategories: async parentId => {
        const url = replaceUrlPlaceholders(
            VEHICLE_PARTS_ENDPOINTS.CHILD_CATEGORIES,
            {parentId},
        );
        const response = await apiClient.get(url);
        return response.data;
    },

    getMakes: async (params = {}) => {
        const queryString = buildQueryString(params);
        const url = `${VEHICLE_PARTS_ENDPOINTS.MAKES}${
            queryString ? `?${queryString}` : ''
        }`;
        const response = await apiClient.get(url);
        return response.data;
    },

    getModels: async makeId => {
        const url = replaceUrlPlaceholders(VEHICLE_PARTS_ENDPOINTS.MODELS, {
            makeId,
        });
        const response = await apiClient.get(url);
        return response.data;
    },

    searchParts: async searchData => {
        const response = await apiClient.post(
            VEHICLE_PARTS_ENDPOINTS.SEARCH,
            searchData,
        );
        return response.data;
    },

    getPopularParts: async (params = {}) => {
        const queryString = buildQueryString(params);
        const url = `${VEHICLE_PARTS_ENDPOINTS.POPULAR}${
            queryString ? `?${queryString}` : ''
        }`;
        const response = await apiClient.get(url);
        return response.data;
    },
};

// ================================
// 📍 Location Services
// ================================
export const locationService = {
    getCountries: async (params = {}) => {
        const queryString = buildQueryString(params);
        const url = `${LOCATION_ENDPOINTS.COUNTRIES}${
            queryString ? `?${queryString}` : ''
        }`;
        const response = await apiClient.get(url);
        return response.data;
    },

    getStates: async countryId => {
        const url = replaceUrlPlaceholders(LOCATION_ENDPOINTS.COUNTRY_STATES, {
            id: countryId,
        });
        const response = await apiClient.get(url);
        return response.data;
    },

    getCities: async stateId => {
        const url = replaceUrlPlaceholders(LOCATION_ENDPOINTS.STATE_CITIES, {
            id: stateId,
        });
        const response = await apiClient.get(url);
        return response.data;
    },

    searchCities: async (params = {}) => {
        const queryString = buildQueryString(params);
        const url = `${LOCATION_ENDPOINTS.CITIES_SEARCH}${
            queryString ? `?${queryString}` : ''
        }`;
        const response = await apiClient.get(url);
        return response.data;
    },
};

// ================================
// 📞 Contact Services
// ================================
export const contactService = {
    sendMessage: async messageData => {
        const response = await apiClient.post(
            CONTACT_ENDPOINTS.SEND_MESSAGE,
            messageData,
        );
        return response.data;
    },

    getSubjects: async () => {
        const response = await apiClient.get(CONTACT_ENDPOINTS.SUBJECTS);
        return response.data;
    },

    getCustomFields: async () => {
        const response = await apiClient.get(CONTACT_ENDPOINTS.CUSTOM_FIELDS);
        return response.data;
    },
};

// تصدير العميل الأساسي للاستخدام المباشر
export default apiClient;
