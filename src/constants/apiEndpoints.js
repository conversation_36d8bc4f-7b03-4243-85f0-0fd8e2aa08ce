// 📡 API Endpoints Constants - مشروع دليل
// جميع نقاط النهاية للـ APIs المتاحة في المشروع

// ================================
// 🔐 Authentication APIs
// ================================
export const AUTH_ENDPOINTS = {
    REGISTER: '/register',
    LOGIN: '/login',
    LOGOUT: '/logout',
    FORGOT_PASSWORD: '/password/forgot',
    RESET_PASSWORD: '/password/reset',
    EMAIL_CHECK: '/email/check',
    RESEND_VERIFICATION: '/resend-verify-account-email',

    // OTP System
    OTP_SEND: '/otp/send',
    OTP_VERIFY: '/otp/verify',
    OTP_LOGIN: '/otp/login',
};

// ================================
// 🛒 Ecommerce APIs
// ================================
export const ECOMMERCE_ENDPOINTS = {
    // Products
    PRODUCTS: '/ecommerce/products',
    PRODUCT_DETAILS: '/ecommerce/products/', // + slug
    PRODUCT_RELATED: '/ecommerce/products/{slug}/related',
    PRODUCT_CROSS_SALE: '/ecommerce/products/{slug}/cross-sale',
    PRODUCT_REVIEWS: '/ecommerce/products/{slug}/reviews',
    PRODUCT_VARIATION: '/ecommerce/product-variation/', // + id

    // Categories
    CATEGORIES: '/ecommerce/product-categories',
    CATEGORY_DETAILS: '/ecommerce/product-categories/', // + slug
    CATEGORY_PRODUCTS: '/ecommerce/product-categories/{id}/products',

    // Brands
    BRANDS: '/ecommerce/brands',
    BRAND_DETAILS: '/ecommerce/brands/', // + slug
    BRAND_PRODUCTS: '/ecommerce/brands/{id}/products',

    // Cart
    CART: '/ecommerce/cart',
    CART_ADD: '/ecommerce/cart/add',
    CART_UPDATE: '/ecommerce/cart/update',
    CART_REMOVE: '/ecommerce/cart/remove',
    CART_CLEAR: '/ecommerce/cart/clear',

    // Orders (require authentication)
    ORDERS: '/ecommerce/orders',
    ORDER_DETAILS: '/ecommerce/orders/', // + id
    ORDER_CANCEL: '/ecommerce/orders/{id}/cancel',
    ORDER_INVOICE: '/ecommerce/orders/{id}/invoice',
    ORDER_UPLOAD_PROOF: '/ecommerce/orders/{id}/upload-proof',
    ORDER_DOWNLOAD_PROOF: '/ecommerce/orders/{id}/download-proof',
    ORDER_CONFIRM_DELIVERY: '/ecommerce/orders/{id}/confirm-delivery',

    // Checkout
    CHECKOUT: '/ecommerce/checkout',
    CHECKOUT_PROCESS: '/ecommerce/checkout/process',

    // Reviews (require authentication)
    REVIEWS: '/ecommerce/reviews',
    REVIEW_CREATE: '/ecommerce/reviews',
    REVIEW_DELETE: '/ecommerce/reviews/', // + id

    // Compare
    COMPARE: '/ecommerce/compare',
    COMPARE_ADD: '/ecommerce/compare',
    COMPARE_REMOVE: '/ecommerce/compare/', // + id
    COMPARE_GET: '/ecommerce/compare/', // + id

    // Coupons
    COUPONS: '/ecommerce/coupons',
    COUPON_APPLY: '/ecommerce/coupon/apply',
    COUPON_REMOVE: '/ecommerce/coupon/remove',

    // Flash Sales
    FLASH_SALES: '/ecommerce/flash-sales',

    // Currencies
    CURRENCIES: '/ecommerce/currencies',
    CURRENT_CURRENCY: '/ecommerce/currencies/current',

    // Addresses (require authentication)
    ADDRESSES: '/ecommerce/addresses',
    ADDRESS_CREATE: '/ecommerce/addresses',
    ADDRESS_UPDATE: '/ecommerce/addresses/', // + id
    ADDRESS_DELETE: '/ecommerce/addresses/', // + id
};

// ================================
// 🚗 Vehicle Parts Finder APIs
// ================================
export const VEHICLE_PARTS_ENDPOINTS = {
    ROOT_CATEGORIES: '/vehicle-parts/root-categories',
    CHILD_CATEGORIES: '/vehicle-parts/categories/{parentId}/children',
    MAKES: '/vehicle-parts/makes',
    MODELS: '/vehicle-parts/makes/{makeId}/models',
    YEARS: '/vehicle-parts/models/{modelId}/years',
    FIND_PARTS: '/vehicle-parts/find-parts',
    SEARCH: '/vehicle-parts/search',
    POPULAR: '/vehicle-parts/popular',
    CATEGORY_PARTS: '/vehicle-parts/categories/{id}/parts',
};

// ================================
// 📍 Location APIs
// ================================
export const LOCATION_ENDPOINTS = {
    COUNTRIES: '/location/countries',
    COUNTRY_DETAILS: '/location/countries/', // + id
    COUNTRY_STATES: '/location/countries/{id}/states',
    COUNTRY_CITIES: '/location/countries/{id}/cities',
    STATES: '/location/states',
    STATE_DETAILS: '/location/states/', // + id
    STATE_CITIES: '/location/states/{id}/cities',
    CITIES: '/location/cities',
    CITY_DETAILS: '/location/cities/', // + id
    CITIES_SEARCH: '/location/cities/search',
};

// ================================
// 📞 Contact APIs
// ================================
export const CONTACT_ENDPOINTS = {
    SEND_MESSAGE: '/contact',
    SUBJECTS: '/contact/subjects',
    CUSTOM_FIELDS: '/contact/custom-fields',
    CHECK_EMAIL: '/contact/check-email',
};

// ================================
// 📧 Newsletter APIs
// ================================
export const NEWSLETTER_ENDPOINTS = {
    SUBSCRIBE: '/newsletter/subscribe',
    UNSUBSCRIBE: '/newsletter/unsubscribe',
    STATUS: '/newsletter/status/', // + email
};

// ================================
// 📝 Blog APIs
// ================================
export const BLOG_ENDPOINTS = {
    POSTS: '/posts',
    POST_DETAILS: '/posts/', // + slug
    POST_SEARCH: '/search',
    POST_FILTERS: '/posts/filters',
    CATEGORIES: '/categories',
    CATEGORY_DETAILS: '/categories/', // + slug
    CATEGORY_FILTERS: '/categories/filters',
    TAGS: '/tags',
};

// ================================
// 🖼️ Gallery APIs
// ================================
export const GALLERY_ENDPOINTS = {
    GALLERIES: '/galleries',
    GALLERY_DETAILS: '/galleries/', // + id
    GALLERY_IMAGES: '/galleries/{id}/images',
    GALLERY_SEARCH: '/galleries/search',
    GALLERY_RANDOM: '/galleries/random',
};

// ================================
// 💬 Testimonial APIs
// ================================
export const TESTIMONIAL_ENDPOINTS = {
    TESTIMONIALS: '/testimonials',
    TESTIMONIAL_DETAILS: '/testimonials/', // + id
    TESTIMONIAL_CREATE: '/testimonials',
    TESTIMONIAL_SEARCH: '/testimonials/search',
    TESTIMONIAL_RANDOM: '/testimonials/random',
};

// ================================
// 🏪 Marketplace APIs
// ================================
export const MARKETPLACE_ENDPOINTS = {
    STORES: '/marketplace/stores',
    STORE_DETAILS: '/marketplace/stores/', // + id
    STORE_PRODUCTS: '/marketplace/stores/{id}/products',
    STORE_SEARCH: '/marketplace/stores/search',
    FEATURED_STORES: '/marketplace/stores/featured',
    MARKETPLACE_STATS: '/marketplace/stats',
};

// ================================
// 👤 User Profile APIs (require authentication)
// ================================
export const PROFILE_ENDPOINTS = {
    PROFILE: '/me',
    UPDATE_PROFILE: '/me',
    UPDATE_AVATAR: '/update/avatar',
    UPDATE_PASSWORD: '/update/password',
    SETTINGS: '/settings',
    USER_REVIEWS: '/ecommerce/reviews',
};

// ================================
// 🔔 Notification APIs (require authentication)
// ================================
export const NOTIFICATION_ENDPOINTS = {
    NOTIFICATIONS: '/notifications',
    NOTIFICATION_STATS: '/notifications/stats',
    MARK_ALL_READ: '/notifications/mark-all-read',
    MARK_READ: '/notifications/{id}/read',
    MARK_CLICKED: '/notifications/{id}/clicked',
    DELETE_NOTIFICATION: '/notifications/', // + id
};

// ================================
// 🌐 Language & Currency APIs
// ================================
export const SYSTEM_ENDPOINTS = {
    LANGUAGES: '/languages',
    CURRENT_LANGUAGE: '/languages/current',
    CURRENCIES: '/ecommerce/currencies',
    CURRENT_CURRENCY: '/ecommerce/currencies/current',
};

// ================================
// 📱 Device Token APIs
// ================================
export const DEVICE_TOKEN_ENDPOINTS = {
    REGISTER_TOKEN: '/device-tokens',
    UPDATE_TOKEN: '/device-tokens/{id}',
    DELETE_TOKEN: '/device-tokens/{id}',
};

// ================================
// 🎯 Simple Slider APIs
// ================================
export const SLIDER_ENDPOINTS = {
    SIMPLE_SLIDERS: '/simple-sliders',
};

// ================================
// 📢 Ads APIs
// ================================
export const ADS_ENDPOINTS = {
    ADS: '/ads',
};

// Helper function to replace placeholders in URLs
export const replaceUrlPlaceholders = (url, params) => {
    let finalUrl = url;
    Object.keys(params).forEach(key => {
        finalUrl = finalUrl.replace(`{${key}}`, params[key]);
    });
    return finalUrl;
};

// Helper function to build query string
export const buildQueryString = params => {
    const queryParams = new URLSearchParams(params);
    return queryParams.toString();
};
