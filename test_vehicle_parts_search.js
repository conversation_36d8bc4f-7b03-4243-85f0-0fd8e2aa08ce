/**
 * اختبار شامل لوظيفة البحث عن قطع الغيار
 */

console.log('🔧 اختبار وظيفة البحث عن قطع الغيار...');

// فحص وجود العناصر
function checkVehicleFinderElements() {
    console.log('\n📋 فحص عناصر البحث عن قطع الغيار...');
    
    const elements = {
        vehicleFinder: document.getElementById('vehicleFinder'),
        partTypeSelect: document.getElementById('partTypeSelect'),
        brandSelect: document.getElementById('brandSelect'),
        modelSelect: document.getElementById('modelSelect'),
        yearSelect: document.getElementById('yearSelect'),
        searchBtn: document.getElementById('searchPartsBtn'),
        resultsDiv: document.getElementById('vehicleFinderResults')
    };
    
    Object.entries(elements).forEach(([name, element]) => {
        if (element) {
            console.log(`✅ ${name} موجود`);
            if (element.tagName === 'SELECT') {
                console.log(`   - عدد الخيارات: ${element.options.length}`);
                console.log(`   - معطل: ${element.disabled}`);
            }
            if (element.tagName === 'BUTTON') {
                console.log(`   - معطل: ${element.disabled}`);
                console.log(`   - النص: ${element.textContent}`);
            }
        } else {
            console.log(`❌ ${name} غير موجود`);
        }
    });
    
    return elements;
}

// اختبار وظائف البحث
function checkSearchFunctions() {
    console.log('\n🔧 فحص وظائف البحث...');
    
    const functions = [
        'initializeVehicleFinder',
        'onPartTypeChange',
        'onBrandChange', 
        'onModelChange',
        'onYearChange',
        'searchVehicleParts',
        'loadBrandsForPartType',
        'loadModelsForBrand',
        'loadYearsForModel',
        'updateSearchButton',
        'populateBrandsSelect',
        'displayVehicleParts',
        'clearVehicleSearch'
    ];
    
    functions.forEach(funcName => {
        if (typeof window[funcName] === 'function') {
            console.log(`✅ ${funcName} موجودة`);
        } else {
            console.log(`❌ ${funcName} غير موجودة`);
        }
    });
}

// اختبار تحديد نوع القطعة
async function testPartTypeSelection() {
    console.log('\n🔧 اختبار تحديد نوع القطعة...');
    
    const partTypeSelect = document.getElementById('partTypeSelect');
    const brandSelect = document.getElementById('brandSelect');
    
    if (!partTypeSelect) {
        console.log('❌ عنصر نوع القطعة غير موجود');
        return;
    }
    
    // اختبار تحديد نوع القطعة
    console.log('اختيار "ادوات صدر"...');
    partTypeSelect.value = '1';
    
    // تشغيل حدث التغيير
    if (typeof onPartTypeChange === 'function') {
        try {
            await onPartTypeChange();
            
            setTimeout(() => {
                console.log('حالة العلامة التجارية بعد اختيار النوع:');
                console.log('- معطل:', brandSelect.disabled);
                console.log('- عدد الخيارات:', brandSelect.options.length);
                
                if (brandSelect.options.length > 1) {
                    console.log('✅ تم تحميل العلامات التجارية');
                    
                    // اختبار اختيار علامة تجارية
                    if (brandSelect.options.length > 1) {
                        brandSelect.selectedIndex = 1;
                        console.log('تم اختيار:', brandSelect.options[1].text);
                        
                        if (typeof onBrandChange === 'function') {
                            onBrandChange();
                        }
                    }
                } else {
                    console.log('⚠️ لم يتم تحميل علامات تجارية');
                }
            }, 1000);
            
        } catch (error) {
            console.error('❌ خطأ في onPartTypeChange:', error);
        }
    } else {
        console.log('❌ وظيفة onPartTypeChange غير موجودة');
    }
}

// اختبار البحث المباشر
async function testDirectSearch() {
    console.log('\n🔍 اختبار البحث المباشر...');
    
    const partTypeSelect = document.getElementById('partTypeSelect');
    const searchBtn = document.getElementById('searchPartsBtn');
    const resultsDiv = document.getElementById('vehicleFinderResults');
    
    if (!partTypeSelect || !searchBtn) {
        console.log('❌ عناصر البحث غير موجودة');
        return;
    }
    
    // تحديد نوع القطعة
    partTypeSelect.value = '1'; // ادوات صدر
    
    // تحديث حالة الزر
    if (typeof updateSearchButton === 'function') {
        updateSearchButton();
    }
    
    console.log('حالة زر البحث:');
    console.log('- معطل:', searchBtn.disabled);
    console.log('- النص:', searchBtn.textContent);
    
    if (!searchBtn.disabled) {
        console.log('🔍 تشغيل البحث...');
        
        if (typeof searchVehicleParts === 'function') {
            try {
                await searchVehicleParts();
                
                setTimeout(() => {
                    if (resultsDiv) {
                        console.log('نتائج البحث:');
                        console.log('- ظاهر:', resultsDiv.classList.contains('show'));
                        console.log('- المحتوى:', resultsDiv.innerHTML.substring(0, 100) + '...');
                        
                        if (resultsDiv.innerHTML.includes('vehicle-finder-loading')) {
                            console.log('⏳ البحث قيد التنفيذ...');
                        } else if (resultsDiv.innerHTML.includes('vehicle-finder-no-results')) {
                            console.log('🔍 لا توجد نتائج');
                        } else if (resultsDiv.innerHTML.includes('vehicle-search-header')) {
                            console.log('✅ تم العثور على نتائج!');
                        } else if (resultsDiv.innerHTML.includes('vehicle-finder-error')) {
                            console.log('❌ خطأ في البحث');
                        }
                    }
                }, 2000);
                
            } catch (error) {
                console.error('❌ خطأ في البحث:', error);
            }
        } else {
            console.log('❌ وظيفة searchVehicleParts غير موجودة');
        }
    } else {
        console.log('⚠️ زر البحث معطل');
    }
}

// اختبار البحث بمعايير مختلفة
async function testSearchWithDifferentCriteria() {
    console.log('\n🎯 اختبار البحث بمعايير مختلفة...');
    
    const testCases = [
        { partType: '1', description: 'ادوات صدر' },
        { partType: '2', description: 'ادوات محرك' },
        { partType: '3', description: 'ادوات كهربائية' },
        { partType: '4', description: 'قطع غيار' }
    ];
    
    for (let i = 0; i < testCases.length; i++) {
        const testCase = testCases[i];
        console.log(`\n📋 اختبار ${i + 1}: ${testCase.description}`);
        
        const partTypeSelect = document.getElementById('partTypeSelect');
        if (partTypeSelect) {
            partTypeSelect.value = testCase.partType;
            
            if (typeof updateSearchButton === 'function') {
                updateSearchButton();
            }
            
            const searchBtn = document.getElementById('searchPartsBtn');
            if (searchBtn && !searchBtn.disabled) {
                console.log('🔍 تشغيل البحث...');
                
                if (typeof searchVehicleParts === 'function') {
                    try {
                        await searchVehicleParts();
                        
                        // انتظار قصير بين الاختبارات
                        await new Promise(resolve => setTimeout(resolve, 1000));
                        
                    } catch (error) {
                        console.error('❌ خطأ في البحث:', error);
                    }
                }
            }
        }
    }
}

// اختبار شامل
async function runAllVehicleSearchTests() {
    console.log('🚀 بدء جميع اختبارات البحث عن قطع الغيار...');
    
    // فحص العناصر والوظائف
    checkVehicleFinderElements();
    checkSearchFunctions();
    
    // اختبار التفاعل
    await testPartTypeSelection();
    
    // انتظار قبل الاختبار التالي
    setTimeout(async () => {
        await testDirectSearch();
        
        // انتظار قبل الاختبار الأخير
        setTimeout(async () => {
            await testSearchWithDifferentCriteria();
        }, 3000);
    }, 2000);
    
    console.log('\n✅ انتهت جميع اختبارات البحث');
}

// تشغيل الاختبارات
runAllVehicleSearchTests();

console.log('\n📋 الاختبارات المتاحة:');
console.log('- checkVehicleFinderElements() - فحص العناصر');
console.log('- checkSearchFunctions() - فحص الوظائف');
console.log('- testPartTypeSelection() - اختبار تحديد نوع القطعة');
console.log('- testDirectSearch() - اختبار البحث المباشر');
console.log('- testSearchWithDifferentCriteria() - اختبار معايير مختلفة');
console.log('- runAllVehicleSearchTests() - تشغيل جميع الاختبارات');

console.log('\n🎯 المتوقع بعد الإصلاح:');
console.log('✅ تحميل العلامات التجارية عند اختيار نوع القطعة');
console.log('✅ تفعيل زر البحث عند اختيار معيار واحد على الأقل');
console.log('✅ عرض نتائج البحث أو رسالة "لا توجد نتائج"');
console.log('✅ عمل البحث مع جميع أنواع القطع');
