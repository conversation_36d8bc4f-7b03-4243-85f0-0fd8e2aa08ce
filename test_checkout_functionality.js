/**
 * اختبار وظيفة إتمام الطلب
 */

console.log('🛒 اختبار وظيفة إتمام الطلب...');

// إعداد بيانات الاختبار
function setupTestData() {
    console.log('\n📋 إعداد بيانات الاختبار...');
    
    // إضافة مستخدم تجريبي
    window.user = {
        id: 1,
        name: 'أحمد محمد',
        email: '<EMAIL>',
        phone: '07701234567',
        address: 'بغداد - الكرادة - شارع الرئيسي'
    };
    
    // إضافة منتجات للسلة
    window.cart = [
        {
            id: 1,
            name: 'شمعة صدر خلفية كيا سبكترا',
            price: 25000,
            quantity: 2,
            image: 'https://via.placeholder.com/150'
        },
        {
            id: 2,
            name: 'فلتر هواء هيونداي',
            price: 15000,
            quantity: 1,
            image: 'https://via.placeholder.com/150'
        }
    ];
    
    console.log('✅ تم إعداد البيانات:');
    console.log('- المستخدم:', window.user.name);
    console.log('- عدد المنتجات في السلة:', window.cart.length);
    console.log('- إجمالي المبلغ:', getCartTotal(), 'دينار');
}

// اختبار وظيفة getCartTotal
function testGetCartTotal() {
    console.log('\n💰 اختبار حساب إجمالي السلة...');
    
    if (typeof getCartTotal !== 'function') {
        console.log('❌ وظيفة getCartTotal غير موجودة');
        return;
    }
    
    const total = getCartTotal();
    const expectedTotal = (25000 * 2) + (15000 * 1); // 65000
    
    console.log('الإجمالي المحسوب:', total);
    console.log('الإجمالي المتوقع:', expectedTotal);
    
    if (total === expectedTotal) {
        console.log('✅ حساب الإجمالي صحيح');
    } else {
        console.log('❌ خطأ في حساب الإجمالي');
    }
}

// اختبار وظيفة proceedToCheckout
function testProceedToCheckout() {
    console.log('\n🛒 اختبار وظيفة proceedToCheckout...');
    
    if (typeof proceedToCheckout !== 'function') {
        console.log('❌ وظيفة proceedToCheckout غير موجودة');
        return;
    }
    
    console.log('✅ وظيفة proceedToCheckout موجودة');
    
    try {
        console.log('محاولة تشغيل proceedToCheckout...');
        proceedToCheckout();
        
        // التحقق من ظهور النموذج
        setTimeout(() => {
            const modal = document.getElementById('checkoutModal');
            if (modal) {
                console.log('✅ تم عرض نموذج إتمام الطلب');
                console.log('محتوى النموذج:', modal.querySelector('.checkout-header h3')?.textContent);
            } else {
                console.log('❌ لم يتم عرض نموذج إتمام الطلب');
            }
        }, 500);
        
    } catch (error) {
        console.error('❌ خطأ في proceedToCheckout:', error);
    }
}

// اختبار نموذج تأكيد الطلب
function testCheckoutModal() {
    console.log('\n📋 اختبار نموذج تأكيد الطلب...');
    
    // فتح النموذج
    if (typeof showCheckoutConfirmation === 'function') {
        showCheckoutConfirmation();
        
        setTimeout(() => {
            const modal = document.getElementById('checkoutModal');
            if (modal) {
                console.log('✅ النموذج مفتوح');
                
                // فحص العناصر
                const header = modal.querySelector('.checkout-header h3');
                const summaryItems = modal.querySelectorAll('.summary-item');
                const confirmBtn = modal.querySelector('.confirm-order-btn');
                const cancelBtn = modal.querySelector('.cancel-btn');
                
                console.log('عنوان النموذج:', header?.textContent);
                console.log('عدد عناصر الملخص:', summaryItems.length);
                console.log('زر التأكيد موجود:', !!confirmBtn);
                console.log('زر الإلغاء موجود:', !!cancelBtn);
                
                // اختبار إغلاق النموذج
                if (typeof closeCheckoutModal === 'function') {
                    setTimeout(() => {
                        console.log('اختبار إغلاق النموذج...');
                        closeCheckoutModal();
                        
                        setTimeout(() => {
                            const modalAfterClose = document.getElementById('checkoutModal');
                            if (!modalAfterClose) {
                                console.log('✅ تم إغلاق النموذج بنجاح');
                            } else {
                                console.log('❌ فشل في إغلاق النموذج');
                            }
                        }, 100);
                    }, 2000);
                }
            } else {
                console.log('❌ فشل في فتح النموذج');
            }
        }, 500);
    } else {
        console.log('❌ وظيفة showCheckoutConfirmation غير موجودة');
    }
}

// اختبار تأكيد الطلب
function testConfirmOrder() {
    console.log('\n✅ اختبار تأكيد الطلب...');
    
    if (typeof confirmOrder !== 'function') {
        console.log('❌ وظيفة confirmOrder غير موجودة');
        return;
    }
    
    console.log('✅ وظيفة confirmOrder موجودة');
    
    // حفظ حالة السلة الحالية
    const originalCart = [...window.cart];
    
    try {
        console.log('السلة قبل التأكيد:', window.cart.length, 'منتج');
        confirmOrder();
        
        // التحقق بعد فترة
        setTimeout(() => {
            console.log('السلة بعد التأكيد:', window.cart.length, 'منتج');
            
            if (window.cart.length === 0) {
                console.log('✅ تم مسح السلة بنجاح');
            } else {
                console.log('❌ لم يتم مسح السلة');
            }
        }, 3000);
        
    } catch (error) {
        console.error('❌ خطأ في confirmOrder:', error);
        // استعادة السلة في حالة الخطأ
        window.cart = originalCart;
    }
}

// اختبار CSS والتخطيط
function testCheckoutLayout() {
    console.log('\n🎨 اختبار تخطيط صفحة السلة...');
    
    // التحقق من وجود عناصر السلة
    const cartSummary = document.querySelector('.cart-summary');
    const checkoutBtn = document.querySelector('.checkout-btn');
    
    if (cartSummary) {
        const style = window.getComputedStyle(cartSummary);
        console.log('✅ cart-summary موجود');
        console.log('Position:', style.position);
        console.log('Margin:', style.margin);
        console.log('Max-width:', style.maxWidth);
    } else {
        console.log('❌ cart-summary غير موجود');
    }
    
    if (checkoutBtn) {
        console.log('✅ checkout-btn موجود');
        console.log('نص الزر:', checkoutBtn.textContent);
    } else {
        console.log('❌ checkout-btn غير موجود');
    }
}

// تشغيل جميع الاختبارات
function runAllCheckoutTests() {
    console.log('🚀 بدء جميع اختبارات إتمام الطلب...');
    
    setupTestData();
    testGetCartTotal();
    testProceedToCheckout();
    testCheckoutLayout();
    
    // اختبارات متقدمة بعد فترة
    setTimeout(() => {
        testCheckoutModal();
    }, 2000);
    
    console.log('\n✅ انتهت جميع الاختبارات');
}

// تشغيل الاختبارات
runAllCheckoutTests();

console.log('\n📋 الاختبارات المتاحة:');
console.log('- setupTestData() - إعداد بيانات الاختبار');
console.log('- testGetCartTotal() - اختبار حساب الإجمالي');
console.log('- testProceedToCheckout() - اختبار وظيفة إتمام الطلب');
console.log('- testCheckoutModal() - اختبار نموذج التأكيد');
console.log('- testConfirmOrder() - اختبار تأكيد الطلب');
console.log('- testCheckoutLayout() - اختبار التخطيط');
console.log('- runAllCheckoutTests() - تشغيل جميع الاختبارات');
