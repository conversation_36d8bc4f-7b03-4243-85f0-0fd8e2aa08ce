# 📍 مسار APK - دليل كار

## 🎯 **المسار الدقيق لـ APK**

### 📱 **بعد البناء بنجاح، ستجد APK في:**

```
/Applications/XAMPP/xamppfiles/htdocs/dalila/dalilkMobileApp/android/app/build/outputs/apk/debug/app-debug.apk
```

---

## 🚀 **طرق إنشاء APK (مرتبة حسب السهولة)**

### **1. الطريقة الأسرع: PWABuilder ⚡**

#### الخطوات:
1. انتقل إلى: https://www.pwabuilder.com
2. أدخل URL: `https://dalilakauto.com/web-preview.html`
3. اضغط "Start"
4. اختر "Android"
5. اضغط "Download"

#### مسار التحميل:
```
~/Downloads/dalila-car-app.apk
```
أو مجلد التحميلات الافتراضي في متصفحك

---

### **2. الطريقة المتقدمة: Android Studio 🔧**

#### المتطلبات:
```bash
# تثبيت Java (مطلوب)
brew install openjdk@11

# تعيين JAVA_HOME
export JAVA_HOME=/usr/local/opt/openjdk@11
```

#### الخطوات:
```bash
# الانتقال للمشروع
cd /Applications/XAMPP/xamppfiles/htdocs/dalila/dalilkMobileApp

# فتح Android Studio
npx cap open android
```

#### في Android Studio:
1. `Build` → `Build Bundle(s) / APK(s)` → `Build APK(s)`
2. انتظر انتهاء البناء
3. اضغط "locate" في الإشعار

#### مسار APK:
```
/Applications/XAMPP/xamppfiles/htdocs/dalila/dalilkMobileApp/android/app/build/outputs/apk/debug/app-debug.apk
```

---

### **3. الطريقة المباشرة: Command Line 💻**

#### بعد تثبيت Java:
```bash
cd /Applications/XAMPP/xamppfiles/htdocs/dalila/dalilkMobileApp/android

# إعطاء صلاحيات
chmod +x gradlew

# بناء APK
./gradlew assembleDebug
```

#### مسار APK:
```
./app/build/outputs/apk/debug/app-debug.apk
```

---

## 🔍 **البحث عن APK**

### **إذا لم تجد APK، استخدم:**

```bash
# البحث في مجلد المشروع
find /Applications/XAMPP/xamppfiles/htdocs/dalila/dalilkMobileApp -name "*.apk" -type f

# البحث في النظام كاملاً
find / -name "*dalila*.apk" -type f 2>/dev/null

# البحث في مجلد التحميلات
ls -la ~/Downloads/*.apk
```

---

## 📱 **تثبيت APK**

### **على الجهاز:**
```bash
# تفعيل Developer Options و USB Debugging
adb install /path/to/app-debug.apk
```

### **على المحاكي:**
- سحب APK إلى نافذة المحاكي
- أو استخدام: `adb install app-debug.apk`

---

## 🛠️ **حل مشكلة Java**

### **تثبيت Java على macOS:**
```bash
# باستخدام Homebrew
brew install openjdk@11

# تعيين متغير البيئة
echo 'export JAVA_HOME=/usr/local/opt/openjdk@11' >> ~/.zshrc
source ~/.zshrc

# التحقق من التثبيت
java -version
```

### **تثبيت Java على Ubuntu:**
```bash
sudo apt update
sudo apt install openjdk-11-jdk

# تعيين JAVA_HOME
echo 'export JAVA_HOME=/usr/lib/jvm/java-11-openjdk-amd64' >> ~/.bashrc
source ~/.bashrc
```

---

## 📋 **حالة المشروع الحالية**

### **✅ جاهز:**
- مجلد Android مُهيأ
- ملفات Capacitor كاملة
- تكوين صحيح

### **❌ مطلوب:**
- تثبيت Java JDK 11+
- تشغيل أمر البناء

---

## 🎯 **التوصية الحالية**

**للحصول على APK فوراً:**

استخدم **PWABuilder** - لا يتطلب تثبيت Java أو Android Studio:

1. https://www.pwabuilder.com
2. أدخل: `https://dalilakauto.com/web-preview.html`
3. Download APK

**ستحصل على APK جاهز في أقل من 5 دقائق!** 🚀

---

## 📞 **إذا واجهت مشاكل**

### **APK غير موجود:**
- تأكد من اكتمال عملية البناء
- تحقق من وجود أخطاء في البناء
- استخدم PWABuilder كبديل

### **Java مفقود:**
- ثبت OpenJDK 11
- تأكد من تعيين JAVA_HOME
- أعد تشغيل Terminal

### **Android Studio لا يفتح:**
- تأكد من تثبيت Android Studio
- تأكد من تثبيت Android SDK
- جرب فتح المجلد يدوياً

---

## 🎉 **النتيجة المتوقعة**

**بعد البناء الناجح:**
- حجم APK: ~5-10 MB
- اسم الملف: `app-debug.apk`
- جاهز للتثبيت والاختبار

**مسار APK النهائي:**
```
/Applications/XAMPP/xamppfiles/htdocs/dalila/dalilkMobileApp/android/app/build/outputs/apk/debug/app-debug.apk
```
