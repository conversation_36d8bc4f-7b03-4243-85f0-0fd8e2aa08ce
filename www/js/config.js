// ⚙️ دليل كار - إعدادات التطبيق
const AppConfig = {
    // إعدادات API
    api: {
        baseUrl: 'https://dalilakauto.com/api/v1',
        timeout: 10000,
        retryAttempts: 3,
        retryDelay: 1000,
        cacheTTL: {
            products: 600000,      // 10 دقائق
            categories: 3600000,   // ساعة واحدة
            brands: 3600000,       // ساعة واحدة
            settings: 7200000      // ساعتان
        }
    },

    // إعدادات التطبيق
    app: {
        name: 'دليل كار',
        version: '1.1.0',
        description: 'متجر شامل لقطع غيار السيارات',
        author: 'دليل كار',
        supportEmail: '<EMAIL>',
        supportPhone: '+************'
    },

    // إعدادات PWA
    pwa: {
        enableServiceWorker: true,
        enablePushNotifications: false,
        enableBackgroundSync: true,
        enableOfflineMode: true,
        cacheStrategy: 'networkFirst' // networkFirst, cacheFirst, staleWhileRevalidate
    },

    // إعدادات واجهة المستخدم
    ui: {
        theme: {
            primaryColor: '#667eea',
            secondaryColor: '#764ba2',
            accentColor: '#10b981',
            errorColor: '#ef4444',
            warningColor: '#f59e0b',
            infoColor: '#3b82f6'
        },
        animations: {
            enabled: true,
            duration: 300,
            easing: 'ease-out'
        },
        layout: {
            maxWidth: '1200px',
            containerPadding: '16px',
            borderRadius: '8px'
        }
    },

    // إعدادات الأداء
    performance: {
        lazyLoading: true,
        imageOptimization: true,
        bundleCompression: true,
        preloadCriticalResources: true,
        deferNonCriticalCSS: true
    },

    // إعدادات التحليلات
    analytics: {
        enabled: false,
        trackPageViews: true,
        trackUserInteractions: true,
        trackErrors: true,
        trackPerformance: true
    },

    // إعدادات الأمان
    security: {
        enableCSP: true,
        enableHTTPS: true,
        sanitizeInputs: true,
        validateForms: true,
        preventXSS: true
    },

    // إعدادات التخزين المحلي
    storage: {
        prefix: 'dalila_',
        encryption: false,
        compression: true,
        maxSize: '10MB',
        cleanupInterval: 86400000 // 24 ساعة
    },

    // إعدادات الإشعارات
    notifications: {
        enabled: true,
        position: 'top-right',
        duration: 5000,
        maxVisible: 3,
        showProgress: true
    },

    // إعدادات البحث
    search: {
        minQueryLength: 2,
        debounceDelay: 300,
        maxResults: 50,
        enableAutoComplete: true,
        enableSearchHistory: true
    },

    // إعدادات السلة
    cart: {
        persistToLocalStorage: true,
        maxItems: 100,
        enableGuestCheckout: true,
        sessionTimeout: 3600000 // ساعة واحدة
    },

    // إعدادات الصور
    images: {
        placeholder: '/images/placeholder.jpg',
        lazyLoading: true,
        webpSupport: true,
        compressionQuality: 0.8,
        maxWidth: 1200,
        maxHeight: 800
    },

    // إعدادات التطوير
    development: {
        enableDebugMode: false,
        enableConsoleLogging: true,
        enablePerformanceMonitoring: false,
        enableErrorReporting: true
    },

    // إعدادات الإنتاج
    production: {
        enableMinification: true,
        enableCompression: true,
        enableCaching: true,
        enableCDN: false
    },

    // إعدادات اللغة والمنطقة
    localization: {
        defaultLanguage: 'ar',
        supportedLanguages: ['ar', 'en'],
        direction: 'rtl',
        currency: 'SAR',
        currencySymbol: 'ريال',
        dateFormat: 'DD/MM/YYYY',
        timeFormat: '24h'
    },

    // إعدادات الشبكة
    network: {
        enableOfflineDetection: true,
        enableRetryOnFailure: true,
        enableRequestQueue: true,
        enableBackgroundSync: true,
        maxConcurrentRequests: 6
    },

    // إعدادات الوصولية
    accessibility: {
        enableKeyboardNavigation: true,
        enableScreenReaderSupport: true,
        enableHighContrast: false,
        enableFocusIndicators: true,
        minTouchTargetSize: 44
    },

    // إعدادات SEO
    seo: {
        enableMetaTags: true,
        enableStructuredData: true,
        enableSitemap: true,
        enableRobotsTxt: true,
        enableCanonicalUrls: true
    },

    // روابط مهمة
    links: {
        website: 'https://dalilakauto.com',
        api: 'https://dalilakauto.com/api/v1',
        cdn: 'https://dalilakauto.com/storage',
        support: 'https://dalilakauto.com/support',
        privacy: 'https://dalilakauto.com/privacy',
        terms: 'https://dalilakauto.com/terms'
    },

    // معلومات الاتصال
    contact: {
        email: '<EMAIL>',
        phone: '+************',
        whatsapp: '+************',
        address: 'المملكة العربية السعودية',
        workingHours: 'السبت - الخميس: 8:00 ص - 6:00 م'
    },

    // إعدادات وسائل التواصل الاجتماعي
    social: {
        facebook: '',
        twitter: '',
        instagram: '',
        youtube: '',
        linkedin: '',
        snapchat: '',
        tiktok: ''
    },

    // إعدادات الدفع (للمستقبل)
    payment: {
        enabledMethods: ['cash', 'bank_transfer'],
        currency: 'SAR',
        taxRate: 0.15, // 15% VAT
        shippingCost: 25,
        freeShippingThreshold: 500
    },

    // إعدادات الشحن (للمستقبل)
    shipping: {
        enabledMethods: ['standard', 'express'],
        standardDeliveryDays: '3-5',
        expressDeliveryDays: '1-2',
        enableTrackingNumbers: true,
        enableDeliveryNotifications: true
    }
};

// تجميد الكائن لمنع التعديل
Object.freeze(AppConfig);

// تصدير للاستخدام العام
window.AppConfig = AppConfig;

// دالة مساعدة للحصول على قيمة من التكوين
window.getConfig = function(path, defaultValue = null) {
    const keys = path.split('.');
    let value = AppConfig;
    
    for (const key of keys) {
        if (value && typeof value === 'object' && key in value) {
            value = value[key];
        } else {
            return defaultValue;
        }
    }
    
    return value;
};

// دالة للتحقق من تمكين ميزة معينة
window.isFeatureEnabled = function(feature) {
    return getConfig(feature, false) === true;
};

// دالة للحصول على معلومات البيئة
window.getEnvironment = function() {
    const hostname = window.location.hostname;
    
    if (hostname === 'localhost' || hostname === '127.0.0.1') {
        return 'development';
    } else if (hostname.includes('staging') || hostname.includes('test')) {
        return 'staging';
    } else {
        return 'production';
    }
};

// دالة للحصول على إعدادات البيئة الحالية
window.getEnvironmentConfig = function() {
    const env = getEnvironment();
    return getConfig(env, {});
};

console.log('⚙️ تم تحميل إعدادات التطبيق بنجاح');
