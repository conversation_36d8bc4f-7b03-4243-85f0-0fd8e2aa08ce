// 🌐 دليل كار - خدمة API محسنة
class ApiService {
    constructor() {
        this.baseUrl = 'https://dalilakauto.com/api/v1';
        this.cache = new Map();
        this.requestQueue = [];
        this.isOnline = navigator.onLine;
        this.retryAttempts = 3;
        this.retryDelay = 1000;
        
        this.setupNetworkMonitoring();
    }

    // إعداد مراقبة الشبكة
    setupNetworkMonitoring() {
        window.addEventListener('online', () => {
            this.isOnline = true;
            this.processQueue();
        });

        window.addEventListener('offline', () => {
            this.isOnline = false;
        });
    }

    // طلب API أساسي مع معالجة الأخطاء والتخزين المؤقت
    async request(endpoint, options = {}) {
        const url = `${this.baseUrl}${endpoint}`;
        const cacheKey = this.generateCacheKey(endpoint, options);
        
        // التحقق من الكاش أولاً
        if (this.cache.has(cacheKey) && !options.forceRefresh) {
            const cached = this.cache.get(cacheKey);
            if (this.isCacheValid(cached)) {
                console.log('📱 استخدام البيانات المحفوظة:', endpoint);
                return cached.data;
            }
        }

        // إذا كان غير متصل، أضف للطابور
        if (!this.isOnline) {
            return this.handleOfflineRequest(endpoint, options);
        }

        try {
            const response = await this.makeRequest(url, options);
            const data = await response.json();
            
            // تخزين في الكاش
            this.cache.set(cacheKey, {
                data,
                timestamp: Date.now(),
                ttl: options.cacheTTL || 300000 // 5 دقائق افتراضي
            });
            
            return data;
        } catch (error) {
            console.error('❌ خطأ في API:', error);
            return this.handleApiError(error, cacheKey);
        }
    }

    // تنفيذ الطلب مع إعادة المحاولة
    async makeRequest(url, options, attempt = 1) {
        const requestOptions = {
            method: options.method || 'GET',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
                ...options.headers
            },
            ...options
        };

        if (options.body && typeof options.body === 'object') {
            requestOptions.body = JSON.stringify(options.body);
        }

        try {
            const response = await fetch(url, requestOptions);
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            return response;
        } catch (error) {
            if (attempt < this.retryAttempts) {
                console.log(`🔄 إعادة المحاولة ${attempt + 1}/${this.retryAttempts} لـ ${url}`);
                await this.delay(this.retryDelay * attempt);
                return this.makeRequest(url, options, attempt + 1);
            }
            throw error;
        }
    }

    // معالجة الطلبات في وضع عدم الاتصال
    handleOfflineRequest(endpoint, options) {
        const cacheKey = this.generateCacheKey(endpoint, options);
        
        // إضافة للطابور
        this.requestQueue.push({ endpoint, options, cacheKey });
        
        // إرجاع البيانات المحفوظة إن وجدت
        if (this.cache.has(cacheKey)) {
            const cached = this.cache.get(cacheKey);
            return Promise.resolve(cached.data);
        }
        
        // إرجاع بيانات افتراضية
        return Promise.resolve({
            error: true,
            message: 'البيانات غير متوفرة في وضع عدم الاتصال',
            offline: true,
            data: []
        });
    }

    // معالجة أخطاء API
    handleApiError(error, cacheKey) {
        // محاولة إرجاع البيانات المحفوظة
        if (this.cache.has(cacheKey)) {
            const cached = this.cache.get(cacheKey);
            console.log('📱 استخدام البيانات المحفوظة بسبب الخطأ');
            return cached.data;
        }
        
        // إرجاع خطأ منظم
        return {
            error: true,
            message: this.getErrorMessage(error),
            code: error.code || 'UNKNOWN_ERROR',
            data: []
        };
    }

    // معالجة طابور الطلبات عند عودة الاتصال
    async processQueue() {
        console.log('🔄 معالجة طابور الطلبات المؤجلة...');
        
        while (this.requestQueue.length > 0) {
            const { endpoint, options } = this.requestQueue.shift();
            try {
                await this.request(endpoint, { ...options, forceRefresh: true });
            } catch (error) {
                console.error('❌ فشل في معالجة طلب مؤجل:', error);
            }
        }
    }

    // توليد مفتاح الكاش
    generateCacheKey(endpoint, options) {
        const params = options.params || {};
        const sortedParams = Object.keys(params).sort().reduce((result, key) => {
            result[key] = params[key];
            return result;
        }, {});
        
        return `${endpoint}_${JSON.stringify(sortedParams)}`;
    }

    // التحقق من صحة الكاش
    isCacheValid(cached) {
        return Date.now() - cached.timestamp < cached.ttl;
    }

    // تأخير
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // الحصول على رسالة خطأ مفهومة
    getErrorMessage(error) {
        if (error.message.includes('Failed to fetch')) {
            return 'فشل في الاتصال بالخادم';
        }
        if (error.message.includes('404')) {
            return 'البيانات المطلوبة غير موجودة';
        }
        if (error.message.includes('500')) {
            return 'خطأ في الخادم، يرجى المحاولة لاحقاً';
        }
        if (error.message.includes('403')) {
            return 'غير مسموح بالوصول لهذه البيانات';
        }
        return 'حدث خطأ غير متوقع';
    }

    // تنظيف الكاش
    clearCache() {
        this.cache.clear();
        console.log('🗑️ تم تنظيف الكاش');
    }

    // تنظيف الكاش المنتهي الصلاحية
    cleanExpiredCache() {
        const now = Date.now();
        for (const [key, value] of this.cache.entries()) {
            if (now - value.timestamp > value.ttl) {
                this.cache.delete(key);
            }
        }
    }

    // === طرق API المحددة ===

    // جلب المنتجات
    async getProducts(params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const endpoint = `/ecommerce/products${queryString ? `?${queryString}` : ''}`;
        return await this.request(endpoint, { 
            params,
            cacheTTL: 600000 // 10 دقائق
        });
    }

    // جلب منتج واحد
    async getProduct(id) {
        return await this.request(`/ecommerce/products/${id}`, {
            cacheTTL: 1800000 // 30 دقيقة
        });
    }

    // جلب الفئات
    async getCategories() {
        return await this.request('/ecommerce/product-categories', {
            cacheTTL: 3600000 // ساعة واحدة
        });
    }

    // جلب العلامات التجارية
    async getBrands() {
        return await this.request('/ecommerce/brands', {
            cacheTTL: 3600000 // ساعة واحدة
        });
    }

    // البحث في المنتجات
    async searchProducts(query, params = {}) {
        return await this.getProducts({
            search: query,
            ...params
        });
    }

    // جلب منتجات فئة معينة
    async getCategoryProducts(categoryId, params = {}) {
        return await this.getProducts({
            category_id: categoryId,
            ...params
        });
    }

    // جلب منتجات علامة تجارية معينة
    async getBrandProducts(brandId, params = {}) {
        return await this.getProducts({
            brand_id: brandId,
            ...params
        });
    }

    // جلب المنتجات المميزة
    async getFeaturedProducts(limit = 8) {
        return await this.getProducts({
            featured: 1,
            per_page: limit
        });
    }

    // جلب المنتجات الجديدة
    async getNewProducts(limit = 8) {
        return await this.getProducts({
            sort: 'created_at',
            order: 'desc',
            per_page: limit
        });
    }

    // جلب المنتجات الأكثر مبيعاً
    async getBestSellingProducts(limit = 8) {
        return await this.getProducts({
            sort: 'sales_count',
            order: 'desc',
            per_page: limit
        });
    }

    // إرسال طلب اتصال
    async submitContactForm(data) {
        return await this.request('/contact', {
            method: 'POST',
            body: data
        });
    }

    // إرسال طلب عرض سعر
    async submitQuoteRequest(data) {
        return await this.request('/quote-request', {
            method: 'POST',
            body: data
        });
    }

    // جلب إعدادات التطبيق
    async getAppSettings() {
        return await this.request('/settings', {
            cacheTTL: 7200000 // ساعتان
        });
    }

    // تحديث إحصائيات الاستخدام
    async updateUsageStats(data) {
        return await this.request('/usage-stats', {
            method: 'POST',
            body: data
        });
    }
}

// إنشاء instance واحد للاستخدام العام
const apiService = new ApiService();

// تنظيف الكاش كل 10 دقائق
setInterval(() => {
    apiService.cleanExpiredCache();
}, 600000);

// تصدير للاستخدام العام
window.apiService = apiService;

export default apiService;
