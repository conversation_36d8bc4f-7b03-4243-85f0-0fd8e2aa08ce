// 🚀 دليل كار - التطبيق الرئيسي
class DalilaCarApp {
    constructor() {
        this.apiBaseUrl = 'https://dalilakauto.com/api/v1';
        this.isOnline = navigator.onLine;
        this.cache = new Map();
        this.init();
    }

    // تهيئة التطبيق
    async init() {
        console.log('🚀 تهيئة تطبيق دليل كار...');
        
        // تسجيل Service Worker
        await this.registerServiceWorker();
        
        // إعداد مراقبة الاتصال
        this.setupNetworkMonitoring();
        
        // إعداد معالجة الأخطاء
        this.setupErrorHandling();
        
        // تحميل البيانات الأساسية
        await this.loadInitialData();
        
        // إعداد واجهة المستخدم
        this.setupUI();
        
        console.log('✅ تم تهيئة التطبيق بنجاح');
    }

    // تسجيل Service Worker
    async registerServiceWorker() {
        if ('serviceWorker' in navigator) {
            try {
                const registration = await navigator.serviceWorker.register('/sw.js');
                console.log('✅ Service Worker مسجل:', registration);
                
                // التحقق من التحديثات
                registration.addEventListener('updatefound', () => {
                    console.log('🔄 تحديث جديد متوفر');
                    this.showUpdateNotification();
                });
            } catch (error) {
                console.error('❌ فشل تسجيل Service Worker:', error);
            }
        }
    }

    // إعداد مراقبة الاتصال
    setupNetworkMonitoring() {
        window.addEventListener('online', () => {
            this.isOnline = true;
            this.showNotification('تم استعادة الاتصال بالإنترنت', 'success');
            this.syncOfflineData();
        });

        window.addEventListener('offline', () => {
            this.isOnline = false;
            this.showNotification('لا يوجد اتصال بالإنترنت - سيتم العمل في الوضع المحدود', 'warning');
        });
    }

    // إعداد معالجة الأخطاء
    setupErrorHandling() {
        window.addEventListener('error', (event) => {
            console.error('❌ خطأ في التطبيق:', event.error);
            this.logError(event.error);
        });

        window.addEventListener('unhandledrejection', (event) => {
            console.error('❌ Promise مرفوض:', event.reason);
            this.logError(event.reason);
        });
    }

    // تحميل البيانات الأساسية
    async loadInitialData() {
        try {
            const [categories, brands] = await Promise.all([
                this.fetchCategories(),
                this.fetchBrands()
            ]);

            this.cache.set('categories', categories);
            this.cache.set('brands', brands);
            
            console.log('✅ تم تحميل البيانات الأساسية');
        } catch (error) {
            console.error('❌ فشل تحميل البيانات الأساسية:', error);
        }
    }

    // إعداد واجهة المستخدم
    setupUI() {
        // إعداد التنقل
        this.setupNavigation();
        
        // إعداد البحث
        this.setupSearch();
        
        // إعداد السلة
        this.setupCart();
        
        // إعداد الإشعارات
        this.setupNotifications();
    }

    // إعداد التنقل
    setupNavigation() {
        const navLinks = document.querySelectorAll('[data-nav]');
        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const page = link.dataset.nav;
                this.navigateTo(page);
            });
        });

        // معالجة زر الرجوع
        window.addEventListener('popstate', (e) => {
            if (e.state && e.state.page) {
                this.showPage(e.state.page);
            }
        });
    }

    // التنقل إلى صفحة
    navigateTo(page) {
        history.pushState({ page }, '', `#${page}`);
        this.showPage(page);
    }

    // عرض صفحة مع تأثيرات انتقالية
    showPage(page) {
        const currentPage = document.querySelector('.page.active');
        const targetPage = document.getElementById(`page-${page}`);

        if (!targetPage) return;

        // إذا كانت نفس الصفحة، لا تفعل شيء
        if (currentPage === targetPage) return;

        // إخفاء الصفحة الحالية مع تأثير
        if (currentPage) {
            currentPage.classList.add('page-transition-exit');
            setTimeout(() => {
                currentPage.style.display = 'none';
                currentPage.classList.remove('active', 'page-transition-exit');
            }, 300);
        }

        // عرض الصفحة الجديدة مع تأثير
        setTimeout(() => {
            targetPage.style.display = 'block';
            targetPage.classList.add('active', 'page-transition-enter');

            // إزالة فئة التأثير بعد انتهاء الرسوم المتحركة
            setTimeout(() => {
                targetPage.classList.remove('page-transition-enter');
            }, 300);

            this.loadPageData(page);
            this.updateBottomNavigation(page);
            this.addScrollRevealAnimations();
        }, currentPage ? 150 : 0);
    }

    // إضافة تأثيرات الظهور عند التمرير
    addScrollRevealAnimations() {
        const elements = document.querySelectorAll('.card, .product-card, .category-card');
        elements.forEach((el, index) => {
            el.classList.add('scroll-reveal');
            setTimeout(() => {
                el.classList.add('revealed');
            }, index * 100);
        });
    }

    // تحميل بيانات الصفحة
    async loadPageData(page) {
        switch (page) {
            case 'home':
                await this.loadHomeData();
                break;
            case 'products':
                await this.loadAllProducts();
                break;
            case 'categories':
                await this.loadAllCategories();
                break;
            case 'cart':
                await this.loadCart();
                break;
            default:
                console.log('صفحة غير معروفة:', page);
        }
    }

    // تحميل جميع المنتجات
    async loadAllProducts() {
        try {
            this.showLoading('productsGrid');

            const products = await this.fetchProducts({ per_page: 50 });
            this.renderProductsGrid(products);

        } catch (error) {
            this.showError('فشل تحميل المنتجات');
        } finally {
            this.hideLoading('productsGrid');
        }
    }

    // تحميل جميع الفئات
    async loadAllCategories() {
        try {
            this.showLoading('allCategories');

            const categories = await this.fetchCategories();
            this.renderAllCategories(categories);

        } catch (error) {
            this.showError('فشل تحميل الفئات');
        } finally {
            this.hideLoading('allCategories');
        }
    }

    // تحميل السلة
    async loadCart() {
        try {
            const cart = JSON.parse(localStorage.getItem('cart') || '[]');
            this.renderCart(cart);
        } catch (error) {
            this.showError('فشل تحميل السلة');
        }
    }

    // استخدام API Service الجديد
    get api() {
        return window.apiService;
    }

    // جلب الفئات
    async fetchCategories() {
        return await this.api.getCategories();
    }

    // جلب العلامات التجارية
    async fetchBrands() {
        return await this.api.getBrands();
    }

    // جلب المنتجات
    async fetchProducts(params = {}) {
        return await this.api.getProducts(params);
    }

    // جلب المنتجات المميزة
    async fetchFeaturedProducts(limit = 8) {
        return await this.api.getFeaturedProducts(limit);
    }

    // تحميل بيانات الصفحة الرئيسية
    async loadHomeData() {
        try {
            this.showLoading('featuredProducts');
            this.showLoading('categoriesList');

            const [featuredProducts, categories] = await Promise.all([
                this.fetchFeaturedProducts(8),
                this.fetchCategories()
            ]);

            this.renderFeaturedProducts(featuredProducts);
            this.renderCategories(categories);

        } catch (error) {
            this.showError('فشل تحميل بيانات الصفحة الرئيسية');
        } finally {
            this.hideLoading('featuredProducts');
            this.hideLoading('categoriesList');
        }
    }

    // عرض المنتجات المميزة
    renderFeaturedProducts(products) {
        const container = document.getElementById('featured-products');
        if (!container || !products.data) return;

        container.innerHTML = products.data.map(product => `
            <div class="card product-card" data-product-id="${product.id}">
                <img src="${product.image || '/images/placeholder.jpg'}" 
                     alt="${product.name}" 
                     class="product-image">
                <div class="card-body">
                    <h3 class="product-name">${product.name}</h3>
                    <p class="product-price">${product.price} ريال</p>
                    <button class="btn btn-primary add-to-cart" 
                            data-product-id="${product.id}">
                        إضافة للسلة
                    </button>
                </div>
            </div>
        `).join('');

        // إضافة مستمعي الأحداث
        container.querySelectorAll('.add-to-cart').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const productId = e.target.dataset.productId;
                this.addToCart(productId);
            });
        });
    }

    // عرض الفئات في الصفحة الرئيسية
    renderCategories(categories) {
        const container = document.getElementById('categoriesList');
        if (!container || !categories.data) return;

        container.innerHTML = categories.data.slice(0, 8).map(category => `
            <div class="card category-card" data-category-id="${category.id}">
                <div class="card-body text-center">
                    <div class="category-icon">${category.icon || '📦'}</div>
                    <h4 class="category-name">${category.name}</h4>
                </div>
            </div>
        `).join('');

        // إضافة مستمعي الأحداث
        container.querySelectorAll('.category-card').forEach(card => {
            card.addEventListener('click', (e) => {
                const categoryId = e.currentTarget.dataset.categoryId;
                this.showCategoryProducts(categoryId);
            });
        });
    }

    // عرض جميع الفئات
    renderAllCategories(categories) {
        const container = document.getElementById('allCategories');
        if (!container || !categories.data) return;

        container.innerHTML = categories.data.map(category => `
            <div class="card category-card" data-category-id="${category.id}">
                <div class="card-body text-center">
                    <div class="category-icon">${category.icon || '📦'}</div>
                    <h4 class="category-name">${category.name}</h4>
                    <p class="text-sm text-secondary">${category.products_count || 0} منتج</p>
                </div>
            </div>
        `).join('');

        // إضافة مستمعي الأحداث
        container.querySelectorAll('.category-card').forEach(card => {
            card.addEventListener('click', (e) => {
                const categoryId = e.currentTarget.dataset.categoryId;
                this.showCategoryProducts(categoryId);
            });
        });
    }

    // عرض شبكة المنتجات
    renderProductsGrid(products) {
        const container = document.getElementById('productsGrid');
        if (!container || !products.data) return;

        container.innerHTML = products.data.map(product => `
            <div class="card product-card" data-product-id="${product.id}">
                <img src="${product.image || '/images/placeholder.jpg'}"
                     alt="${product.name}"
                     class="product-image">
                <div class="card-body">
                    <h3 class="product-name">${product.name}</h3>
                    <p class="product-price">${product.price} ريال</p>
                    <div class="product-actions">
                        <button class="btn btn-primary add-to-cart"
                                data-product-id="${product.id}">
                            إضافة للسلة
                        </button>
                        <button class="btn btn-outline view-product"
                                data-product-id="${product.id}">
                            عرض التفاصيل
                        </button>
                    </div>
                </div>
            </div>
        `).join('');

        // إضافة مستمعي الأحداث
        this.attachProductEventListeners(container);
    }

    // عرض السلة
    renderCart(cartItems) {
        const container = document.getElementById('cartItems');
        if (!container) return;

        if (cartItems.length === 0) {
            container.innerHTML = `
                <div class="text-center">
                    <h3>السلة فارغة</h3>
                    <p>لم تقم بإضافة أي منتجات للسلة بعد</p>
                    <button class="btn btn-primary" data-nav="products">تصفح المنتجات</button>
                </div>
            `;
            return;
        }

        // هنا يمكن إضافة منطق عرض عناصر السلة
        container.innerHTML = `
            <div class="cart-summary">
                <h3>عناصر السلة (${cartItems.length})</h3>
                <!-- سيتم إضافة عرض تفصيلي للسلة لاحقاً -->
            </div>
        `;
    }

    // إضافة للسلة
    async addToCart(productId, quantity = 1) {
        try {
            // الحصول على السلة الحالية
            let cart = JSON.parse(localStorage.getItem('cart') || '[]');
            
            // البحث عن المنتج في السلة
            const existingItem = cart.find(item => item.id === productId);
            
            if (existingItem) {
                existingItem.quantity += quantity;
            } else {
                cart.push({ id: productId, quantity });
            }
            
            // حفظ السلة
            localStorage.setItem('cart', JSON.stringify(cart));
            
            // تحديث عداد السلة
            this.updateCartCount();
            
            this.showNotification('تم إضافة المنتج للسلة', 'success');
            
        } catch (error) {
            console.error('❌ فشل إضافة المنتج للسلة:', error);
            this.showNotification('فشل إضافة المنتج للسلة', 'error');
        }
    }

    // تحديث عداد السلة
    updateCartCount() {
        const cart = JSON.parse(localStorage.getItem('cart') || '[]');
        const count = cart.reduce((total, item) => total + item.quantity, 0);
        
        const cartBadge = document.getElementById('cart-count');
        if (cartBadge) {
            cartBadge.textContent = count;
            cartBadge.style.display = count > 0 ? 'block' : 'none';
        }
    }

    // عرض حالة التحميل
    showLoading(containerId) {
        const container = document.getElementById(containerId);
        if (container) {
            container.innerHTML = `
                <div class="loading-spinner">
                    <div class="spinner"></div>
                    <p>جاري التحميل...</p>
                </div>
            `;
        }
    }

    // إخفاء حالة التحميل
    hideLoading(containerId) {
        const container = document.getElementById(containerId);
        if (container) {
            const spinner = container.querySelector('.loading-spinner');
            if (spinner) {
                spinner.remove();
            }
        }
    }

    // عرض رسالة خطأ
    showError(message) {
        this.showNotification(message, 'error');
    }

    // عرض إشعار محسن مع تأثيرات
    showNotification(message, type = 'info', duration = 5000) {
        const container = document.getElementById('notifications-container') || this.createNotificationsContainer();

        const notification = document.createElement('div');
        notification.className = `notification notification-${type} notification-enter`;

        // إضافة أيقونة حسب النوع
        const icon = this.getNotificationIcon(type);

        notification.innerHTML = `
            <div class="notification-content">
                <div class="notification-icon">${icon}</div>
                <div class="notification-message">${message}</div>
            </div>
            <button class="notification-close" aria-label="إغلاق الإشعار">&times;</button>
            <div class="notification-progress"></div>
        `;

        container.appendChild(notification);

        // تأثير الدخول
        requestAnimationFrame(() => {
            notification.classList.add('notification-show');
        });

        // شريط التقدم
        const progressBar = notification.querySelector('.notification-progress');
        if (duration > 0) {
            progressBar.style.animation = `notificationProgress ${duration}ms linear`;
        }

        // إزالة الإشعار تلقائياً
        const autoRemove = setTimeout(() => {
            this.removeNotification(notification);
        }, duration);

        // إزالة عند النقر على زر الإغلاق
        notification.querySelector('.notification-close').addEventListener('click', () => {
            clearTimeout(autoRemove);
            this.removeNotification(notification);
        });

        // إزالة عند النقر على الإشعار (اختياري)
        notification.addEventListener('click', (e) => {
            if (!e.target.closest('.notification-close')) {
                clearTimeout(autoRemove);
                this.removeNotification(notification);
            }
        });

        return notification;
    }

    // إنشاء حاوي الإشعارات
    createNotificationsContainer() {
        const container = document.createElement('div');
        container.id = 'notifications-container';
        container.className = 'notifications-container';
        document.body.appendChild(container);
        return container;
    }

    // الحصول على أيقونة الإشعار
    getNotificationIcon(type) {
        const icons = {
            success: '✅',
            error: '❌',
            warning: '⚠️',
            info: 'ℹ️'
        };
        return icons[type] || icons.info;
    }

    // إزالة الإشعار مع تأثير
    removeNotification(notification) {
        notification.classList.add('notification-exit');
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }

    // إعداد البحث
    setupSearch() {
        const searchInput = document.getElementById('search-input');
        const searchButton = document.getElementById('search-button');

        if (searchInput && searchButton) {
            searchButton.addEventListener('click', () => {
                this.performSearch(searchInput.value);
            });

            searchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.performSearch(searchInput.value);
                }
            });
        }
    }

    // تنفيذ البحث
    async performSearch(query) {
        if (!query.trim()) return;

        try {
            // التنقل إلى صفحة البحث
            this.navigateTo('search');

            this.showLoading('searchResults');

            const results = await this.api.searchProducts(query, {
                per_page: 20
            });

            this.renderSearchResults(results);

        } catch (error) {
            this.showError('فشل في البحث');
        } finally {
            this.hideLoading('searchResults');
        }
    }

    // عرض نتائج البحث
    renderSearchResults(results) {
        const container = document.getElementById('search-results');
        if (!container) return;

        if (!results.data || results.data.length === 0) {
            container.innerHTML = '<p class="text-center">لم يتم العثور على نتائج</p>';
            return;
        }

        container.innerHTML = results.data.map(product => `
            <div class="card product-card">
                <img src="${product.image || '/images/placeholder.jpg'}" 
                     alt="${product.name}">
                <div class="card-body">
                    <h3>${product.name}</h3>
                    <p class="price">${product.price} ريال</p>
                    <button class="btn btn-primary add-to-cart" 
                            data-product-id="${product.id}">
                        إضافة للسلة
                    </button>
                </div>
            </div>
        `).join('');
    }

    // إعداد السلة
    setupCart() {
        this.updateCartCount();
    }

    // إعداد الإشعارات
    setupNotifications() {
        // إنشاء حاوي الإشعارات
        if (!document.getElementById('notifications-container')) {
            const container = document.createElement('div');
            container.id = 'notifications-container';
            container.className = 'notifications-container';
            document.body.appendChild(container);
        }
    }

    // مزامنة البيانات المحدودة
    async syncOfflineData() {
        // مزامنة السلة والطلبات المحفوظة محلياً
        console.log('🔄 مزامنة البيانات المحدودة...');
    }

    // عرض إشعار التحديث
    showUpdateNotification() {
        const notification = document.createElement('div');
        notification.className = 'update-notification';
        notification.innerHTML = `
            <p>تحديث جديد متوفر للتطبيق</p>
            <button id="update-app">تحديث الآن</button>
            <button id="dismiss-update">لاحقاً</button>
        `;

        document.body.appendChild(notification);

        document.getElementById('update-app').addEventListener('click', () => {
            window.location.reload();
        });

        document.getElementById('dismiss-update').addEventListener('click', () => {
            notification.remove();
        });
    }

    // إضافة مستمعي أحداث المنتجات مع تأثيرات
    attachProductEventListeners(container) {
        // أزرار إضافة للسلة
        container.querySelectorAll('.add-to-cart').forEach(btn => {
            this.addRippleEffect(btn);
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const productId = e.target.dataset.productId;
                this.addToCartWithAnimation(productId, btn);
            });
        });

        // أزرار عرض التفاصيل
        container.querySelectorAll('.view-product').forEach(btn => {
            this.addRippleEffect(btn);
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const productId = e.target.dataset.productId;
                this.showProductDetails(productId);
            });
        });

        // النقر على كارت المنتج
        container.querySelectorAll('.product-card').forEach(card => {
            card.addEventListener('click', (e) => {
                if (!e.target.closest('button')) {
                    const productId = card.dataset.productId;
                    this.showProductDetails(productId);
                }
            });

            // تأثير hover
            card.addEventListener('mouseenter', () => {
                card.classList.add('hover-lift');
            });

            card.addEventListener('mouseleave', () => {
                card.classList.remove('hover-lift');
            });
        });
    }

    // إضافة تأثير ripple للأزرار
    addRippleEffect(button) {
        button.classList.add('btn-ripple');

        button.addEventListener('click', function(e) {
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;

            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = x + 'px';
            ripple.style.top = y + 'px';
            ripple.classList.add('ripple');

            this.appendChild(ripple);

            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    }

    // إضافة للسلة مع تأثيرات بصرية
    async addToCartWithAnimation(productId, button) {
        try {
            // تأثير تحميل على الزر
            const originalText = button.textContent;
            button.textContent = 'جاري الإضافة...';
            button.disabled = true;
            button.classList.add('loading');

            // إضافة للسلة
            await this.addToCart(productId);

            // تأثير نجاح
            button.textContent = 'تم الإضافة ✓';
            button.classList.remove('loading');
            button.classList.add('success');

            // إعادة النص الأصلي بعد ثانيتين
            setTimeout(() => {
                button.textContent = originalText;
                button.disabled = false;
                button.classList.remove('success');
            }, 2000);

        } catch (error) {
            // تأثير خطأ
            button.textContent = 'فشل الإضافة ✗';
            button.classList.remove('loading');
            button.classList.add('error');

            setTimeout(() => {
                button.textContent = originalText;
                button.disabled = false;
                button.classList.remove('error');
            }, 2000);
        }
    }

    // عرض منتجات فئة معينة
    async showCategoryProducts(categoryId) {
        try {
            this.navigateTo('products');
            this.showLoading('productsGrid');

            const products = await this.api.getCategoryProducts(categoryId);
            this.renderProductsGrid(products);

        } catch (error) {
            this.showError('فشل تحميل منتجات الفئة');
        } finally {
            this.hideLoading('productsGrid');
        }
    }

    // عرض تفاصيل المنتج
    async showProductDetails(productId) {
        try {
            const product = await this.api.getProduct(productId);
            this.showProductModal(product);
        } catch (error) {
            this.showError('فشل تحميل تفاصيل المنتج');
        }
    }

    // عرض نافذة تفاصيل المنتج
    showProductModal(product) {
        // إنشاء نافذة منبثقة لعرض تفاصيل المنتج
        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.innerHTML = `
            <div class="modal">
                <div class="modal-header">
                    <h3 class="modal-title">${product.name}</h3>
                    <button class="modal-close">&times;</button>
                </div>
                <div class="modal-body">
                    <img src="${product.image || '/images/placeholder.jpg'}"
                         alt="${product.name}"
                         style="width: 100%; max-height: 300px; object-fit: cover; border-radius: 8px; margin-bottom: 16px;">
                    <p class="product-price" style="font-size: 24px; font-weight: bold; color: var(--primary-600); margin-bottom: 16px;">
                        ${product.price} ريال
                    </p>
                    <p>${product.description || 'لا يوجد وصف متاح'}</p>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-primary add-to-cart-modal" data-product-id="${product.id}">
                        إضافة للسلة
                    </button>
                    <button class="btn btn-secondary close-modal">إغلاق</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // إضافة مستمعي الأحداث
        modal.querySelector('.modal-close').addEventListener('click', () => {
            modal.remove();
        });

        modal.querySelector('.close-modal').addEventListener('click', () => {
            modal.remove();
        });

        modal.querySelector('.add-to-cart-modal').addEventListener('click', (e) => {
            const productId = e.target.dataset.productId;
            this.addToCart(productId);
            modal.remove();
        });

        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.remove();
            }
        });
    }

    // تحديث التنقل السفلي
    updateBottomNavigation(activePage) {
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
            if (item.dataset.nav === activePage) {
                item.classList.add('active');
            }
        });
    }

    // تسجيل الأخطاء
    logError(error) {
        // يمكن إرسال الأخطاء لخدمة مراقبة الأخطاء
        console.error('📝 تسجيل خطأ:', error);

        // إرسال للخادم (اختياري)
        if (this.api && typeof this.api.updateUsageStats === 'function') {
            this.api.updateUsageStats({
                type: 'error',
                message: error.message,
                stack: error.stack,
                timestamp: new Date().toISOString()
            }).catch(() => {
                // تجاهل أخطاء إرسال الإحصائيات
            });
        }
    }
}

// تهيئة التطبيق عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    window.dalilaApp = new DalilaCarApp();
});

// تصدير للاستخدام العام
window.DalilaCarApp = DalilaCarApp;
