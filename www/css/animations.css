/* 🎬 دليل كار - الرسوم المتحركة والتأثيرات */

/* ===== متغيرات الرسوم المتحركة ===== */
:root {
  --animation-duration-fast: 150ms;
  --animation-duration-normal: 300ms;
  --animation-duration-slow: 500ms;
  --animation-easing: cubic-bezier(0.4, 0, 0.2, 1);
  --animation-easing-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
  --animation-easing-elastic: cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

/* ===== الرسوم المتحركة الأساسية ===== */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes scaleOut {
  from {
    opacity: 1;
    transform: scale(1);
  }
  to {
    opacity: 0;
    transform: scale(0.8);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-5px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(5px);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes wiggle {
  0%, 7% {
    transform: rotateZ(0);
  }
  15% {
    transform: rotateZ(-15deg);
  }
  20% {
    transform: rotateZ(10deg);
  }
  25% {
    transform: rotateZ(-10deg);
  }
  30% {
    transform: rotateZ(6deg);
  }
  35% {
    transform: rotateZ(-4deg);
  }
  40%, 100% {
    transform: rotateZ(0);
  }
}

@keyframes heartbeat {
  0% {
    transform: scale(1);
  }
  14% {
    transform: scale(1.3);
  }
  28% {
    transform: scale(1);
  }
  42% {
    transform: scale(1.3);
  }
  70% {
    transform: scale(1);
  }
}

/* ===== فئات الرسوم المتحركة ===== */
.animate-fade-in {
  animation: fadeIn var(--animation-duration-normal) var(--animation-easing);
}

.animate-fade-out {
  animation: fadeOut var(--animation-duration-normal) var(--animation-easing);
}

.animate-slide-in-up {
  animation: slideInUp var(--animation-duration-normal) var(--animation-easing);
}

.animate-slide-in-down {
  animation: slideInDown var(--animation-duration-normal) var(--animation-easing);
}

.animate-slide-in-right {
  animation: slideInRight var(--animation-duration-normal) var(--animation-easing);
}

.animate-slide-in-left {
  animation: slideInLeft var(--animation-duration-normal) var(--animation-easing);
}

.animate-scale-in {
  animation: scaleIn var(--animation-duration-normal) var(--animation-easing-bounce);
}

.animate-scale-out {
  animation: scaleOut var(--animation-duration-normal) var(--animation-easing);
}

.animate-bounce-in {
  animation: bounceIn var(--animation-duration-slow) var(--animation-easing-elastic);
}

.animate-shake {
  animation: shake var(--animation-duration-slow) var(--animation-easing);
}

.animate-pulse {
  animation: pulse 2s infinite;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

.animate-wiggle {
  animation: wiggle 1s var(--animation-easing);
}

.animate-heartbeat {
  animation: heartbeat 1.5s ease-in-out infinite;
}

/* ===== تأثيرات التمرير ===== */
.hover-lift {
  transition: transform var(--animation-duration-fast) var(--animation-easing);
}

.hover-lift:hover {
  transform: translateY(-4px);
}

.hover-scale {
  transition: transform var(--animation-duration-fast) var(--animation-easing);
}

.hover-scale:hover {
  transform: scale(1.05);
}

.hover-rotate {
  transition: transform var(--animation-duration-fast) var(--animation-easing);
}

.hover-rotate:hover {
  transform: rotate(5deg);
}

.hover-glow {
  transition: box-shadow var(--animation-duration-fast) var(--animation-easing);
}

.hover-glow:hover {
  box-shadow: 0 0 20px rgba(102, 126, 234, 0.4);
}

/* ===== تأثيرات التركيز ===== */
.focus-ring {
  transition: box-shadow var(--animation-duration-fast) var(--animation-easing);
}

.focus-ring:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.3);
}

/* ===== تأثيرات التحميل ===== */
.loading-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.loading-dots::after {
  content: '';
  animation: dots 1.5s infinite;
}

@keyframes dots {
  0%, 20% {
    content: '';
  }
  40% {
    content: '.';
  }
  60% {
    content: '..';
  }
  80%, 100% {
    content: '...';
  }
}

/* ===== تأثيرات الإشعارات ===== */
.notification-enter {
  animation: slideInRight var(--animation-duration-normal) var(--animation-easing);
}

.notification-exit {
  animation: slideInRight var(--animation-duration-normal) var(--animation-easing) reverse;
}

/* ===== تأثيرات النوافذ المنبثقة ===== */
.modal-enter {
  animation: modalEnter var(--animation-duration-normal) var(--animation-easing);
}

.modal-exit {
  animation: modalExit var(--animation-duration-normal) var(--animation-easing);
}

@keyframes modalEnter {
  from {
    opacity: 0;
    transform: scale(0.8) translateY(20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes modalExit {
  from {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
  to {
    opacity: 0;
    transform: scale(0.8) translateY(20px);
  }
}

/* ===== تأثيرات التبديل بين الصفحات ===== */
.page-transition-enter {
  animation: pageEnter var(--animation-duration-normal) var(--animation-easing);
}

.page-transition-exit {
  animation: pageExit var(--animation-duration-normal) var(--animation-easing);
}

@keyframes pageEnter {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pageExit {
  from {
    opacity: 1;
    transform: translateX(0);
  }
  to {
    opacity: 0;
    transform: translateX(-20px);
  }
}

/* ===== تأثيرات الأزرار ===== */
.btn-ripple {
  position: relative;
  overflow: hidden;
}

.btn-ripple::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.btn-ripple:active::before {
  width: 300px;
  height: 300px;
}

/* ===== تأثيرات التمرير ===== */
.scroll-reveal {
  opacity: 0;
  transform: translateY(30px);
  transition: opacity 0.6s var(--animation-easing), 
              transform 0.6s var(--animation-easing);
}

.scroll-reveal.revealed {
  opacity: 1;
  transform: translateY(0);
}

/* ===== تأثيرات الشبكة ===== */
.stagger-animation > * {
  opacity: 0;
  transform: translateY(20px);
  animation: staggerItem var(--animation-duration-normal) var(--animation-easing) forwards;
}

.stagger-animation > *:nth-child(1) { animation-delay: 0ms; }
.stagger-animation > *:nth-child(2) { animation-delay: 100ms; }
.stagger-animation > *:nth-child(3) { animation-delay: 200ms; }
.stagger-animation > *:nth-child(4) { animation-delay: 300ms; }
.stagger-animation > *:nth-child(5) { animation-delay: 400ms; }
.stagger-animation > *:nth-child(6) { animation-delay: 500ms; }

@keyframes staggerItem {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ===== تحسينات الأداء ===== */
.will-change-transform {
  will-change: transform;
}

.will-change-opacity {
  will-change: opacity;
}

.will-change-auto {
  will-change: auto;
}

/* ===== إعدادات تقليل الحركة ===== */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
  
  .animate-pulse,
  .animate-spin,
  .animate-heartbeat {
    animation: none !important;
  }
}
