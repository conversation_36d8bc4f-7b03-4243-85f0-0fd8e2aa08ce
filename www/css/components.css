/* 🧩 مكونات واجهة المستخدم المتقدمة */

/* ===== Header & Navigation ===== */
.app-header {
  background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
  color: var(--text-inverse);
  padding: var(--spacing-md) 0;
  box-shadow: var(--shadow-lg);
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: var(--font-size-xl);
  font-weight: 700;
}

.logo img {
  width: 40px;
  height: 40px;
  border-radius: var(--border-radius-md);
}

.header-actions {
  display: flex;
  gap: var(--spacing-md);
  align-items: center;
}

.search-container {
  position: relative;
  max-width: 400px;
  width: 100%;
}

.search-input {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  padding-left: 40px;
  border: none;
  border-radius: var(--border-radius-full);
  font-size: var(--font-size-sm);
}

.search-button {
  position: absolute;
  left: var(--spacing-sm);
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--text-tertiary);
  cursor: pointer;
}

.cart-button {
  position: relative;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  color: var(--text-inverse);
  padding: var(--spacing-sm);
  border-radius: var(--border-radius-md);
  transition: var(--transition-fast);
}

.cart-button:hover {
  background: rgba(255, 255, 255, 0.2);
}

.cart-count {
  position: absolute;
  top: -8px;
  right: -8px;
  background: var(--error-color);
  color: var(--text-inverse);
  border-radius: var(--border-radius-full);
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-xs);
  font-weight: 700;
}

/* ===== Bottom Navigation ===== */
.bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: var(--bg-primary);
  border-top: 1px solid var(--border-light);
  display: flex;
  justify-content: space-around;
  padding: var(--spacing-sm) 0;
  z-index: 100;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-xs);
  color: var(--text-tertiary);
  text-decoration: none;
  transition: var(--transition-fast);
  min-width: 60px;
}

.nav-item.active,
.nav-item:hover {
  color: var(--primary-600);
}

.nav-icon {
  font-size: 20px;
}

.nav-label {
  font-size: var(--font-size-xs);
  font-weight: 600;
}

/* ===== Product Cards ===== */
.product-card {
  transition: var(--transition-normal);
  cursor: pointer;
}

.product-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.product-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
  border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
}

.product-name {
  font-size: var(--font-size-md);
  font-weight: 600;
  margin-bottom: var(--spacing-xs);
  color: var(--text-primary);
  line-height: 1.4;
}

.product-price {
  font-size: var(--font-size-lg);
  font-weight: 700;
  color: var(--primary-600);
  margin-bottom: var(--spacing-md);
}

.product-actions {
  display: flex;
  gap: var(--spacing-sm);
}

/* ===== Category Cards ===== */
.category-card {
  text-align: center;
  transition: var(--transition-normal);
  cursor: pointer;
}

.category-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.category-icon {
  font-size: 48px;
  margin-bottom: var(--spacing-sm);
}

.category-name {
  font-size: var(--font-size-md);
  font-weight: 600;
  color: var(--text-primary);
}

/* ===== Loading States ===== */
.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-4xl);
  color: var(--text-secondary);
}

.spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--border-light);
  border-top: 3px solid var(--primary-600);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--spacing-md);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* ===== Notifications ===== */
.notifications-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
  max-width: 400px;
}

.notification {
  background: var(--bg-primary);
  border: 1px solid var(--border-medium);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-sm);
  box-shadow: var(--shadow-lg);
  display: flex;
  justify-content: space-between;
  align-items: center;
  animation: slideInRight 0.3s ease-out;
}

.notification-success {
  border-left: 4px solid var(--success-color);
}

.notification-error {
  border-left: 4px solid var(--error-color);
}

.notification-warning {
  border-left: 4px solid var(--warning-color);
}

.notification-info {
  border-left: 4px solid var(--info-color);
}

.notification-content {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  flex: 1;
}

.notification-icon {
  font-size: var(--font-size-lg);
  flex-shrink: 0;
}

.notification-message {
  flex: 1;
  font-weight: 500;
}

.notification-close {
  background: none;
  border: none;
  font-size: var(--font-size-lg);
  cursor: pointer;
  color: var(--text-tertiary);
  padding: var(--spacing-xs);
  border-radius: var(--border-radius-sm);
  transition: var(--transition-fast);
  flex-shrink: 0;
}

.notification-close:hover {
  background: rgba(0, 0, 0, 0.1);
  color: var(--text-primary);
}

.notification-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 3px;
  background: currentColor;
  opacity: 0.3;
  transform-origin: left;
}

.notification-show {
  transform: translateX(0);
  opacity: 1;
}

@keyframes notificationProgress {
  from {
    transform: scaleX(1);
  }
  to {
    transform: scaleX(0);
  }
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* ===== Modal ===== */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease-out;
}

.modal {
  background: var(--bg-primary);
  border-radius: var(--border-radius-xl);
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  animation: slideUp 0.3s ease-out;
}

.modal-header {
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-light);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-title {
  font-size: var(--font-size-xl);
  font-weight: 700;
  margin: 0;
}

.modal-close {
  background: none;
  border: none;
  font-size: var(--font-size-xl);
  cursor: pointer;
  color: var(--text-tertiary);
  padding: var(--spacing-xs);
}

.modal-body {
  padding: var(--spacing-lg);
}

.modal-footer {
  padding: var(--spacing-lg);
  border-top: 1px solid var(--border-light);
  display: flex;
  gap: var(--spacing-md);
  justify-content: flex-end;
}

/* ===== Tabs ===== */
.tabs {
  border-bottom: 1px solid var(--border-light);
}

.tab-list {
  display: flex;
  gap: 0;
  margin: 0;
  padding: 0;
  list-style: none;
}

.tab-button {
  background: none;
  border: none;
  padding: var(--spacing-md) var(--spacing-lg);
  cursor: pointer;
  font-size: var(--font-size-md);
  font-weight: 600;
  color: var(--text-secondary);
  border-bottom: 2px solid transparent;
  transition: var(--transition-fast);
}

.tab-button:hover {
  color: var(--text-primary);
}

.tab-button.active {
  color: var(--primary-600);
  border-bottom-color: var(--primary-600);
}

.tab-content {
  padding: var(--spacing-lg) 0;
}

.tab-panel {
  display: none;
}

.tab-panel.active {
  display: block;
  animation: fadeIn 0.3s ease-out;
}

/* ===== Accordion ===== */
.accordion-item {
  border: 1px solid var(--border-light);
  border-radius: var(--border-radius-md);
  margin-bottom: var(--spacing-sm);
  overflow: hidden;
}

.accordion-header {
  background: var(--bg-secondary);
  padding: var(--spacing-md);
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: var(--transition-fast);
}

.accordion-header:hover {
  background: var(--bg-tertiary);
}

.accordion-title {
  font-weight: 600;
  margin: 0;
}

.accordion-icon {
  transition: var(--transition-fast);
}

.accordion-item.active .accordion-icon {
  transform: rotate(180deg);
}

.accordion-content {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease-out;
}

.accordion-item.active .accordion-content {
  max-height: 500px;
}

.accordion-body {
  padding: var(--spacing-md);
}

/* ===== Breadcrumb ===== */
.breadcrumb {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) 0;
  font-size: var(--font-size-sm);
}

.breadcrumb-item {
  color: var(--text-secondary);
}

.breadcrumb-item:last-child {
  color: var(--text-primary);
  font-weight: 600;
}

.breadcrumb-separator {
  color: var(--text-tertiary);
}

/* ===== Badge ===== */
.badge {
  display: inline-flex;
  align-items: center;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-full);
  font-size: var(--font-size-xs);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.badge-primary {
  background: var(--primary-100);
  color: var(--primary-700);
}

.badge-success {
  background: #dcfce7;
  color: #166534;
}

.badge-warning {
  background: #fef3c7;
  color: #92400e;
}

.badge-error {
  background: #fee2e2;
  color: #991b1b;
}

/* ===== Progress Bar ===== */
.progress {
  width: 100%;
  height: 8px;
  background: var(--bg-tertiary);
  border-radius: var(--border-radius-full);
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-500), var(--primary-600));
  border-radius: var(--border-radius-full);
  transition: width 0.3s ease;
}

/* ===== Skeleton Loading ===== */
.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

.skeleton-text {
  height: 1em;
  border-radius: var(--border-radius-sm);
  margin-bottom: var(--spacing-xs);
}

.skeleton-text:last-child {
  width: 60%;
}

.skeleton-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
}

.skeleton-image {
  width: 100%;
  height: 200px;
  border-radius: var(--border-radius-md);
}

/* ===== Responsive Utilities ===== */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: var(--spacing-md);
  }
  
  .search-container {
    order: 2;
    max-width: 100%;
  }
  
  .header-actions {
    order: 1;
    width: 100%;
    justify-content: space-between;
  }
  
  .modal {
    width: 95%;
    margin: var(--spacing-md);
  }
  
  .notifications-container {
    right: var(--spacing-sm);
    left: var(--spacing-sm);
    max-width: none;
  }
}

/* ===== Loading Screen ===== */
.loading-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  color: var(--text-inverse);
}

.loading-logo {
  width: 120px;
  height: 120px;
  margin-bottom: var(--spacing-lg);
  border-radius: var(--border-radius-xl);
  animation: pulse 2s infinite;
}

.loading-text {
  font-size: var(--font-size-xl);
  font-weight: 700;
  margin-bottom: var(--spacing-xl);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid var(--text-inverse);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* ===== App Container ===== */
.app-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: var(--bg-primary);
}

.main-content {
  flex: 1;
  padding-bottom: 80px; /* Space for bottom nav */
}

.page {
  padding: var(--spacing-lg) 0;
}

.page.active {
  display: block;
}

/* ===== Hero Section ===== */
.hero-section {
  background: linear-gradient(135deg, var(--primary-50), var(--primary-100));
  padding: var(--spacing-4xl) 0;
  text-align: center;
  margin-bottom: var(--spacing-xl);
  border-radius: var(--border-radius-xl);
}

.hero-content h1 {
  font-size: var(--font-size-4xl);
  color: var(--primary-800);
  margin-bottom: var(--spacing-md);
}

.hero-content p {
  font-size: var(--font-size-lg);
  color: var(--primary-600);
  margin-bottom: var(--spacing-xl);
}

/* ===== Sections ===== */
.categories-section,
.featured-section {
  margin-bottom: var(--spacing-4xl);
}

.categories-section h2,
.featured-section h2 {
  font-size: var(--font-size-2xl);
  margin-bottom: var(--spacing-lg);
  color: var(--text-primary);
  text-align: center;
}

/* ===== Error Fallback ===== */
.error-fallback {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--bg-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.error-fallback h2 {
  color: var(--error-color);
  margin-bottom: var(--spacing-md);
}

.error-fallback p {
  color: var(--text-secondary);
  margin-bottom: var(--spacing-lg);
}

/* ===== Dark Mode Support ===== */
@media (prefers-color-scheme: dark) {
  :root {
    --text-primary: #f8fafc;
    --text-secondary: #cbd5e1;
    --text-tertiary: #94a3b8;
    --bg-primary: #0f172a;
    --bg-secondary: #1e293b;
    --bg-tertiary: #334155;
    --border-light: #334155;
    --border-medium: #475569;
    --border-dark: #64748b;
  }

  .app-header {
    background: linear-gradient(135deg, var(--primary-700), var(--primary-800));
  }

  .hero-section {
    background: linear-gradient(135deg, var(--primary-900), var(--primary-800));
  }

  .hero-content h1 {
    color: var(--primary-200);
  }

  .hero-content p {
    color: var(--primary-300);
  }

  .skeleton {
    background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
    background-size: 200% 100%;
  }
}
