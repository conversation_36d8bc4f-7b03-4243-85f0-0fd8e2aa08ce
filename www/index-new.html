<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>دليل كار - قطع غيار السيارات</title>

    <!-- PWA Manifest -->
    <link rel="manifest" href="manifest.json">

    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#667eea">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="دليل كار">
    <meta name="msapplication-TileColor" content="#667eea">

    <!-- SEO Meta Tags -->
    <meta name="description" content="دليل كار - متجر شامل لقطع غيار السيارات الكورية واليابانية بأفضل الأسعار وأعلى جودة">
    <meta name="keywords" content="قطع غيار, سيارات, دليل, متجر, هيونداي, كيا, تويوتا, نيسان, ميتسوبيشي">
    <meta name="author" content="دليل كار">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="دليل كار - قطع غيار السيارات">
    <meta property="og:description" content="متجر شامل لقطع غيار السيارات الكورية واليابانية">
    <meta property="og:image" content="https://dalilakauto.com/storage/main/general/logo.png">
    <meta property="og:url" content="https://dalilakauto.com">
    <meta property="og:type" content="website">
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="دليل كار - قطع غيار السيارات">
    <meta name="twitter:description" content="متجر شامل لقطع غيار السيارات الكورية واليابانية">
    <meta name="twitter:image" content="https://dalilakauto.com/storage/main/general/logo.png">

    <!-- Icons -->
    <link rel="apple-touch-icon" href="https://dalilakauto.com/storage/main/general/logo.png">
    <link rel="icon" type="image/png" sizes="32x32" href="https://dalilakauto.com/storage/main/general/logo.png">
    <link rel="icon" type="image/png" sizes="16x16" href="https://dalilakauto.com/storage/main/general/logo.png">
    
    <!-- Preload Critical Resources -->
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" as="style">
    <link rel="preconnect" href="https://dalilakauto.com">
    <link rel="dns-prefetch" href="https://dalilakauto.com">
    
    <!-- Load Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- Load CSS Files -->
    <link rel="stylesheet" href="css/app.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/animations.css">
</head>
<body>
    <!-- Loading Screen -->
    <div id="loadingScreen" class="loading-screen">
        <img src="https://dalilakauto.com/storage/main/general/logo.png" alt="دليل كار" class="loading-logo">
        <div class="loading-text">دليل كار</div>
        <div class="loading-spinner"></div>
    </div>

    <!-- App Container -->
    <div class="app-container" id="appContainer" style="display: none;">
        <!-- Header -->
        <header class="app-header">
            <div class="container">
                <div class="header-content">
                    <div class="logo">
                        <img src="https://dalilakauto.com/storage/main/general/logo.png" alt="دليل كار">
                        <span class="logo-text">دليل كار</span>
                    </div>
                    
                    <div class="search-container">
                        <input type="text" id="searchInput" class="search-input" placeholder="ابحث عن قطع الغيار...">
                        <button id="searchButton" class="search-button">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                <path d="m21 21-6-6m6-6a8 8 0 1 1-16 0 8 8 0 0 1 16 0"/>
                            </svg>
                        </button>
                    </div>
                    
                    <div class="header-actions">
                        <button class="cart-button" data-nav="cart">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                                <path d="m2 3h2l.4 2a4 4 0 0 0 4 3h9a4 4 0 0 0 4-3l.4-2H22"/>
                                <circle cx="9" cy="20" r="1"/>
                                <circle cx="20" cy="20" r="1"/>
                            </svg>
                            <span id="cartCount" class="cart-count" style="display: none;">0</span>
                        </button>
                    </div>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Home Page -->
            <div id="page-home" class="page active">
                <div class="container">
                    <!-- Hero Section -->
                    <section class="hero-section">
                        <div class="hero-content">
                            <h1>مرحباً بك في دليل كار</h1>
                            <p>متجرك الشامل لقطع غيار السيارات الكورية واليابانية</p>
                            <button class="btn btn-primary btn-lg" data-nav="products">تصفح المنتجات</button>
                        </div>
                    </section>

                    <!-- Categories Section -->
                    <section class="categories-section">
                        <h2>الفئات الرئيسية</h2>
                        <div id="categoriesList" class="grid grid-cols-2 md:grid-cols-4 gap-md">
                            <!-- Categories will be loaded here -->
                        </div>
                    </section>

                    <!-- Featured Products -->
                    <section class="featured-section">
                        <h2>المنتجات المميزة</h2>
                        <div id="featuredProducts" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-md">
                            <!-- Featured products will be loaded here -->
                        </div>
                    </section>
                </div>
            </div>

            <!-- Products Page -->
            <div id="page-products" class="page" style="display: none;">
                <div class="container">
                    <div class="breadcrumb">
                        <span class="breadcrumb-item">الرئيسية</span>
                        <span class="breadcrumb-separator">/</span>
                        <span class="breadcrumb-item">المنتجات</span>
                    </div>
                    
                    <h1>جميع المنتجات</h1>
                    
                    <div id="productsGrid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-md">
                        <!-- Products will be loaded here -->
                    </div>
                </div>
            </div>

            <!-- Categories Page -->
            <div id="page-categories" class="page" style="display: none;">
                <div class="container">
                    <div class="breadcrumb">
                        <span class="breadcrumb-item">الرئيسية</span>
                        <span class="breadcrumb-separator">/</span>
                        <span class="breadcrumb-item">الفئات</span>
                    </div>
                    
                    <h1>فئات المنتجات</h1>
                    
                    <div id="allCategories" class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-md">
                        <!-- All categories will be loaded here -->
                    </div>
                </div>
            </div>

            <!-- Cart Page -->
            <div id="page-cart" class="page" style="display: none;">
                <div class="container">
                    <div class="breadcrumb">
                        <span class="breadcrumb-item">الرئيسية</span>
                        <span class="breadcrumb-separator">/</span>
                        <span class="breadcrumb-item">سلة التسوق</span>
                    </div>
                    
                    <h1>سلة التسوق</h1>
                    
                    <div id="cartItems">
                        <!-- Cart items will be loaded here -->
                    </div>
                </div>
            </div>

            <!-- Search Results Page -->
            <div id="page-search" class="page" style="display: none;">
                <div class="container">
                    <div class="breadcrumb">
                        <span class="breadcrumb-item">الرئيسية</span>
                        <span class="breadcrumb-separator">/</span>
                        <span class="breadcrumb-item">نتائج البحث</span>
                    </div>
                    
                    <h1>نتائج البحث</h1>
                    
                    <div id="searchResults" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-md">
                        <!-- Search results will be loaded here -->
                    </div>
                </div>
            </div>
        </main>

        <!-- Bottom Navigation -->
        <nav class="bottom-nav">
            <a href="#home" class="nav-item active" data-nav="home">
                <div class="nav-icon">🏠</div>
                <div class="nav-label">الرئيسية</div>
            </a>
            <a href="#products" class="nav-item" data-nav="products">
                <div class="nav-icon">📦</div>
                <div class="nav-label">المنتجات</div>
            </a>
            <a href="#categories" class="nav-item" data-nav="categories">
                <div class="nav-icon">📂</div>
                <div class="nav-label">الفئات</div>
            </a>
            <a href="#cart" class="nav-item" data-nav="cart">
                <div class="nav-icon">🛒</div>
                <div class="nav-label">السلة</div>
            </a>
        </nav>
    </div>

    <!-- Error Fallback -->
    <div id="errorFallback" class="error-fallback" style="display: none;">
        <div class="container text-center">
            <h2>عذراً، حدث خطأ</h2>
            <p>يرجى تحديث الصفحة أو المحاولة لاحقاً</p>
            <button onclick="location.reload()" class="btn btn-primary">تحديث الصفحة</button>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/config.js"></script>
    <script src="js/api.js"></script>
    <script src="js/app.js"></script>
    
    <!-- Capacitor Scripts -->
    <script type="module" src="https://unpkg.com/@capacitor/core@latest/dist/capacitor.js"></script>
    
    <script>
        // Initialize app when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            // Hide loading screen after 2 seconds
            setTimeout(() => {
                const loadingScreen = document.getElementById('loadingScreen');
                const appContainer = document.getElementById('appContainer');
                
                if (loadingScreen && appContainer) {
                    loadingScreen.style.display = 'none';
                    appContainer.style.display = 'flex';
                }
            }, 2000);
        });

        // Initialize Capacitor if available
        if (window.Capacitor) {
            import('https://unpkg.com/@capacitor/app@latest/dist/esm/index.js').then(({ App }) => {
                console.log('Capacitor App plugin loaded');
            }).catch(err => {
                console.log('Capacitor not available:', err);
            });
        }
    </script>
</body>
</html>
