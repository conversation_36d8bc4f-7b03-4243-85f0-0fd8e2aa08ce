/**
 * اختبار شامل للبحث الهرمي وAPIs
 */

console.log('🔍 اختبار البحث الهرمي وAPIs...');

// إعدادات الاختبار
const API_BASE_URL = 'https://dalilakauto.com/api/v1';

// اختبار APIs مباشرة
async function testVehiclePartsAPIs() {
    console.log('\n🧪 اختبار APIs البحث الهرمي...');
    
    const endpoints = [
        { name: 'الماركات', url: '/vehicle-parts/makes' },
        { name: 'الفئات الجذرية', url: '/vehicle-parts/root-categories' },
        { name: 'البحث', url: '/vehicle-parts/search' },
        { name: 'القطع الشائعة', url: '/vehicle-parts/popular' }
    ];
    
    for (const endpoint of endpoints) {
        try {
            console.log(`\n🔍 اختبار ${endpoint.name}...`);
            const response = await fetch(`${API_BASE_URL}${endpoint.url}`);
            
            console.log(`📡 الحالة: ${response.status}`);
            console.log(`📊 النوع: ${response.headers.get('content-type')}`);
            
            if (response.ok) {
                const data = await response.json();
                console.log(`✅ ${endpoint.name} يعمل`);
                console.log(`📋 البيانات:`, {
                    success: data.success,
                    dataCount: data.data?.length || 0,
                    message: data.message
                });
                
                // عرض عينة من البيانات
                if (data.data && data.data.length > 0) {
                    console.log(`📄 عينة من البيانات:`, data.data.slice(0, 3));
                }
            } else {
                console.log(`❌ ${endpoint.name} لا يعمل`);
                const errorText = await response.text();
                console.log(`📝 رسالة الخطأ:`, errorText.substring(0, 200));
            }
            
        } catch (error) {
            console.error(`❌ خطأ في ${endpoint.name}:`, error.message);
        }
    }
}

// اختبار التدفق الهرمي الكامل
async function testHierarchicalFlow() {
    console.log('\n🔄 اختبار التدفق الهرمي الكامل...');
    
    try {
        // 1. تحميل الماركات
        console.log('\n1️⃣ تحميل الماركات...');
        const makesResponse = await fetch(`${API_BASE_URL}/vehicle-parts/makes`);
        
        if (!makesResponse.ok) {
            throw new Error(`فشل في تحميل الماركات: ${makesResponse.status}`);
        }
        
        const makesData = await makesResponse.json();
        console.log(`✅ تم تحميل ${makesData.data?.length || 0} ماركة`);
        
        if (makesData.data && makesData.data.length > 0) {
            const firstMake = makesData.data[0];
            console.log(`📋 أول ماركة: ${firstMake.name} (ID: ${firstMake.id})`);
            
            // 2. تحميل الموديلات للماركة الأولى
            console.log(`\n2️⃣ تحميل الموديلات للماركة: ${firstMake.name}...`);
            const modelsResponse = await fetch(`${API_BASE_URL}/vehicle-parts/makes/${firstMake.id}/models`);
            
            if (modelsResponse.ok) {
                const modelsData = await modelsResponse.json();
                console.log(`✅ تم تحميل ${modelsData.data?.length || 0} موديل`);
                
                if (modelsData.data && modelsData.data.length > 0) {
                    const firstModel = modelsData.data[0];
                    console.log(`📋 أول موديل: ${firstModel.name} (ID: ${firstModel.id})`);
                    
                    // 3. تحميل السنوات للموديل الأول
                    console.log(`\n3️⃣ تحميل السنوات للموديل: ${firstModel.name}...`);
                    const yearsResponse = await fetch(`${API_BASE_URL}/vehicle-parts/models/${firstModel.id}/years`);
                    
                    if (yearsResponse.ok) {
                        const yearsData = await yearsResponse.json();
                        console.log(`✅ تم تحميل ${yearsData.data?.length || 0} سنة`);
                        
                        if (yearsData.data && yearsData.data.length > 0) {
                            console.log(`📋 السنوات المتاحة:`, yearsData.data.slice(0, 5).map(y => y.name));
                        }
                    } else {
                        console.log(`⚠️ فشل في تحميل السنوات: ${yearsResponse.status}`);
                    }
                }
            } else {
                console.log(`⚠️ فشل في تحميل الموديلات: ${modelsResponse.status}`);
            }
        }
        
    } catch (error) {
        console.error('❌ خطأ في التدفق الهرمي:', error.message);
    }
}

// اختبار البحث عن القطع
async function testPartsSearch() {
    console.log('\n🔍 اختبار البحث عن القطع...');
    
    const searchQueries = [
        'شمعة',
        'فلتر',
        'بوشة',
        'محرك',
        'كهرباء'
    ];
    
    for (const query of searchQueries) {
        try {
            console.log(`\n🔍 البحث عن: "${query}"`);
            
            // البحث في المنتجات العادية
            const productsResponse = await fetch(`${API_BASE_URL}/ecommerce/products?search=${encodeURIComponent(query)}&per_page=5`);
            
            if (productsResponse.ok) {
                const productsData = await productsResponse.json();
                const resultsCount = productsData.data?.length || 0;
                console.log(`✅ تم العثور على ${resultsCount} منتج`);
                
                if (resultsCount > 0) {
                    console.log(`📋 أول منتج: ${productsData.data[0].name}`);
                }
            } else {
                console.log(`❌ فشل البحث: ${productsResponse.status}`);
            }
            
        } catch (error) {
            console.error(`❌ خطأ في البحث عن "${query}":`, error.message);
        }
    }
}

// اختبار وظائف التطبيق
function testApplicationFunctions() {
    console.log('\n🔧 اختبار وظائف التطبيق...');
    
    const functions = [
        'initializeVehicleFinder',
        'loadVehicleMakes',
        'loadVehicleModels', 
        'loadVehicleYears',
        'onPartTypeChange',
        'onBrandChange',
        'onModelChange',
        'onYearChange',
        'searchVehicleParts',
        'updateSearchButton',
        'testVehiclePartsAPIs'
    ];
    
    functions.forEach(funcName => {
        if (typeof window[funcName] === 'function') {
            console.log(`✅ ${funcName} موجودة`);
        } else {
            console.log(`❌ ${funcName} غير موجودة`);
        }
    });
}

// اختبار العناصر في الصفحة
function testPageElements() {
    console.log('\n📋 اختبار عناصر الصفحة...');
    
    const elements = [
        'vehicleFinder',
        'partTypeSelect',
        'brandSelect',
        'modelSelect',
        'yearSelect',
        'searchPartsBtn',
        'vehicleFinderResults'
    ];
    
    elements.forEach(elementId => {
        const element = document.getElementById(elementId);
        if (element) {
            console.log(`✅ ${elementId} موجود`);
            if (element.tagName === 'SELECT') {
                console.log(`   - عدد الخيارات: ${element.options.length}`);
                console.log(`   - معطل: ${element.disabled}`);
            }
        } else {
            console.log(`❌ ${elementId} غير موجود`);
        }
    });
}

// محاكاة تفاعل المستخدم
async function simulateUserInteraction() {
    console.log('\n👤 محاكاة تفاعل المستخدم...');
    
    const partTypeSelect = document.getElementById('partTypeSelect');
    const brandSelect = document.getElementById('brandSelect');
    
    if (partTypeSelect) {
        console.log('1️⃣ اختيار نوع القطعة...');
        partTypeSelect.value = '1'; // ادوات صدر
        
        if (typeof onPartTypeChange === 'function') {
            await onPartTypeChange();
            
            setTimeout(() => {
                console.log('2️⃣ فحص تحديث العلامات التجارية...');
                console.log(`   - معطل: ${brandSelect.disabled}`);
                console.log(`   - عدد الخيارات: ${brandSelect.options.length}`);
                
                if (!brandSelect.disabled && brandSelect.options.length > 1) {
                    console.log('3️⃣ اختيار علامة تجارية...');
                    brandSelect.selectedIndex = 1;
                    
                    if (typeof onBrandChange === 'function') {
                        onBrandChange();
                    }
                }
            }, 1000);
        }
    }
}

// تشغيل جميع الاختبارات
async function runAllTests() {
    console.log('🚀 بدء جميع اختبارات البحث الهرمي...');
    
    // اختبار العناصر والوظائف
    testPageElements();
    testApplicationFunctions();
    
    // اختبار APIs
    await testVehiclePartsAPIs();
    await testHierarchicalFlow();
    await testPartsSearch();
    
    // محاكاة التفاعل
    await simulateUserInteraction();
    
    console.log('\n✅ انتهت جميع الاختبارات');
    console.log('\n📊 ملخص النتائج:');
    console.log('- تم اختبار APIs البحث الهرمي');
    console.log('- تم اختبار التدفق الكامل للبحث');
    console.log('- تم اختبار وظائف التطبيق');
    console.log('- تم محاكاة تفاعل المستخدم');
}

// تشغيل الاختبارات
runAllTests();

console.log('\n📋 الاختبارات المتاحة:');
console.log('- testVehiclePartsAPIs() - اختبار APIs');
console.log('- testHierarchicalFlow() - اختبار التدفق الهرمي');
console.log('- testPartsSearch() - اختبار البحث عن القطع');
console.log('- testApplicationFunctions() - اختبار وظائف التطبيق');
console.log('- testPageElements() - اختبار عناصر الصفحة');
console.log('- simulateUserInteraction() - محاكاة تفاعل المستخدم');
console.log('- runAllTests() - تشغيل جميع الاختبارات');
