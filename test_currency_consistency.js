/**
 * اختبار توحيد العملة في التطبيق
 */

console.log('💰 اختبار توحيد العملة...');

// فحص API للتأكد من العملة
async function checkAPICurrency() {
    console.log('\n🔍 فحص عملة API...');
    
    try {
        const response = await fetch('https://dalilakauto.com/api/v1/ecommerce/products?page=1&per_page=3');
        const data = await response.json();
        
        if (data.data && data.data.length > 0) {
            const firstProduct = data.data[0];
            console.log('✅ API متاح');
            console.log('اسم المنتج:', firstProduct.name);
            console.log('السعر الخام:', firstProduct.price);
            console.log('السعر المنسق:', firstProduct.price_formatted);
            
            // استخراج العملة من السعر المنسق
            const currencyMatch = firstProduct.price_formatted?.match(/[A-Z]{3}/);
            const currency = currencyMatch ? currencyMatch[0] : 'غير محدد';
            console.log('العملة من API:', currency);
            
            return currency;
        } else {
            console.log('❌ لا توجد منتجات في API');
            return null;
        }
    } catch (error) {
        console.error('❌ خطأ في الاتصال بـ API:', error.message);
        return null;
    }
}

// فحص عرض العملة في التطبيق
function checkAppCurrencyDisplay() {
    console.log('\n📱 فحص عرض العملة في التطبيق...');
    
    // فحص النصوص الثابتة
    const bodyText = document.body.innerHTML;
    
    const rialCount = (bodyText.match(/ريال/g) || []).length;
    const dinarCount = (bodyText.match(/دينار/g) || []).length;
    const iqdCount = (bodyText.match(/IQD/g) || []).length;
    
    console.log('عدد مرات ظهور "ريال":', rialCount);
    console.log('عدد مرات ظهور "دينار":', dinarCount);
    console.log('عدد مرات ظهور "IQD":', iqdCount);
    
    // فحص عناصر محددة
    const totalPriceElement = document.querySelector('.total-price');
    if (totalPriceElement) {
        console.log('نص إجمالي السعر:', totalPriceElement.textContent);
    }
    
    // فحص فلاتر الأسعار
    const priceFilter = document.getElementById('priceFilter');
    if (priceFilter) {
        const options = Array.from(priceFilter.options).map(opt => opt.textContent);
        console.log('خيارات فلتر الأسعار:', options);
    }
    
    return {
        rial: rialCount,
        dinar: dinarCount,
        iqd: iqdCount
    };
}

// فحص وظائف تنسيق الأسعار
function checkPriceFormattingFunctions() {
    console.log('\n🔧 فحص وظائف تنسيق الأسعار...');
    
    // اختبار وظيفة formatPrice
    if (typeof formatPrice === 'function') {
        console.log('✅ وظيفة formatPrice موجودة');
        
        const testPrice = 25000;
        const formattedPrice = formatPrice(testPrice);
        console.log(`تنسيق السعر ${testPrice}:`, formattedPrice);
    } else {
        console.log('❌ وظيفة formatPrice غير موجودة');
    }
    
    // اختبار وظيفة generatePriceHTML
    if (typeof generatePriceHTML === 'function') {
        console.log('✅ وظيفة generatePriceHTML موجودة');
        
        const testProduct = {
            price: 25000,
            price_formatted: '25000 IQD'
        };
        
        const priceHTML = generatePriceHTML(testProduct);
        console.log('HTML السعر:', priceHTML);
    } else {
        console.log('❌ وظيفة generatePriceHTML غير موجودة');
    }
}

// اختبار شامل
async function runCurrencyTest() {
    console.log('🧪 بدء اختبار شامل للعملة...');
    
    // فحص API
    const apiCurrency = await checkAPICurrency();
    
    // فحص التطبيق
    const appCurrencies = checkAppCurrencyDisplay();
    
    // فحص الوظائف
    checkPriceFormattingFunctions();
    
    // تقرير النتائج
    console.log('\n📊 تقرير النتائج:');
    console.log('=================');
    console.log('عملة API:', apiCurrency || 'غير محدد');
    console.log('العملات في التطبيق:', appCurrencies);
    
    // التوصيات
    console.log('\n💡 التوصيات:');
    if (apiCurrency === 'IQD') {
        console.log('✅ API يستخدم الدينار العراقي (IQD) بشكل صحيح');
        
        if (appCurrencies.rial > 0) {
            console.log('⚠️ يوجد', appCurrencies.rial, 'مكان يعرض "ريال" - يجب تغييرها لـ "دينار"');
        }
        
        if (appCurrencies.dinar > appCurrencies.rial) {
            console.log('✅ معظم التطبيق يستخدم "دينار" بشكل صحيح');
        }
    }
    
    console.log('\n🎯 الخلاصة:');
    console.log('العملة الصحيحة هي: الدينار العراقي (IQD)');
    console.log('يجب توحيد جميع عروض الأسعار لتستخدم "دينار" أو "IQD"');
}

// تشغيل الاختبار
runCurrencyTest();

console.log('\n📋 الاختبارات المتاحة:');
console.log('- checkAPICurrency() - فحص عملة API');
console.log('- checkAppCurrencyDisplay() - فحص عرض العملة في التطبيق');
console.log('- checkPriceFormattingFunctions() - فحص وظائف تنسيق الأسعار');
console.log('- runCurrencyTest() - اختبار شامل');
