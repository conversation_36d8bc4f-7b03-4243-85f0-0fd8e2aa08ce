#!/bin/bash

# Start local server for Dalila Car App
echo "🚀 بدء تشغيل خادم دليل كار المحلي..."

# Check if http-server is installed
if ! command -v http-server &> /dev/null; then
    echo "📦 تثبيت http-server..."
    npm install -g http-server
fi

# Start server
echo "🌐 تشغيل الخادم على المنفذ 8000..."
echo "📱 يمكنك الوصول للتطبيق على: http://localhost:8000"
echo "🔗 أو استخدم IP الجهاز للوصول من الهاتف"

# Get local IP
LOCAL_IP=$(ifconfig | grep "inet " | grep -v 127.0.0.1 | awk '{print $2}' | head -1)
if [ ! -z "$LOCAL_IP" ]; then
    echo "📲 للوصول من الهاتف: http://$LOCAL_IP:8000"
fi

echo ""
echo "⚠️  للإيقاف: اضغط Ctrl+C"
echo ""

# Start the server
http-server . -p 8000 -c-1 --cors
