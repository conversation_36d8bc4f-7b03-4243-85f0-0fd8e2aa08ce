# 📱 دليل إنشاء APK لتطبيق دليل كار

## 🎯 نظرة عامة
هذا الدليل يوضح كيفية تحويل تطبيق دليل كار PWA إلى APK للأندرويد.

## 📋 المتطلبات المسبقة

### الأساسية:
- Node.js (الإصدار 16 أو أحدث)
- npm أو yarn
- Git

### لطريقة Capacitor:
- Android Studio
- Android SDK (API Level 21+)
- Java Development Kit (JDK 11+)
- Gradle

## 🚀 طرق إنشاء APK

### الطريقة الأولى: Capacitor (موصى بها للمطورين)

#### 1. تثبيت المتطلبات:
```bash
# تثبيت Capacitor CLI عالمياً
npm install -g @capacitor/cli

# تثبيت التبعيات
npm install
```

#### 2. تشغيل سكريبت البناء:
```bash
node build-apk.js
```

#### 3. فتح Android Studio:
```bash
npx cap open android
```

#### 4. بناء APK في Android Studio:
1. اختر `Build` من القائمة العلوية
2. اختر `Build Bundle(s) / APK(s)`
3. اختر `Build APK(s)`
4. انتظر حتى انتهاء البناء
5. ستجد APK في: `android/app/build/outputs/apk/debug/`

### الطريقة الثانية: PWABuilder (أسهل للمبتدئين)

#### 1. زيارة PWABuilder:
انتقل إلى: https://www.pwabuilder.com

#### 2. إدخال URL:
أدخل: `https://dalilakauto.com/web-preview.html`

#### 3. تحليل التطبيق:
اضغط على "Start" وانتظر التحليل

#### 4. تحميل APK:
1. اختر "Android" من الخيارات
2. اختر "Download"
3. ستحصل على APK جاهز للتثبيت

### الطريقة الثالثة: Cordova (بديل)

#### 1. تثبيت Cordova:
```bash
npm install -g cordova
```

#### 2. إنشاء مشروع Cordova:
```bash
cordova create dalila-car com.dalilakauto.app "دليل كار"
cd dalila-car
```

#### 3. نسخ الملفات:
```bash
cp ../web-preview.html www/index.html
cp ../manifest.json www/
cp ../sw.js www/
```

#### 4. إضافة منصة Android:
```bash
cordova platform add android
```

#### 5. بناء APK:
```bash
cordova build android
```

## 📁 هيكل الملفات

```
dalila-car-app/
├── web-preview.html          # الملف الرئيسي للتطبيق
├── manifest.json             # PWA Manifest
├── sw.js                     # Service Worker
├── capacitor.config.json     # تكوين Capacitor
├── package.json              # تبعيات Node.js
├── build-apk.js             # سكريبت البناء
├── pwa-builder-config.json   # تكوين PWABuilder
└── android/                  # مجلد Android (ينشأ تلقائياً)
    └── app/
        └── build/
            └── outputs/
                └── apk/
                    └── debug/
                        └── app-debug.apk
```

## 🔧 تخصيص التطبيق

### تغيير الأيقونة:
1. أنشئ أيقونات بأحجام مختلفة (192x192, 512x512)
2. ضعها في مجلد `android/app/src/main/res/`
3. حدث `manifest.json`

### تغيير اسم التطبيق:
1. حدث `capacitor.config.json`:
```json
{
  "appName": "الاسم الجديد"
}
```

### تغيير معرف التطبيق:
1. حدث `capacitor.config.json`:
```json
{
  "appId": "com.yourcompany.newapp"
}
```

## 🛠️ حل المشاكل الشائعة

### مشكلة: "Command not found: npx"
**الحل:**
```bash
npm install -g npx
```

### مشكلة: "Android SDK not found"
**الحل:**
1. ثبت Android Studio
2. افتح SDK Manager
3. ثبت Android SDK Platform-Tools
4. أضف SDK إلى PATH

### مشكلة: "Java not found"
**الحل:**
```bash
# تثبيت OpenJDK 11
sudo apt install openjdk-11-jdk  # Ubuntu/Debian
brew install openjdk@11          # macOS
```

### مشكلة: "Gradle build failed"
**الحل:**
1. نظف المشروع:
```bash
cd android
./gradlew clean
```
2. أعد البناء:
```bash
./gradlew assembleDebug
```

## 📱 اختبار APK

### على الجهاز:
1. فعل "Developer Options"
2. فعل "USB Debugging"
3. ثبت APK:
```bash
adb install app-debug.apk
```

### على المحاكي:
1. افتح Android Studio
2. اختر "AVD Manager"
3. أنشئ محاكي جديد
4. اسحب APK إلى المحاكي

## 🚀 نشر التطبيق

### Google Play Store:
1. أنشئ حساب مطور
2. وقع APK بمفتاح إنتاج
3. ارفع إلى Play Console
4. املأ معلومات التطبيق
5. انشر التطبيق

### التوزيع المباشر:
1. وقع APK
2. ارفع إلى خادم
3. شارك رابط التحميل

## 📊 إحصائيات التطبيق

- **الحجم**: ~5-10 MB
- **الحد الأدنى لـ Android**: API Level 21 (Android 5.0)
- **الأذونات**: الإنترنت، الكاميرا، التخزين
- **اللغات**: العربية (RTL)

## 🔒 الأمان

### التوقيع:
```bash
# إنشاء مفتاح توقيع
keytool -genkey -v -keystore dalila-release-key.keystore -alias dalila -keyalg RSA -keysize 2048 -validity 10000

# توقيع APK
jarsigner -verbose -sigalg SHA1withRSA -digestalg SHA1 -keystore dalila-release-key.keystore app-unsigned.apk dalila
```

### ProGuard (تصغير الكود):
أضف إلى `android/app/build.gradle`:
```gradle
android {
    buildTypes {
        release {
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
}
```

## 📞 الدعم

إذا واجهت مشاكل:
1. تحقق من الـ logs: `adb logcat`
2. راجع الوثائق: https://capacitorjs.com/docs
3. ابحث في المجتمع: https://github.com/ionic-team/capacitor

## 🎉 تهانينا!

الآن لديك APK جاهز لتطبيق دليل كار! 🚗📱
