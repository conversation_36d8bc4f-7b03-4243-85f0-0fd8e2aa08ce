#!/bin/bash

# 📱 Script تلقائي لبناء APK تطبيق دليلك أوتو
# مع الشعار الاحترافي الجديد

echo "🚀 بدء بناء APK لتطبيق دليلك أوتو..."
echo "📱 مع الشعار الاحترافي الجديد"
echo "=================================="

# التحقق من Node.js
if ! command -v node &> /dev/null; then
    echo "❌ Node.js غير مثبت. يرجى تثبيته أولاً."
    exit 1
fi

echo "✅ Node.js version: $(node --version)"

# التحقق من npm
if ! command -v npm &> /dev/null; then
    echo "❌ npm غير مثبت."
    exit 1
fi

echo "✅ npm version: $(npm --version)"

# التحقق من Java
if ! command -v java &> /dev/null; then
    echo "❌ Java غير مثبت. يرجى تثبيت JDK 11+."
    exit 1
fi

echo "✅ Java version: $(java --version | head -n 1)"

# التحقق من Android SDK
if [ -z "$ANDROID_HOME" ]; then
    echo "⚠️  ANDROID_HOME غير معد. يرجى تعيينه."
    echo "مثال: export ANDROID_HOME=\$HOME/Library/Android/sdk"
fi

echo ""
echo "📦 تثبيت التبعيات..."
npm install

if [ $? -ne 0 ]; then
    echo "❌ فشل في تثبيت التبعيات"
    exit 1
fi

echo "✅ تم تثبيت التبعيات بنجاح"

# التحقق من وجود Capacitor
if ! command -v cap &> /dev/null; then
    echo "📱 تثبيت Capacitor CLI..."
    npm install -g @capacitor/cli
fi

echo ""
echo "🔧 إعداد Capacitor..."

# تهيئة Capacitor إذا لم يكن مهيأ
if [ ! -f "capacitor.config.ts" ] && [ ! -f "capacitor.config.js" ]; then
    echo "🔧 تهيئة Capacitor..."
    npx cap init "دليلك أوتو - قطع غيار السيارات" "com.dalilakauto.app"
fi

# إضافة منصة Android إذا لم تكن موجودة
if [ ! -d "android" ]; then
    echo "📱 إضافة منصة Android..."
    npx cap add android
    
    if [ $? -ne 0 ]; then
        echo "❌ فشل في إضافة منصة Android"
        exit 1
    fi
    
    echo "✅ تم إضافة منصة Android بنجاح"
fi

echo ""
echo "📋 نسخ ملفات الويب..."
npx cap copy android

if [ $? -ne 0 ]; then
    echo "❌ فشل في نسخ الملفات"
    exit 1
fi

echo "✅ تم نسخ الملفات بنجاح"

echo ""
echo "🔄 مزامنة المشروع..."
npx cap sync android

if [ $? -ne 0 ]; then
    echo "❌ فشل في مزامنة المشروع"
    exit 1
fi

echo "✅ تم مزامنة المشروع بنجاح"

echo ""
echo "🎨 تحديث الأيقونات..."

# إنشاء مجلد الأيقونات إذا لم يكن موجود
mkdir -p android/app/src/main/res/mipmap-hdpi
mkdir -p android/app/src/main/res/mipmap-mdpi
mkdir -p android/app/src/main/res/mipmap-xhdpi
mkdir -p android/app/src/main/res/mipmap-xxhdpi
mkdir -p android/app/src/main/res/mipmap-xxxhdpi

echo "✅ تم إعداد مجلدات الأيقونات"

echo ""
echo "🏗️  بناء المشروع..."

# الانتقال لمجلد Android
cd android

# تنظيف المشروع
echo "🧹 تنظيف المشروع..."
./gradlew clean

# بناء APK
echo "🔨 بناء APK..."
./gradlew assembleDebug

if [ $? -ne 0 ]; then
    echo "❌ فشل في بناء APK"
    exit 1
fi

# العودة للمجلد الرئيسي
cd ..

echo ""
echo "🎉 تم بناء APK بنجاح!"
echo "📍 مكان الملف: android/app/build/outputs/apk/debug/app-debug.apk"

# التحقق من وجود الملف
if [ -f "android/app/build/outputs/apk/debug/app-debug.apk" ]; then
    APK_SIZE=$(du -h "android/app/build/outputs/apk/debug/app-debug.apk" | cut -f1)
    echo "📏 حجم APK: $APK_SIZE"
    echo ""
    echo "✅ APK جاهز للتثبيت والاختبار!"
    echo ""
    echo "📱 لتثبيت APK على جهاز متصل:"
    echo "adb install android/app/build/outputs/apk/debug/app-debug.apk"
    echo ""
    echo "🔧 لفتح المشروع في Android Studio:"
    echo "npx cap open android"
    echo ""
    echo "🏗️  لبناء Release APK (للنشر):"
    echo "cd android && ./gradlew assembleRelease"
else
    echo "❌ لم يتم العثور على APK. تحقق من الأخطاء أعلاه."
    exit 1
fi

echo ""
echo "🌟 تطبيق دليلك أوتو جاهز مع الشعار الاحترافي الجديد!"
echo "=================================="
