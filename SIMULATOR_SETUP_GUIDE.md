# 📱 دليل تشغيل التطبيق على السيميوليتر

## 🎯 الخيارات المتاحة لمشاهدة التطبيق

### 1. 📱 iOS Simulator (الأسهل)

#### المتطلبات:
- Xcode مثبت
- iOS Simulator

#### خطوات التشغيل:
```bash
# 1. إصلاح CocoaPods
cd dalilkMobileApp/ios
pod install

# 2. العودة للمجلد الرئيسي
cd ..

# 3. تشغيل التطبيق
npx react-native run-ios
```

#### إذا واجهت مشاكل:
```bash
# تنظيف الكاش
npx react-native clean
cd ios && xcodebuild clean && cd ..

# إعادة تثبيت pods
cd ios && pod deintegrate && pod install && cd ..

# تشغيل مرة أخرى
npx react-native run-ios
```

---

### 2. 🤖 Android Emulator

#### المتطلبات:
- Android Studio مثبت
- Android SDK
- Android Emulator

#### إعداد البيئة:
```bash
# 1. تثبيت Android Studio
# تحميل من: https://developer.android.com/studio

# 2. إعداد متغيرات البيئة
export ANDROID_HOME=$HOME/Library/Android/sdk
export PATH=$PATH:$ANDROID_HOME/emulator
export PATH=$PATH:$ANDROID_HOME/tools
export PATH=$PATH:$ANDROID_HOME/tools/bin
export PATH=$PATH:$ANDROID_HOME/platform-tools

# 3. إنشاء AVD (Android Virtual Device)
# من Android Studio > AVD Manager > Create Virtual Device
```

#### تشغيل التطبيق:
```bash
# 1. تشغيل الإيميوليتر
emulator -avd YOUR_AVD_NAME

# 2. تشغيل التطبيق
npx react-native run-android
```

---

### 3. 🌐 نسخة ويب للمعاينة (الأسرع)

#### إنشاء نسخة ويب سريعة:
```bash
# 1. تثبيت React Native Web
npm install react-native-web react-dom

# 2. تثبيت Webpack
npm install --save-dev webpack webpack-cli webpack-dev-server
npm install --save-dev html-webpack-plugin babel-loader

# 3. تشغيل النسخة الويب
npm run web
```

---

## 🚀 الطريقة الأسرع للمعاينة

### استخدام Expo Go (موصى به):

```bash
# 1. تثبيت Expo CLI
npm install -g @expo/cli

# 2. إنشاء مشروع Expo جديد
npx create-expo-app DalilkApp --template blank

# 3. نسخ الكود
# انسخ ملفات src/ إلى المشروع الجديد

# 4. تشغيل التطبيق
cd DalilkApp
npx expo start
```

#### مميزات Expo:
- ✅ تشغيل فوري على الهاتف
- ✅ QR Code للمعاينة
- ✅ Hot Reload
- ✅ لا يحتاج إعداد معقد

---

## 📱 معاينة التطبيق الحالي

### الشاشات المتاحة:
1. **شاشة البداية** - عرض المنتجات
2. **شاشة تسجيل الدخول** - نظام المصادقة
3. **شاشة المنتجات** - عرض 30 منتج
4. **شاشة الفئات** - عرض 868 فئة
5. **شاشة باحث قطع الغيار** - 35 ماركة
6. **شاشة سلة التسوق** - إدارة المشتريات
7. **شاشة الطلبات** - تتبع الطلبات

### البيانات المتاحة:
- ✅ 30 منتج قطع غيار
- ✅ 868 فئة منتج
- ✅ 16 علامة تجارية
- ✅ 35 ماركة مركبة
- ✅ نظام مصادقة كامل

---

## 🔧 حل المشاكل الشائعة

### مشكلة CocoaPods:
```bash
sudo gem install cocoapods
cd ios && pod install
```

### مشكلة Android SDK:
```bash
# تحديد مسار SDK
export ANDROID_HOME=/Users/<USER>/Library/Android/sdk
```

### مشكلة Metro Bundler:
```bash
npx react-native start --reset-cache
```

### مشكلة الأذونات:
```bash
chmod +x android/gradlew
```

---

## 📸 لقطات شاشة متوقعة

عند تشغيل التطبيق ستشاهد:

### 🏠 الشاشة الرئيسية:
- شريط علوي مع شعار دليل
- قائمة المنتجات مع الصور
- أزرار التنقل السفلية

### 🔐 شاشة تسجيل الدخول:
- حقول البريد الإلكتروني وكلمة المرور
- زر تسجيل الدخول
- رابط إنشاء حساب جديد

### 🛒 شاشة المنتجات:
- شبكة المنتجات مع الصور
- أسعار المنتجات
- أزرار إضافة للسلة

### 🚗 باحث قطع الغيار:
- قائمة الماركات
- فلاتر البحث
- نتائج البحث

---

## 🎯 التوصية

**للمعاينة السريعة:** استخدم Expo Go
**للتطوير:** استخدم iOS Simulator
**للاختبار الشامل:** استخدم Android Emulator

---

## 📞 الدعم

إذا واجهت أي مشاكل:
1. تأكد من تشغيل Metro Bundler: `npm start`
2. تأكد من تشغيل الخادم: `php -S 0.0.0.0:8080 -t public`
3. راجع ملفات التوثيق في المشروع
4. استخدم `--verbose` للحصول على تفاصيل أكثر

**التطبيق جاهز ويعمل! فقط يحتاج إعداد بيئة التطوير المناسبة.**
