/**
 * Service Worker for Dalila Car Auto Parts App
 * Provides offline functionality and caching
 */

const CACHE_NAME = 'dalila-car-v1.0.0';
const API_CACHE_NAME = 'dalila-api-v1.0.0';

// Files to cache for offline functionality
const STATIC_CACHE_URLS = [
    '/web-preview.html',
    '/manifest.json',
    // Add other static assets here
];

// API endpoints to cache
const API_CACHE_URLS = [
    'https://dalilakauto.com/api/v1/ecommerce/products',
    'https://dalilakauto.com/api/v1/ecommerce/product-categories',
    'https://dalilakauto.com/api/v1/ecommerce/brands',
    'https://dalilakauto.com/api/v1/vehicle-parts/makes',
    'https://dalilakauto.com/api/v1/vehicle-parts/root-categories'
];

// Install event - cache static files
self.addEventListener('install', event => {
    console.log('🔧 Service Worker: Installing...');
    
    event.waitUntil(
        caches.open(CACHE_NAME)
            .then(cache => {
                console.log('📦 Service Worker: Caching static files');
                return cache.addAll(STATIC_CACHE_URLS);
            })
            .then(() => {
                console.log('✅ Service Worker: Installation complete');
                return self.skipWaiting();
            })
            .catch(error => {
                console.error('❌ Service Worker: Installation failed', error);
            })
    );
});

// Activate event - clean up old caches
self.addEventListener('activate', event => {
    console.log('🚀 Service Worker: Activating...');
    
    event.waitUntil(
        caches.keys()
            .then(cacheNames => {
                return Promise.all(
                    cacheNames.map(cacheName => {
                        if (cacheName !== CACHE_NAME && cacheName !== API_CACHE_NAME) {
                            console.log('🗑️ Service Worker: Deleting old cache', cacheName);
                            return caches.delete(cacheName);
                        }
                    })
                );
            })
            .then(() => {
                console.log('✅ Service Worker: Activation complete');
                return self.clients.claim();
            })
    );
});

// Fetch event - handle network requests
self.addEventListener('fetch', event => {
    const { request } = event;
    const url = new URL(request.url);
    
    // Handle API requests
    if (url.origin === 'https://dalilakauto.com' && url.pathname.startsWith('/api/')) {
        event.respondWith(handleApiRequest(request));
        return;
    }
    
    // Handle static file requests
    if (request.method === 'GET') {
        event.respondWith(handleStaticRequest(request));
        return;
    }
    
    // For other requests, just fetch from network
    event.respondWith(fetch(request));
});

// Handle API requests with cache-first strategy for GET requests
async function handleApiRequest(request) {
    const cache = await caches.open(API_CACHE_NAME);
    
    if (request.method === 'GET') {
        try {
            // Try network first for fresh data
            const networkResponse = await fetch(request);
            
            if (networkResponse.ok) {
                // Cache successful responses
                cache.put(request, networkResponse.clone());
                return networkResponse;
            }
            
            // If network fails, try cache
            const cachedResponse = await cache.match(request);
            if (cachedResponse) {
                console.log('📱 Service Worker: Serving API from cache', request.url);
                return cachedResponse;
            }
            
            return networkResponse;
        } catch (error) {
            // Network error, try cache
            const cachedResponse = await cache.match(request);
            if (cachedResponse) {
                console.log('📱 Service Worker: Network failed, serving from cache', request.url);
                return cachedResponse;
            }
            
            // Return offline response
            return new Response(
                JSON.stringify({
                    error: true,
                    message: 'لا يوجد اتصال بالإنترنت',
                    offline: true
                }),
                {
                    status: 503,
                    statusText: 'Service Unavailable',
                    headers: { 'Content-Type': 'application/json' }
                }
            );
        }
    }
    
    // For non-GET requests, always try network
    return fetch(request);
}

// Handle static file requests with cache-first strategy
async function handleStaticRequest(request) {
    const cache = await caches.open(CACHE_NAME);
    
    // Try cache first
    const cachedResponse = await cache.match(request);
    if (cachedResponse) {
        return cachedResponse;
    }
    
    // If not in cache, fetch from network and cache
    try {
        const networkResponse = await fetch(request);
        if (networkResponse.ok) {
            cache.put(request, networkResponse.clone());
        }
        return networkResponse;
    } catch (error) {
        // Return offline page for navigation requests
        if (request.mode === 'navigate') {
            return cache.match('/web-preview.html');
        }
        
        // For other requests, return error
        return new Response('Offline', { status: 503 });
    }
}

// Handle background sync for offline actions
self.addEventListener('sync', event => {
    console.log('🔄 Service Worker: Background sync', event.tag);
    
    if (event.tag === 'background-sync-cart') {
        event.waitUntil(syncCartData());
    }
    
    if (event.tag === 'background-sync-orders') {
        event.waitUntil(syncOrderData());
    }
});

// Sync cart data when back online
async function syncCartData() {
    try {
        // Get pending cart actions from IndexedDB
        const pendingActions = await getPendingCartActions();
        
        for (const action of pendingActions) {
            try {
                await fetch('/api/v1/cart', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(action)
                });
                
                // Remove from pending actions
                await removePendingCartAction(action.id);
            } catch (error) {
                console.error('Failed to sync cart action:', error);
            }
        }
    } catch (error) {
        console.error('Cart sync failed:', error);
    }
}

// Sync order data when back online
async function syncOrderData() {
    try {
        // Get pending orders from IndexedDB
        const pendingOrders = await getPendingOrders();
        
        for (const order of pendingOrders) {
            try {
                await fetch('/api/v1/orders', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(order)
                });
                
                // Remove from pending orders
                await removePendingOrder(order.id);
            } catch (error) {
                console.error('Failed to sync order:', error);
            }
        }
    } catch (error) {
        console.error('Order sync failed:', error);
    }
}

// Handle push notifications
self.addEventListener('push', event => {
    console.log('📬 Service Worker: Push notification received');
    
    const options = {
        body: 'لديك إشعار جديد من دليل كار',
        icon: '/icon-192x192.png',
        badge: '/badge-72x72.png',
        vibrate: [100, 50, 100],
        data: {
            dateOfArrival: Date.now(),
            primaryKey: 1
        },
        actions: [
            {
                action: 'explore',
                title: 'عرض التفاصيل',
                icon: '/icon-explore.png'
            },
            {
                action: 'close',
                title: 'إغلاق',
                icon: '/icon-close.png'
            }
        ]
    };
    
    if (event.data) {
        const data = event.data.json();
        options.body = data.body || options.body;
        options.title = data.title || 'دليل كار';
    }
    
    event.waitUntil(
        self.registration.showNotification('دليل كار', options)
    );
});

// Handle notification clicks
self.addEventListener('notificationclick', event => {
    console.log('🔔 Service Worker: Notification clicked');
    
    event.notification.close();
    
    if (event.action === 'explore') {
        event.waitUntil(
            clients.openWindow('/web-preview.html')
        );
    }
});

// Placeholder functions for IndexedDB operations
async function getPendingCartActions() {
    // Implement IndexedDB logic here
    return [];
}

async function removePendingCartAction(id) {
    // Implement IndexedDB logic here
}

async function getPendingOrders() {
    // Implement IndexedDB logic here
    return [];
}

async function removePendingOrder(id) {
    // Implement IndexedDB logic here
}

console.log('🚀 Service Worker: Script loaded successfully');
