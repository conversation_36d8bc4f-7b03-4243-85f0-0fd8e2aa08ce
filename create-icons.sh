#!/bin/bash

echo "🎨 إنشاء أيقونات التطبيق من شعار دليلك"
echo "======================================="

# إنشاء مجلدات الأيقونات
mkdir -p android/app/src/main/res/mipmap-hdpi
mkdir -p android/app/src/main/res/mipmap-mdpi
mkdir -p android/app/src/main/res/mipmap-xhdpi
mkdir -p android/app/src/main/res/mipmap-xxhdpi
mkdir -p android/app/src/main/res/mipmap-xxxhdpi

echo "📁 تم إنشاء مجلدات الأيقونات"

# تحميل الشعار من الموقع
echo "⬇️  تحميل شعار دليلك..."
curl -o dalila-logo.png "https://dalilakauto.com/storage/main/general/logo.png"

if [ -f "dalila-logo.png" ]; then
    echo "✅ تم تحميل الشعار بنجاح"
    
    # نسخ الشعار كأيقونة للأحجام المختلفة
    cp dalila-logo.png android/app/src/main/res/mipmap-hdpi/ic_launcher.png
    cp dalila-logo.png android/app/src/main/res/mipmap-mdpi/ic_launcher.png
    cp dalila-logo.png android/app/src/main/res/mipmap-xhdpi/ic_launcher.png
    cp dalila-logo.png android/app/src/main/res/mipmap-xxhdpi/ic_launcher.png
    cp dalila-logo.png android/app/src/main/res/mipmap-xxxhdpi/ic_launcher.png
    
    # نسخ كأيقونة مدورة أيضاً
    cp dalila-logo.png android/app/src/main/res/mipmap-hdpi/ic_launcher_round.png
    cp dalila-logo.png android/app/src/main/res/mipmap-mdpi/ic_launcher_round.png
    cp dalila-logo.png android/app/src/main/res/mipmap-xhdpi/ic_launcher_round.png
    cp dalila-logo.png android/app/src/main/res/mipmap-xxhdpi/ic_launcher_round.png
    cp dalila-logo.png android/app/src/main/res/mipmap-xxxhdpi/ic_launcher_round.png
    
    echo "✅ تم نسخ الأيقونات لجميع الأحجام"
else
    echo "❌ فشل في تحميل الشعار"
    echo "💡 سيتم استخدام أيقونة افتراضية"
fi

echo ""
echo "🎯 الأيقونات جاهزة في:"
echo "   android/app/src/main/res/mipmap-*/ic_launcher.png"
echo "   android/app/src/main/res/mipmap-*/ic_launcher_round.png"

echo ""
echo "✅ جاهز لبناء APK!"
