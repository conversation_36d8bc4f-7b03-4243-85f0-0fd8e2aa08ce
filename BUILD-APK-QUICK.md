# 🚀 دليل سريع لإنشاء APK

## ✅ الطريقة السريعة (PWABuilder)

### 1. انتقل إلى PWABuilder:
```
https://www.pwabuilder.com
```

### 2. أدخل URL التطبيق:
```
https://dalilakauto.com/web-preview.html
```

### 3. اضغط "Start" وانتظر التحليل

### 4. اختر "Android" واضغط "Download"

### 5. ستحصل على APK جاهز! 🎉

---

## 🔧 الطريقة المتقدمة (Capacitor)

### المتطلبات:
- Node.js
- Android Studio
- Java JDK

### الخطوات:

#### 1. تثبيت Capacitor:
```bash
npm install -g @capacitor/cli
```

#### 2. تثبيت التبعيات:
```bash
cp package-pwa.json package.json
npm install
```

#### 3. تهيئة Capacitor:
```bash
npx cap init "دليل كار" com.dalilakauto.app
```

#### 4. إضافة منصة Android:
```bash
npx cap add android
```

#### 5. مزامنة الملفات:
```bash
npx cap sync
```

#### 6. فتح Android Studio:
```bash
npx cap open android
```

#### 7. بناء APK في Android Studio:
- Build → Build Bundle(s) / APK(s) → Build APK(s)
- انتظر انتهاء البناء
- ستجد APK في: `android/app/build/outputs/apk/debug/`

---

## 📱 اختبار APK

### على الجهاز:
1. فعل "Developer Options"
2. فعل "USB Debugging"  
3. ثبت APK: `adb install app-debug.apk`

### على المحاكي:
1. افتح Android Studio
2. اختر "AVD Manager"
3. أنشئ محاكي
4. اسحب APK إلى المحاكي

---

## 🛠️ حل المشاكل

### "Command not found":
```bash
npm install -g @capacitor/cli
```

### "Android SDK not found":
- ثبت Android Studio
- افتح SDK Manager
- ثبت Android SDK Platform-Tools

### "Java not found":
```bash
# Ubuntu/Debian
sudo apt install openjdk-11-jdk

# macOS
brew install openjdk@11
```

---

## 📁 الملفات المهمة

- `web-preview.html` - التطبيق الرئيسي
- `manifest.json` - PWA Manifest
- `sw.js` - Service Worker
- `capacitor.config.json` - تكوين Capacitor
- `package-pwa.json` - تبعيات PWA

---

## 🎯 نصائح سريعة

1. **للمبتدئين**: استخدم PWABuilder
2. **للمطورين**: استخدم Capacitor
3. **للاختبار**: استخدم المحاكي أولاً
4. **للنشر**: وقع APK بمفتاح إنتاج

---

## 📞 مساعدة سريعة

إذا واجهت مشاكل:
1. تحقق من الـ logs: `adb logcat`
2. نظف المشروع: `./gradlew clean`
3. أعد البناء: `./gradlew assembleDebug`

---

## ✨ النتيجة

ستحصل على APK بحجم ~5-10 MB يعمل على Android 5.0+ 🚗📱
