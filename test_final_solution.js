/**
 * اختبار الحل النهائي لصفحة جميع المنتجات
 */

console.log('🎯 اختبار الحل النهائي...');

// اختبار صفحة جميع المنتجات الجديدة
function testNewAllProductsPage() {
    console.log('\n🧪 اختبار صفحة جميع المنتجات الجديدة...');
    
    try {
        showAllProducts();
        
        setTimeout(() => {
            const allProductsPage = document.getElementById('allProductsPage');
            console.log('All Products Page:', allProductsPage ? '✅ موجودة' : '❌ غير موجودة');
            
            if (allProductsPage) {
                console.log('Display:', allProductsPage.style.display);
                
                const productsGrid = document.getElementById('allProductsGrid');
                console.log('Products Grid:', productsGrid ? '✅ موجودة' : '❌ غير موجودة');
                
                const loadMoreBtn = document.getElementById('loadMoreAllProductsBtn');
                console.log('Load More Button:', loadMoreBtn ? '✅ موجود' : '❌ غير موجود');
                
                const bottomNav = allProductsPage.querySelector('.bottom-nav');
                console.log('Bottom Navigation:', bottomNav ? '✅ موجودة' : '❌ غير موجودة');
                
                if (productsGrid) {
                    const productCards = productsGrid.querySelectorAll('.category-product-card');
                    console.log(`عدد المنتجات المعروضة: ${productCards.length}`);
                }
            }
        }, 2000);
        
    } catch (error) {
        console.error('❌ خطأ في اختبار صفحة جميع المنتجات:', error);
    }
}

// اختبار صفحة أدوات الصدر للمقارنة
function testChestToolsPageComparison() {
    console.log('\n🔧 اختبار صفحة أدوات الصدر للمقارنة...');
    
    try {
        showCategoryProducts('chest-tools');
        
        setTimeout(() => {
            const specificCategoryPage = document.getElementById('specificCategoryPage');
            console.log('Specific Category Page:', specificCategoryPage ? '✅ موجودة' : '❌ غير موجودة');
            
            if (specificCategoryPage) {
                console.log('Display:', specificCategoryPage.style.display);
                
                const bottomNav = specificCategoryPage.querySelector('.bottom-nav');
                console.log('Bottom Navigation:', bottomNav ? '✅ موجودة' : '❌ غير موجودة');
                
                if (bottomNav) {
                    const navItems = bottomNav.querySelectorAll('.nav-item');
                    console.log(`عدد عناصر التنقل: ${navItems.length}`);
                    
                    const svgIcons = bottomNav.querySelectorAll('svg');
                    console.log(`عدد أيقونات SVG: ${svgIcons.length}`);
                }
            }
        }, 2000);
        
    } catch (error) {
        console.error('❌ خطأ في اختبار صفحة أدوات الصدر:', error);
    }
}

// اختبار زر "تحميل المزيد"
function testLoadMoreButton() {
    console.log('\n📦 اختبار زر "تحميل المزيد"...');
    
    // أولاً افتح صفحة جميع المنتجات
    showAllProducts();
    
    setTimeout(() => {
        const loadMoreBtn = document.getElementById('loadMoreAllProductsBtn');
        if (loadMoreBtn) {
            console.log('✅ زر "تحميل المزيد" موجود');
            console.log('محاولة الضغط على الزر...');
            
            // محاكاة الضغط
            loadMoreAllProducts();
            
            setTimeout(() => {
                console.log('تم اختبار زر "تحميل المزيد"');
                console.log('البيانات الحالية:', {
                    currentPage: window.allProductsCurrentPage,
                    totalProducts: window.allProductsData?.length || 0,
                    metaInfo: window.allProductsMetaInfo ? 'موجودة' : 'غير موجودة'
                });
            }, 3000);
            
        } else {
            console.log('❌ زر "تحميل المزيد" غير موجود');
        }
    }, 2000);
}

// تشغيل جميع الاختبارات
console.log('\n📋 الاختبارات المتاحة:');
console.log('- testNewAllProductsPage() - اختبار صفحة جميع المنتجات الجديدة');
console.log('- testChestToolsPageComparison() - اختبار صفحة أدوات الصدر للمقارنة');
console.log('- testLoadMoreButton() - اختبار زر "تحميل المزيد"');

console.log('\n🎯 المتوقع:');
console.log('✅ صفحة جميع المنتجات تشبه صفحة أدوات الصدر');
console.log('✅ قائمة تنقل موحدة مع SVG icons');
console.log('✅ زر "تحميل المزيد" في النهاية');
console.log('✅ تحميل 30 منتج في كل مرة');
