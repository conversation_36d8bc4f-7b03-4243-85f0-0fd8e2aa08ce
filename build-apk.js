#!/usr/bin/env node

/**
 * Build Script for Dalila Car Auto Parts APK
 * This script creates an APK from the PWA using Capacitor
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 بدء إنشاء APK لتطبيق دليل كار...');

// Configuration
const APP_CONFIG = {
    appId: 'com.dalilakauto.app',
    appName: 'دليل كار',
    webDir: '.',
    bundledWebRuntime: false,
    server: {
        url: 'https://dalilakauto.com',
        cleartext: true
    }
};

// Create Capacitor config
const capacitorConfig = {
    appId: APP_CONFIG.appId,
    appName: APP_CONFIG.appName,
    webDir: APP_CONFIG.webDir,
    bundledWebRuntime: APP_CONFIG.bundledWebRuntime,
    server: APP_CONFIG.server,
    plugins: {
        SplashScreen: {
            launchShowDuration: 2000,
            backgroundColor: '#667eea',
            androidSplashResourceName: 'splash',
            androidScaleType: 'CENTER_CROP',
            showSpinner: false
        },
        StatusBar: {
            style: 'DARK',
            backgroundColor: '#667eea'
        },
        Keyboard: {
            resize: 'body',
            style: 'dark',
            resizeOnFullScreen: true
        },
        App: {
            launchUrl: 'https://dalilakauto.com/web-preview.html'
        }
    },
    android: {
        buildOptions: {
            keystorePath: undefined,
            keystorePassword: undefined,
            keystoreAlias: undefined,
            keystoreAliasPassword: undefined,
            releaseType: 'APK'
        }
    }
};

// Android manifest template
const androidManifest = `<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="${APP_CONFIG.appId}">

    <application
        android:allowBackup="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/AppTheme"
        android:usesCleartextTraffic="true">

        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:launchMode="singleTask"
            android:theme="@style/AppTheme.NoActionBarLaunch">

            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:scheme="https"
                      android:host="dalilakauto.com" />
            </intent-filter>

        </activity>

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${APP_CONFIG.appId}.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths"></meta-data>
        </provider>
    </application>

    <!-- Permissions -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />

</manifest>`;

// Build steps
async function buildAPK() {
    try {
        console.log('📝 إنشاء ملفات التكوين...');
        
        // Create capacitor.config.json
        fs.writeFileSync('capacitor.config.json', JSON.stringify(capacitorConfig, null, 2));
        console.log('✅ تم إنشاء capacitor.config.json');
        
        // Create package.json if it doesn't exist
        if (!fs.existsSync('package.json')) {
            const packageJson = {
                name: 'dalila-car-app',
                version: '1.0.0',
                description: 'دليل كار - تطبيق قطع غيار السيارات',
                main: 'web-preview.html',
                scripts: {
                    'build': 'echo "Build completed"',
                    'android': 'npx cap run android',
                    'ios': 'npx cap run ios'
                },
                dependencies: {
                    '@capacitor/core': '^5.0.0',
                    '@capacitor/cli': '^5.0.0',
                    '@capacitor/android': '^5.0.0'
                },
                author: 'Dalila Car Auto Parts',
                license: 'MIT'
            };
            
            fs.writeFileSync('package.json', JSON.stringify(packageJson, null, 2));
            console.log('✅ تم إنشاء package.json');
        }
        
        console.log('📦 تثبيت Capacitor...');
        try {
            execSync('npm install @capacitor/core @capacitor/cli @capacitor/android', { stdio: 'inherit' });
            console.log('✅ تم تثبيت Capacitor');
        } catch (error) {
            console.log('⚠️ تخطي تثبيت npm - تأكد من تثبيت Capacitor يدوياً');
        }
        
        console.log('🔧 تهيئة مشروع Capacitor...');
        try {
            execSync('npx cap init', { stdio: 'inherit' });
            console.log('✅ تم تهيئة Capacitor');
        } catch (error) {
            console.log('⚠️ Capacitor مهيأ مسبقاً أو حدث خطأ في التهيئة');
        }
        
        console.log('📱 إضافة منصة Android...');
        try {
            execSync('npx cap add android', { stdio: 'inherit' });
            console.log('✅ تم إضافة منصة Android');
        } catch (error) {
            console.log('⚠️ منصة Android موجودة مسبقاً أو حدث خطأ');
        }
        
        console.log('🔄 مزامنة الملفات...');
        try {
            execSync('npx cap sync', { stdio: 'inherit' });
            console.log('✅ تم مزامنة الملفات');
        } catch (error) {
            console.log('⚠️ خطأ في المزامنة - تابع العملية');
        }
        
        console.log('🏗️ بناء APK...');
        try {
            execSync('npx cap build android', { stdio: 'inherit' });
            console.log('✅ تم بناء APK بنجاح!');
        } catch (error) {
            console.log('⚠️ خطأ في بناء APK - قد تحتاج لفتح Android Studio');
        }
        
        console.log('\n🎉 انتهى إنشاء APK!');
        console.log('\n📋 الخطوات التالية:');
        console.log('1. افتح Android Studio');
        console.log('2. افتح مجلد android/');
        console.log('3. اختر Build > Build Bundle(s) / APK(s) > Build APK(s)');
        console.log('4. ستجد APK في android/app/build/outputs/apk/');
        
    } catch (error) {
        console.error('❌ خطأ في إنشاء APK:', error.message);
        console.log('\n💡 نصائح لحل المشاكل:');
        console.log('- تأكد من تثبيت Node.js و npm');
        console.log('- تأكد من تثبيت Android Studio و Android SDK');
        console.log('- تأكد من تثبيت Java Development Kit (JDK)');
        console.log('- قم بتشغيل: npm install -g @capacitor/cli');
    }
}

// Alternative PWA to APK using PWABuilder
function generatePWABuilderConfig() {
    const pwaBuilderConfig = {
        name: 'دليل كار',
        short_name: 'دليل كار',
        description: 'تطبيق دليل كار لبيع قطع غيار السيارات',
        start_url: '/web-preview.html',
        display: 'standalone',
        orientation: 'portrait',
        theme_color: '#667eea',
        background_color: '#ffffff',
        icons: [
            {
                src: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTEyIiBoZWlnaHQ9IjUxMiIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZGVmcz48bGluZWFyR3JhZGllbnQgaWQ9ImEiIHgxPSIwJSIgeTE9IjAlIiB4Mj0iMTAwJSIgeTI9IjEwMCUiPjxzdG9wIG9mZnNldD0iMCUiIHN0b3AtY29sb3I9IiM2NjdlZWEiLz48c3RvcCBvZmZzZXQ9IjEwMCUiIHN0b3AtY29sb3I9IiM3NjRiYTIiLz48L2xpbmVhckdyYWRpZW50PjwvZGVmcz48cmVjdCB3aWR0aD0iNTEyIiBoZWlnaHQ9IjUxMiIgZmlsbD0idXJsKCNhKSIgcng9IjY0Ii8+PHRleHQgeD0iMjU2IiB5PSIyODAiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxMjgiIGZvbnQtd2VpZ2h0PSJib2xkIiBmaWxsPSIjZmZmIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj7Yr9mE2YrZhDwvdGV4dD48dGV4dCB4PSIyNTYiIHk9IjM2MCIgZm9udC1mYW1pbHk9IkFyaWFsLCBzYW5zLXNlcmlmIiBmb250LXNpemU9IjQ4IiBmaWxsPSIjZmZmIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj7ZgtmC2Lkg2LrZitin2LE8L3RleHQ+PC9zdmc+',
                sizes: '512x512',
                type: 'image/svg+xml'
            }
        ]
    };
    
    fs.writeFileSync('pwa-builder-config.json', JSON.stringify(pwaBuilderConfig, null, 2));
    console.log('✅ تم إنشاء ملف تكوين PWABuilder');
    console.log('🌐 يمكنك استخدام https://www.pwabuilder.com لإنشاء APK');
}

// Run the build process
console.log('🔧 اختر طريقة إنشاء APK:');
console.log('1. Capacitor (يتطلب Android Studio)');
console.log('2. PWABuilder (أسهل للمبتدئين)');

// For now, run both
buildAPK();
generatePWABuilderConfig();

console.log('\n📱 تم إنشاء ملفات APK بنجاح!');
console.log('📁 الملفات المنشأة:');
console.log('- capacitor.config.json');
console.log('- package.json');
console.log('- pwa-builder-config.json');
console.log('- manifest.json');
console.log('- sw.js');

module.exports = { buildAPK, generatePWABuilderConfig };
