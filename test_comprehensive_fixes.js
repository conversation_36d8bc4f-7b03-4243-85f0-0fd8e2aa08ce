/**
 * 🧪 اختبار شامل لجميع الإصلاحات المطبقة
 * Comprehensive Test for All Applied Fixes
 */

const https = require('https');
const BASE_URL = 'https://dalilakauto.com/api/v1';

// دالة مساعدة محسنة للطلبات
function makeRequest(url, options = {}) {
    return new Promise((resolve, reject) => {
        const req = https.request(url, {
            method: options.method || 'GET',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'User-Agent': 'DalilaApp/1.0 (ComprehensiveTest)',
                ...options.headers
            },
            timeout: 20000
        }, (res) => {
            let data = '';
            
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                try {
                    const parsedData = JSON.parse(data);
                    resolve({
                        status: res.statusCode,
                        data: parsedData,
                        headers: res.headers,
                        success: res.statusCode >= 200 && res.statusCode < 300
                    });
                } catch (error) {
                    resolve({
                        status: res.statusCode,
                        data: data,
                        headers: res.headers,
                        success: res.statusCode >= 200 && res.statusCode < 300
                    });
                }
            });
        });
        
        req.on('error', (error) => {
            reject(error);
        });
        
        req.on('timeout', () => {
            req.destroy();
            reject(new Error('Request timeout'));
        });
        
        if (options.body) {
            req.write(JSON.stringify(options.body));
        }
        
        req.end();
    });
}

// اختبار شامل لجميع APIs
async function testAllAPIs() {
    console.log('\n🧪 اختبار شامل لجميع APIs...');
    console.log('='.repeat(60));

    const allAPIs = [
        {
            name: 'Products API (صفحة 1)',
            endpoint: '/ecommerce/products?page=1&per_page=10',
            critical: true
        },
        {
            name: 'Products API (صفحة 2)',
            endpoint: '/ecommerce/products?page=2&per_page=10',
            critical: false
        },
        {
            name: 'Categories API',
            endpoint: '/ecommerce/product-categories',
            critical: true
        },
        {
            name: 'Brands API',
            endpoint: '/ecommerce/brands',
            critical: true
        },
        {
            name: 'Vehicle Root Categories',
            endpoint: '/vehicle-parts/root-categories',
            critical: true
        },
        {
            name: 'Vehicle Makes',
            endpoint: '/vehicle-parts/makes',
            critical: true
        },
        {
            name: 'Countries API (المشكلة)',
            endpoint: '/location/countries',
            critical: false,
            expectedToFail: true
        },
        {
            name: 'Blog Posts',
            endpoint: '/posts?page=1&per_page=5',
            critical: false
        },
        {
            name: 'Simple Sliders',
            endpoint: '/simple-sliders',
            critical: false
        },
        {
            name: 'Flash Sales',
            endpoint: '/ecommerce/flash-sales',
            critical: false
        }
    ];

    const results = {
        total: allAPIs.length,
        success: 0,
        failed: 0,
        critical_failed: 0,
        details: []
    };

    for (const api of allAPIs) {
        try {
            console.log(`\n📋 اختبار: ${api.name}`);
            const startTime = Date.now();
            
            const response = await makeRequest(`${BASE_URL}${api.endpoint}`);
            const duration = Date.now() - startTime;
            
            if (response.success) {
                console.log(`✅ نجح - ${duration}ms - Status: ${response.status}`);
                
                // تحليل البيانات
                let dataCount = 0;
                if (response.data) {
                    if (response.data.data && Array.isArray(response.data.data)) {
                        dataCount = response.data.data.length;
                    } else if (Array.isArray(response.data)) {
                        dataCount = response.data.length;
                    }
                    console.log(`📊 البيانات: ${dataCount} عنصر`);
                }
                
                results.success++;
                results.details.push({
                    name: api.name,
                    status: 'success',
                    duration,
                    dataCount
                });
                
            } else {
                if (api.expectedToFail) {
                    console.log(`⚠️ فشل كما هو متوقع - Status: ${response.status}`);
                    results.details.push({
                        name: api.name,
                        status: 'expected_fail',
                        error: response.status
                    });
                } else {
                    console.log(`❌ فشل - Status: ${response.status}`);
                    results.failed++;
                    if (api.critical) results.critical_failed++;
                    
                    results.details.push({
                        name: api.name,
                        status: 'failed',
                        error: response.status,
                        critical: api.critical
                    });
                }
            }
            
        } catch (error) {
            console.log(`❌ خطأ - ${error.message}`);
            results.failed++;
            if (api.critical) results.critical_failed++;
            
            results.details.push({
                name: api.name,
                status: 'error',
                error: error.message,
                critical: api.critical
            });
        }
        
        // انتظار قصير بين الطلبات
        await new Promise(resolve => setTimeout(resolve, 500));
    }

    return results;
}

// اختبار الأداء
async function testPerformance() {
    console.log('\n⚡ اختبار الأداء...');
    console.log('='.repeat(60));

    const performanceTests = [
        {
            name: 'تحميل المنتجات السريع',
            endpoint: '/ecommerce/products?page=1&per_page=5',
            maxTime: 2000
        },
        {
            name: 'تحميل الفئات',
            endpoint: '/ecommerce/product-categories',
            maxTime: 3000
        },
        {
            name: 'تحميل العلامات التجارية',
            endpoint: '/ecommerce/brands',
            maxTime: 2000
        }
    ];

    const performanceResults = [];

    for (const test of performanceTests) {
        try {
            console.log(`\n⏱️ اختبار: ${test.name}`);
            
            const startTime = Date.now();
            const response = await makeRequest(`${BASE_URL}${test.endpoint}`);
            const duration = Date.now() - startTime;
            
            if (response.success) {
                if (duration <= test.maxTime) {
                    console.log(`✅ سريع - ${duration}ms (الحد الأقصى: ${test.maxTime}ms)`);
                    performanceResults.push({ name: test.name, status: 'fast', duration });
                } else {
                    console.log(`⚠️ بطيء - ${duration}ms (الحد الأقصى: ${test.maxTime}ms)`);
                    performanceResults.push({ name: test.name, status: 'slow', duration });
                }
            } else {
                console.log(`❌ فشل - Status: ${response.status}`);
                performanceResults.push({ name: test.name, status: 'failed' });
            }
            
        } catch (error) {
            console.log(`❌ خطأ - ${error.message}`);
            performanceResults.push({ name: test.name, status: 'error' });
        }
    }

    return performanceResults;
}

// اختبار التحميل المتتالي (Stress Test)
async function testStressLoad() {
    console.log('\n💪 اختبار التحميل المتتالي...');
    console.log('='.repeat(60));

    const endpoint = '/ecommerce/products?page=1&per_page=10';
    const requestCount = 5;
    const results = [];

    console.log(`📊 إرسال ${requestCount} طلبات متتالية...`);

    for (let i = 1; i <= requestCount; i++) {
        try {
            const startTime = Date.now();
            const response = await makeRequest(`${BASE_URL}${endpoint}`);
            const duration = Date.now() - startTime;
            
            if (response.success) {
                console.log(`✅ طلب ${i}: نجح في ${duration}ms`);
                results.push({ request: i, status: 'success', duration });
            } else {
                console.log(`❌ طلب ${i}: فشل - Status: ${response.status}`);
                results.push({ request: i, status: 'failed', error: response.status });
            }
        } catch (error) {
            console.log(`❌ طلب ${i}: خطأ - ${error.message}`);
            results.push({ request: i, status: 'error', error: error.message });
        }
        
        // انتظار قصير بين الطلبات
        await new Promise(resolve => setTimeout(resolve, 200));
    }

    const successCount = results.filter(r => r.status === 'success').length;
    const avgDuration = results
        .filter(r => r.status === 'success')
        .reduce((sum, r) => sum + r.duration, 0) / successCount;

    console.log(`📊 النتائج: ${successCount}/${requestCount} نجح`);
    if (successCount > 0) {
        console.log(`⏱️ متوسط الوقت: ${avgDuration.toFixed(0)}ms`);
    }

    return { successCount, totalCount: requestCount, avgDuration };
}

// تشغيل جميع الاختبارات
async function runComprehensiveTests() {
    console.log('🚀 بدء الاختبار الشامل لجميع الإصلاحات');
    console.log('⏰ ' + new Date().toLocaleString('ar-EG'));
    console.log('='.repeat(80));

    // اختبار الاتصال الأساسي
    try {
        const basicResponse = await makeRequest('https://dalilakauto.com');
        if (!basicResponse.success) {
            console.log('❌ فشل في الاتصال الأساسي. توقف الاختبار.');
            return;
        }
        console.log('✅ الاتصال الأساسي يعمل');
    } catch (error) {
        console.log('❌ فشل في الاتصال الأساسي:', error.message);
        return;
    }

    // اختبار جميع APIs
    const apiResults = await testAllAPIs();
    
    // اختبار الأداء
    const performanceResults = await testPerformance();
    
    // اختبار التحميل المتتالي
    const stressResults = await testStressLoad();

    // تلخيص شامل للنتائج
    console.log('\n' + '='.repeat(80));
    console.log('📊 التقرير النهائي الشامل:');
    console.log('='.repeat(80));

    // نتائج APIs
    console.log('\n🔌 نتائج APIs:');
    console.log(`✅ نجح: ${apiResults.success}/${apiResults.total}`);
    console.log(`❌ فشل: ${apiResults.failed}/${apiResults.total}`);
    console.log(`🚨 فشل حرج: ${apiResults.critical_failed}`);
    
    const apiSuccessRate = (apiResults.success / apiResults.total * 100).toFixed(1);
    console.log(`📈 معدل نجاح APIs: ${apiSuccessRate}%`);

    // نتائج الأداء
    console.log('\n⚡ نتائج الأداء:');
    const fastTests = performanceResults.filter(r => r.status === 'fast').length;
    const slowTests = performanceResults.filter(r => r.status === 'slow').length;
    console.log(`🚀 سريع: ${fastTests}`);
    console.log(`🐌 بطيء: ${slowTests}`);

    // نتائج التحميل المتتالي
    console.log('\n💪 نتائج التحميل المتتالي:');
    console.log(`✅ نجح: ${stressResults.successCount}/${stressResults.totalCount}`);
    if (stressResults.avgDuration) {
        console.log(`⏱️ متوسط الوقت: ${stressResults.avgDuration.toFixed(0)}ms`);
    }

    // التقييم العام
    console.log('\n🎯 التقييم العام:');
    if (apiResults.critical_failed === 0 && apiSuccessRate >= 90) {
        console.log('🎉 ممتاز! جميع الإصلاحات تعمل بشكل مثالي');
    } else if (apiResults.critical_failed === 0 && apiSuccessRate >= 75) {
        console.log('👍 جيد! معظم الإصلاحات تعمل بشكل صحيح');
    } else if (apiResults.critical_failed > 0) {
        console.log('🚨 تحذير! هناك مشاكل حرجة تحتاج إصلاح فوري');
    } else {
        console.log('⚠️ يحتاج إلى مزيد من الإصلاحات');
    }

    console.log('\n✅ انتهى الاختبار الشامل');
    console.log('📝 يمكن الآن المتابعة مع الاختبارات اليدوية');
}

// تشغيل الاختبارات
runComprehensiveTests().catch(console.error);
