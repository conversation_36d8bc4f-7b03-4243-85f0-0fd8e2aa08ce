/**
 * اختبار وظيفة الاسكرول في صفحة المدونة
 */

console.log('📜 اختبار وظيفة الاسكرول في صفحة المدونة...');

// فحص CSS الاسكرول
function checkScrollCSS() {
    console.log('\n🎨 فحص CSS الاسكرول...');
    
    // فحص صفحة المدونة الرئيسية
    const blogPage = document.getElementById('blogPage');
    if (blogPage) {
        const style = window.getComputedStyle(blogPage);
        console.log('✅ صفحة المدونة الرئيسية:');
        console.log('- Overflow-Y:', style.overflowY);
        console.log('- Height:', style.height);
        console.log('- Scroll Behavior:', style.scrollBehavior);
        console.log('- Classes:', blogPage.className);
    } else {
        console.log('❌ صفحة المدونة الرئيسية غير موجودة');
    }
    
    // فحص شبكة المقالات
    const blogGrid = document.getElementById('blogPostsList');
    if (blogGrid) {
        const style = window.getComputedStyle(blogGrid);
        console.log('\n✅ شبكة المقالات:');
        console.log('- Overflow-Y:', style.overflowY);
        console.log('- Max-Height:', style.maxHeight);
        console.log('- Padding-Bottom:', style.paddingBottom);
    } else {
        console.log('\n❌ شبكة المقالات غير موجودة');
    }
}

// اختبار الاسكرول في صفحة المدونة الرئيسية
function testMainBlogPageScroll() {
    console.log('\n📋 اختبار اسكرول صفحة المدونة الرئيسية...');
    
    try {
        // فتح صفحة المدونة
        showPage('blog');
        
        setTimeout(() => {
            const blogPage = document.getElementById('blogPage');
            if (blogPage && blogPage.style.display !== 'none') {
                console.log('✅ صفحة المدونة مفتوحة');
                
                // فحص قابلية الاسكرول
                const isScrollable = blogPage.scrollHeight > blogPage.clientHeight;
                console.log('قابلية الاسكرول:', isScrollable ? '✅ نعم' : '❌ لا');
                console.log('ارتفاع المحتوى:', blogPage.scrollHeight);
                console.log('ارتفاع العرض:', blogPage.clientHeight);
                
                // اختبار الاسكرول
                if (isScrollable) {
                    console.log('🔄 اختبار الاسكرول...');
                    
                    // اسكرول لأسفل
                    blogPage.scrollTo({ top: 100, behavior: 'smooth' });
                    
                    setTimeout(() => {
                        console.log('موضع الاسكرول الحالي:', blogPage.scrollTop);
                        
                        // اسكرول للأعلى
                        blogPage.scrollTo({ top: 0, behavior: 'smooth' });
                        
                        setTimeout(() => {
                            console.log('✅ اختبار الاسكرول مكتمل');
                        }, 500);
                    }, 500);
                } else {
                    console.log('ℹ️ المحتوى لا يحتاج لاسكرول');
                }
                
            } else {
                console.log('❌ صفحة المدونة غير مفتوحة');
            }
        }, 1000);
        
    } catch (error) {
        console.error('❌ خطأ في اختبار اسكرول صفحة المدونة:', error);
    }
}

// اختبار الاسكرول في صفحة المقال
function testBlogPostScroll() {
    console.log('\n📄 اختبار اسكرول صفحة المقال...');
    
    // إنشاء مقال طويل للاختبار
    const longContent = `
        <h2>مقال طويل للاختبار</h2>
        ${Array(20).fill().map((_, i) => `
            <p>هذه فقرة رقم ${i + 1} من المقال الطويل. تحتوي على نص كافي لاختبار الاسكرول في صفحة المقال. 
            يجب أن يكون المحتوى طويلاً بما فيه الكفاية لتجاوز ارتفاع الشاشة وإجبار المستخدم على التمرير لقراءة المحتوى كاملاً.</p>
        `).join('')}
    `;
    
    const testPost = {
        slug: 'test-long-post',
        name: 'مقال طويل لاختبار الاسكرول',
        description: 'مقال تجريبي طويل لاختبار وظيفة الاسكرول',
        content: longContent,
        image: 'https://via.placeholder.com/400x250',
        created_at: new Date().toISOString(),
        categories: [{ name: 'اختبار' }]
    };
    
    try {
        // إنشاء صفحة المقال
        const blogPostHTML = `
            <div class="page blog-post-page" id="blogPostPageTest" style="display: block;">
                <div class="page-header">
                    <button class="back-btn" onclick="closeBlogPostTest()">
                        <i class="fas fa-arrow-right"></i>
                    </button>
                    <h2>اختبار الاسكرول</h2>
                </div>
                <div class="blog-post-content">
                    <div class="blog-post-header">
                        <img src="${testPost.image}" alt="${testPost.name}" class="blog-post-main-image">
                        <h1 class="blog-post-main-title">${testPost.name}</h1>
                    </div>
                    <div class="blog-post-body">
                        ${testPost.content}
                    </div>
                </div>
            </div>
        `;
        
        // إضافة للحاوية
        const phoneContainer = document.querySelector('.phone-container');
        if (phoneContainer) {
            // إخفاء الصفحات الأخرى
            const pages = document.querySelectorAll('.page');
            pages.forEach(page => page.style.display = 'none');
            
            phoneContainer.insertAdjacentHTML('beforeend', blogPostHTML);
            
            setTimeout(() => {
                const blogPostPage = document.getElementById('blogPostPageTest');
                if (blogPostPage) {
                    console.log('✅ صفحة المقال التجريبية تم إنشاؤها');
                    
                    // فحص قابلية الاسكرول
                    const isScrollable = blogPostPage.scrollHeight > blogPostPage.clientHeight;
                    console.log('قابلية الاسكرول:', isScrollable ? '✅ نعم' : '❌ لا');
                    console.log('ارتفاع المحتوى:', blogPostPage.scrollHeight);
                    console.log('ارتفاع العرض:', blogPostPage.clientHeight);
                    
                    // اختبار الاسكرول
                    if (isScrollable) {
                        console.log('🔄 اختبار اسكرول المقال...');
                        
                        // اسكرول لأسفل
                        blogPostPage.scrollTo({ top: 200, behavior: 'smooth' });
                        
                        setTimeout(() => {
                            console.log('موضع الاسكرول:', blogPostPage.scrollTop);
                            
                            // اسكرول للنهاية
                            blogPostPage.scrollTo({ 
                                top: blogPostPage.scrollHeight, 
                                behavior: 'smooth' 
                            });
                            
                            setTimeout(() => {
                                console.log('اسكرول للنهاية:', blogPostPage.scrollTop);
                                
                                // العودة للأعلى
                                blogPostPage.scrollTo({ top: 0, behavior: 'smooth' });
                                
                                setTimeout(() => {
                                    console.log('✅ اختبار اسكرول المقال مكتمل');
                                    
                                    // تنظيف الاختبار
                                    blogPostPage.remove();
                                    console.log('🧹 تم تنظيف صفحة الاختبار');
                                }, 1000);
                            }, 1000);
                        }, 1000);
                    } else {
                        console.log('ℹ️ المحتوى لا يحتاج لاسكرول');
                        blogPostPage.remove();
                    }
                    
                } else {
                    console.log('❌ فشل في إنشاء صفحة المقال التجريبية');
                }
            }, 500);
            
        } else {
            console.log('❌ حاوية الهاتف غير موجودة');
        }
        
    } catch (error) {
        console.error('❌ خطأ في اختبار اسكرول المقال:', error);
    }
}

// وظيفة إغلاق اختبار المقال
function closeBlogPostTest() {
    const testPage = document.getElementById('blogPostPageTest');
    if (testPage) {
        testPage.remove();
        console.log('🧹 تم إغلاق اختبار المقال');
    }
}

// اختبار شامل للاسكرول
function runAllScrollTests() {
    console.log('🚀 بدء جميع اختبارات الاسكرول...');
    
    checkScrollCSS();
    testMainBlogPageScroll();
    
    // اختبار صفحة المقال بعد فترة
    setTimeout(() => {
        testBlogPostScroll();
    }, 4000);
    
    console.log('\n✅ انتهت جميع اختبارات الاسكرول');
}

// إضافة وظيفة للنافذة العامة
window.closeBlogPostTest = closeBlogPostTest;

// تشغيل الاختبارات
runAllScrollTests();

console.log('\n📋 الاختبارات المتاحة:');
console.log('- checkScrollCSS() - فحص CSS الاسكرول');
console.log('- testMainBlogPageScroll() - اختبار اسكرول صفحة المدونة الرئيسية');
console.log('- testBlogPostScroll() - اختبار اسكرول صفحة المقال');
console.log('- runAllScrollTests() - تشغيل جميع الاختبارات');

console.log('\n🎯 المتوقع بعد الإصلاح:');
console.log('✅ صفحة المدونة الرئيسية تدعم الاسكرول');
console.log('✅ صفحة المقال تدعم الاسكرول');
console.log('✅ شريط اسكرول مخصص وجذاب');
console.log('✅ اسكرول سلس ومريح للمستخدم');
